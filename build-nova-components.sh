#!/bin/bash

# Directory containing Nova components
COMPONENTS_DIR="nova-components"

# Arrays to track statuses
success_components=()
failed_components=()

# Check if the directory exists
if [ ! -d "$COMPONENTS_DIR" ]; then
  echo "❌ Directory $COMPONENTS_DIR not found."
  exit 1
fi

# Change into the components directory
cd "$COMPONENTS_DIR"

# Loop through each subdirectory
for component in */ ; do
  echo "🔧 Building component: $component"
  cd "$component"

  # Check if package.json exists
  if [ -f "package.json" ]; then
    # Install dependencies if node_modules doesn't exist
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies for $component"
    failed_components+=("${component%/}")
    cd ..
    continue
    fi

    # Run the build
    echo "🏗 Running npm run dev..."
    npm run prod
    if [ $? -eq 0 ]; then
      echo "✅ Build succeeded for $component"
      success_components+=("${component%/}")
    else
      echo "❌ Build failed for $component"
      failed_components+=("${component%/}")
    fi
  else
    echo "⚠️ Skipping $component — package.json not found"
    failed_components+=("${component%/}")
  fi

  # Return to the parent directory
  cd ..
done

# Final summary
echo ""
echo "======================"
echo "📊 Build Summary:"
echo "======================"

if [ ${#success_components[@]} -gt 0 ]; then
  echo "✅ Successful builds:"
  for comp in "${success_components[@]}"; do
    echo "  - $comp"
  done
else
  echo "❌ No successful builds."
fi

if [ ${#failed_components[@]} -gt 0 ]; then
  echo ""
  echo "❌ Failed builds:"
  for comp in "${failed_components[@]}"; do
    echo "  - $comp"
  done
else
  echo ""
  echo "🎉 No failed builds!"
fi

echo "======================"
