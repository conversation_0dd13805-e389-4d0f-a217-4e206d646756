<?php

namespace Database\Factories;

use App\Personalize;
use Illuminate\Database\Eloquent\Factories\Factory;

class PersonalizeFactory extends Factory
{
    protected $model = Personalize::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name,
            'duration' => $this->faker->numberBetween(1, 4),
            'options' => [],
        ];
    }
}
