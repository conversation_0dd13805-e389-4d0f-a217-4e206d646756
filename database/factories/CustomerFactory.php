<?php

namespace Database\Factories;

use App\Customer;
use Illuminate\Database\Eloquent\Factories\Factory;

class CustomerFactory extends Factory
{
    protected $model = Customer::class;

    public function definition()
    {
        return [
            'name' => $this->faker->name,
            'password' => bcrypt($this->faker->name),
            'email' => $this->faker->unique()->safeEmail,
            'phone' => $this->faker->numberBetween(7182000000, 7189999999),
            'tax_exempt' => $this->faker->boolean(85),
        ];
    }
}
