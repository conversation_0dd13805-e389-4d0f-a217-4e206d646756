<?php

namespace Database\Factories;

use App\Product;
use App\ProductType;
use App\Vendor;
use App\Personalize;
use App\Category;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class ProductFactory extends Factory
{
    protected $model = Product::class;

    public function definition()
    {
        $hebrew = \Faker\Factory::create('he_IL');
        $hebrew_title = '';
        for ($i = 0; $i < $this->faker->numberBetween(4, 8); $i++) {
            $hebrew_title .= $hebrew->firstNameMale . ' ';
        }

        $title = $this->faker->sentence;

        // Create a ProductType instance
        $productType = ProductType::factory()->create();

        // Generate meta data
        $meta = collect(json_decode($productType->fields, true))
            ->mapWithKeys(function ($key) {
                return [$key['name'] => $this->faker->domainWord];
            })
            ->toArray();

        return [
            'title' => $title,
            'store_title' => $title,
            'heb_title' => $hebrew_title,
            'slug' => Str::slug($title),
            'description' => $this->faker->paragraph(10),
            'short_desc' => $this->faker->paragraph(2),
            'publish' => $this->faker->dateTimeThisYear(),
            'vendor_id' => Vendor::factory()->create()->id,
            'product_type_id' => $productType->id,
            'meta' => $meta,
            'list_price' => $this->faker->randomFloat(2, 0, 199),
            'store_price' => $this->faker->randomFloat(2, 0, 199),
            'online_price' => $this->faker->randomFloat(2, 0, 199),
            'sale_price' => $this->faker->randomFloat(2, 0, 199),
            'barcode' => $this->faker->numberBetween(100000000000, 999999999999),
            'sku' => substr($this->faker->hexColor, 1),
            'vendor_sku' => substr($this->faker->hexColor, 1),
            'width' => $this->faker->randomFloat(2, 0, 199),
            'height' => $this->faker->randomFloat(2, 0, 199),
            'length' => $this->faker->randomFloat(2, 0, 199),
            'website_quantity' => $this->faker->numberBetween(0, 10),
            'store_quantity' => $this->faker->numberBetween(0, 10),
            'track_inventory' => $this->faker->boolean(),
            'personalizes_id' => $this->faker->boolean(15) ? Personalize::factory()->create()->id : null,
            'visibility' => $this->faker->boolean(85),
            'release_date' => $this->faker->boolean(15) ? $this->faker->dateTimeThisDecade() : null,
            'exclude_free_shipping' => $this->faker->boolean(5),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function (Product $product) {
            // Add categories to product
            $product->categories()->saveMany(Category::factory()->count($this->faker->numberBetween(1, 3))->make());

            // Update search field (assuming this method exists)
            $product->update([
                'search' => $product->getModelSwiftypeTransformed(),
            ]);
        });
    }
}

