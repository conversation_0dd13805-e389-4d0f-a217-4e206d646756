<?php

namespace Database\Factories;

use App\Payment;
use Illuminate\Database\Eloquent\Factories\Factory;

class PaymentFactory extends Factory
{
    protected $model = Payment::class;

    public function definition()
    {
        return [
            'name' => $this->faker->name,
            'last_four' => rand(1000, 9999),
            'token' => rand(1000, 9999),
            'securityCode' => rand(1000, 9999),
            'expDate' => now()->addYear(),
            'address_line_1' => $this->faker->buildingNumber . ' ' . $this->faker->streetName,
            'address_line_2' => $this->faker->secondaryAddress,
            'city' => $this->faker->city,
            'state' => $this->faker->stateAbbr,
            'postal_code' => $this->faker->postcode,
            'country' => $this->faker->country,
        ];
    }
}
