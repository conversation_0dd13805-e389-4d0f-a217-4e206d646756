<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Admin>
 */
class AdminFactory extends Factory
{

    public function definition(): array
    {
        return [
            'name' => fake()->name,
            'role' => 'Super Admin',
            'password' => bcrypt('password'),
        ];
    }
}
