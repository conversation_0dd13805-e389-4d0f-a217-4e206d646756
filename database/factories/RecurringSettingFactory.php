<?php

namespace Database\Factories;

use App\RecurringSetting;
use Illuminate\Database\Eloquent\Factories\Factory;

class RecurringSettingFactory extends Factory
{

    protected $model = RecurringSetting::class;

    public function definition()
    {
        return [
            'name' => $this->faker->name,
            'amount' => $this->faker->numberBetween(1, 3),
            'cycle' => $this->faker->randomElement(['week', 'month', 'year']),
        ];
    }
}
