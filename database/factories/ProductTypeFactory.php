<?php

namespace Database\Factories;

use App\ProductType;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductTypeFactory extends Factory
{

    protected $model = ProductType::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->domainWord,
            'creator_type' => array_rand(\App\Creator::$options),
            'fields' => json_encode([
                ['name' => $this->faker->domainWord, 'type' => 'text'],
                ['name' => $this->faker->domainWord, 'type' => 'integer'],
            ]),
        ];
    }
}
