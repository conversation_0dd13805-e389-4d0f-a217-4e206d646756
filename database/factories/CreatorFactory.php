<?php

namespace Database\Factories;

use App\Creator;
use Illuminate\Database\Eloquent\Factories\Factory;

class CreatorFactory extends Factory
{
    protected $model = Creator::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name,
            'type' => array_rand(Creator::$options),
            'description' => $this->faker->paragraph(10),
        ];
    }
}
