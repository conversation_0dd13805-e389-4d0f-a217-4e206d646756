<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRecurringsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('recurrings', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('quantity');
            $table->unsignedInteger('amount');
            $table->morphs('model');
            $table->string('cycle');
            $table->date('next_date');
            $table->unsignedInteger('customer_id');
            $table->unsignedInteger('address_id');
            $table->unsignedInteger('payment_id');
            $table->json('shipping')->nullable();
            $table->json('payments')->nullable();
            $table->string('status');
            $table->json('meta')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('recurrings');
    }
}
