<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCreditCardPaymentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('credit_card_payments', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->string('card_id');
            $table->string('token');
            $table->string('status');
            $table->string('security_code');
            $table->json('payer_info');
            $table->decimal('amount');
            $table->json('charges');
            $table->json('authorized');
            $table->json('captures');
            $table->json('refunds');
            $table->json('errors');
            $table->string('refnum');

            $table->timestamps();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('credit_card_payments');
    }
}
