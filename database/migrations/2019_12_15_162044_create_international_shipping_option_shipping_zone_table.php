<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInternationalShippingOptionShippingZoneTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('international_shipping_option_shipping_zone', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('shipping_option_id')->unsigned();
            $table->bigInteger('international_shipping_zone_id')->unsigned();
            $table->double('base_rate')->nullable();
            $table->double('free_shipping_above')->nullable();
            $table->boolean('active')->default(0);
            $table->string('carrier')->nullable();
            $table->json('carrier_info')->nullable();
            $table->string('service')->nullable();
            $table->json('service_info')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('international_shipping_option_shipping_zone');
    }
}
