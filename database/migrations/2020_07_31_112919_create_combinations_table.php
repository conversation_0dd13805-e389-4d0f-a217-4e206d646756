<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCombinationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('combinations', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('type_id');
            $table->unsignedBigInteger('group_id');

            $table->json('upcoming_products')->nullable();
            $table->date('upcoming_date')->nullable();
            $table->text('upcoming_string')->nullable();
            $table->unsignedBigInteger('upcoming_filter_item_id')->nullable();

            $table->json('next_products')->nullable();
            $table->date('next_date')->nullable();
            $table->text('next_string')->nullable();
            $table->unsignedBigInteger('next_filter_item_id')->nullable();

            $table->string('filter_name')->nullable();
            $table->float('from_price')->nullable();
            $table->float('to_price')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('combinations');
    }
}
