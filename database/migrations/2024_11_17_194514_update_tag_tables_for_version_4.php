<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Удаляем старые внешние ключи и столбцы, если они существуют
        Schema::table('taggables', function (Blueprint $table) {
            // Удаляем старый внешний ключ
            $table->dropForeign(['tag_id']);

            // Удаляем старые столбцы, если они есть
            if (Schema::hasColumn('taggables', 'taggable_type')) {
                $table->dropColumn('taggable_type');
            }
            if (Schema::hasColumn('taggables', 'taggable_id')) {
                $table->dropColumn('taggable_id');
            }
        });

        // Изменяем тип id в таблице tags на unsignedBigInteger
        Schema::table('tags', function (Blueprint $table) {
            $table->unsignedBigInteger('id', true)->change();
        });

        // Изменяем тип tag_id в таблице taggables и добавляем новый внешний ключ
        Schema::table('taggables', function (Blueprint $table) {
            $table->unsignedBigInteger('tag_id')->change();
            $table->foreign('tag_id')->references('id')->on('tags')->cascadeOnDelete();
        });

        // Обновляем структуру taggables для использования morphs и уникального индекса
        Schema::table('taggables', function (Blueprint $table) {
            $table->morphs('taggable'); // Добавляем morphs
            $table->unique(['tag_id', 'taggable_id', 'taggable_type']); // Добавляем уникальный индекс
        });
    }

    public function down(): void
    {
        // Удаляем уникальный индекс и morphs из таблицы taggables
        Schema::table('taggables', function (Blueprint $table) {
            $table->dropUnique(['tag_id', 'taggable_id', 'taggable_type']);
            $table->dropMorphs('taggable');
        });

        // Удаляем внешний ключ из таблицы taggables
        Schema::table('taggables', function (Blueprint $table) {
            $table->dropForeign(['tag_id']);
        });

        // Возвращаем id в таблице tags к unsignedInteger
        Schema::table('tags', function (Blueprint $table) {
            $table->unsignedInteger('id', true)->change();
        });

        // Возвращаем tag_id в таблице taggables к unsignedInteger
        Schema::table('taggables', function (Blueprint $table) {
            $table->unsignedInteger('tag_id')->change();
            $table->foreign('tag_id')->references('id')->on('tags')->cascadeOnDelete();
        });
    }
};
