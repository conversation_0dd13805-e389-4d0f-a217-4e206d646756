<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Outl1ne\MenuBuilder\MenuBuilder;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn(MenuBuilder::getMenuItemsTableName(), 'nestable')) {
            Schema::table(MenuBuilder::getMenuItemsTableName(), function (Blueprint $table) {
                $table->boolean('nestable')->default(1);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropColumns(MenuBuilder::getMenuItemsTableName(), ['nestable']);
    }
};
