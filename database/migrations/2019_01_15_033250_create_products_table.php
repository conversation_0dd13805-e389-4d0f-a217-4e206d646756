<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            $table->increments('id');
            $table->string('title');
            $table->string('slug');
            $table->text('description')->nullable();
            $table->text('short_desc')->nullable();
            $table->string('heb_title')->nullable();
            $table->text('heb_description')->nullable();
            $table->text('heb_short_desc')->nullable();

            $table->float('list_price')->nullable();
            $table->float('store_price')->nullable();
            $table->float('online_price')->nullable();
            $table->float('sale_price')->nullable();
            $table->float('cost_price')->nullable();

            $table->string('barcode')->nullable();
            $table->string('gtni')->nullable();
            $table->string('sku')->nullable();

            $table->string('item_type')->default('physical');

            $table->string('width')->nullable();
            $table->string('height')->nullable();
            $table->string('length')->nullable();
            $table->string('weight')->nullable();
            $table->integer('boxes')->nullable();
            $table->string('origin')->nullable();
            $table->string('system_code')->nullable();

            $table->integer('store_quantity')->nullable();
            $table->integer('website_quantity')->nullable();
            $table->boolean('track_inventory')->nullable()->default(0);

            $table->json('meta')->nullable();
            $table->json('pos_meta')->nullable();
            $table->string('store_title')->nullable();

            $table->unsignedInteger('product_type_id')->nullable()->index();
            $table->unsignedInteger('vendor_id')->nullable()->index();
            $table->unsignedInteger('label_id')->nullable()->index();
            $table->unsignedInteger('personalizes_id')->nullable()->index();
            $table->string('tax_code')->nullable();
            $table->boolean('visibility')->nullable();
            $table->date('publish')->nullable();
            $table->date('expire')->nullable();
            $table->date('release_date')->nullable();
            $table->integer('duration')->nullable();

            $table->json('search')->nullable();
            $table->text('file_format')->nullable();
            $table->integer('max_quantity')->nullable();
            $table->boolean('exclude_free_shipping')->nullable()->default(0);
            $table->boolean('exclude_from_returns')->nullable()->default(0);
            $table->json('notification')->nullable();
            $table->json('var_skus')->nullable();

            $table->unsignedInteger('popularity_score')->nullable();

            $table->string('seo_title')->nullable();
            $table->text('seo_desc')->nullable();

            $table->string('store_vendor')->nullable();
            $table->string('store_category')->nullable();
            $table->string('store_sub_category')->nullable();
            $table->boolean('not_photographed')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('products');
    }
}
