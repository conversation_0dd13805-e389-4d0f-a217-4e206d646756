<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateFulfillmentSchematicsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fulfillment_schematics', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name');
            $table->boolean('delivery');
            $table->double('cutoff');
            $table->boolean('cutoff_is_hourly');
            $table->float('sunday_open')->nullable();
            $table->float('monday_open')->nullable();
            $table->float('tuesday_open')->nullable();
            $table->float('wednesday_open')->nullable();
            $table->float('thursday_open')->nullable();
            $table->float('friday_open')->nullable();
            $table->float('saturday_open')->nullable();
            $table->float('sunday_close')->nullable();
            $table->float('monday_close')->nullable();
            $table->float('tuesday_close')->nullable();
            $table->float('wednesday_close')->nullable();
            $table->float('thursday_close')->nullable();
            $table->float('friday_close')->nullable();
            $table->float('saturday_close')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fulfillment_schematics');
    }
}
