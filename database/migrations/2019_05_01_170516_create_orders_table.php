<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->decimal('sub_total', 8, 2)->default(0)->nullable();
            $table->decimal('tax_amount', 8, 2)->default(0)->nullable();
            $table->decimal('shipping_amount', 8, 2)->default(0)->nullable();
            $table->decimal('grand_total', 8, 2)->default(0)->nullable();

            $table->decimal('discount_amount', 8, 2)->default(0)->nullable();
            $table->json('discount')->nullable();
            $table->unsignedInteger('discount_id')->nullable();

            $table->boolean('subscription')->nullable()->default(0);

            $table->json('products')->nullable();
            $table->json('shipping')->nullable();
            $table->json('payments')->nullable();
            $table->json('meta')->nullable();

            $table->string('status')->nullable();
            $table->string('payment_status')->nullable();

            $table->boolean('guest')->default(0);
            $table->unsignedInteger('customer_id');

            $table->json('product_ids');
            $table->json('reversions')->nullable();
            $table->morphs('payment');

            $table->string('recurring_type');
            $table->unsignedBigInteger('recurring_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders');
    }
}
