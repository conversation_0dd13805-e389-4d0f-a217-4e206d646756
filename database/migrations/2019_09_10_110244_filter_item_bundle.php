<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class FilterItemBundle extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('filter_item_bundle', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->integer('bundle_id')->index()->unsigned()->nullable();
            $table->integer('filter_item_id')->index()->unsigned()->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('filter_item_bundle');
    }
}
