<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateBundlesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bundles', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->string('title');
            $table->string('slug');
            $table->text('description')->nullable();
            $table->text('short_desc')->nullable();
            $table->string('heb_title')->nullable();
            $table->text('heb_description')->nullable();
            $table->text('heb_short_desc')->nullable();
            $table->float('price')->nullable();
            $table->string('barcode')->nullable();
            $table->string('sku')->nullable();
            $table->json('meta')->nullable();
            $table->string('tax_code')->nullable();
            $table->boolean('visibility')->nullable();
            $table->boolean('show_images')->nullable();
            $table->boolean('show_products')->nullable();
            $table->date('publish')->nullable();
            $table->json('search')->nullable();

            $table->unsignedInteger('lists_id')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bundles');
    }
}
