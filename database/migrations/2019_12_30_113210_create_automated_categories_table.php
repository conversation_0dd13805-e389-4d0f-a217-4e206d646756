<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAutomatedCategoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('automated_categories', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable();
            $table->text('description')->nullable();
            $table->string('rule')->nullable();
            $table->string('status')->nullable();
            $table->string('sort_by')->nullable();
            $table->string('contains')->nullable();

            $table->json('categories_and')->nullable();
            $table->json('categories_or')->nullable();
            $table->json('categories_exclude')->nullable();

            $table->json('filters_and')->nullable();
            $table->json('filters_or')->nullable();
            $table->json('filters_exclude')->nullable();

            $table->json('vendors_and')->nullable();
            $table->json('vendors_or')->nullable();
            $table->json('vendors_exclude')->nullable();

            $table->json('creators_and')->nullable();
            $table->json('creators_or')->nullable();
            $table->json('creators_exclude')->nullable();

            $table->json('tags_and')->nullable();
            $table->json('tags_or')->nullable();
            $table->json('tags_exclude')->nullable();

            $table->json('labels_and')->nullable();
            $table->json('labels_or')->nullable();
            $table->json('labels_exclude')->nullable();

            $table->decimal('from_price')->nullable();
            $table->decimal('to_price')->nullable();

            $table->boolean('redirect')->nullable()->default(0);

           
            $table->string('seo_title')->nullable();
            $table->text('seo_desc')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('automated_categories');
    }
}
