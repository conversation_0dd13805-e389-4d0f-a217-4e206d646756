<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateDiscountsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('discounts', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name', 100)->unique()->nullable();
            $table->float('amount', 8, 2)->nullable();
            $table->string('type');
            $table->float('min', 8, 2)->nullable();
            $table->float('max', 8, 2)->nullable();
            $table->dateTime('start')->nullable();
            $table->dateTime('end')->nullable();
            $table->integer('limit')->nullable();
            $table->boolean('limitable');
            $table->boolean('apply_to_gc');
            $table->boolean('apply_to_sale');
            $table->integer('limit_customer')->nullable();
            $table->string('eligibility_type')->nullable();
            $table->string('eligibility_id')->nullable();
            $table->string('free_shipping_location')->nullable();
            $table->string('model_type', 255)->nullable();
            $table->json('model_id');
            $table->boolean('automated');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('discounts');
    }
}
