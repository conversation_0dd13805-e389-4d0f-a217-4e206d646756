<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShippingOptionShippingZoneTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipping_option_shipping_zone', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('shipping_option_id')->unsigned();
            $table->bigInteger('shipping_zone_id')->unsigned();
            $table->double('price_per_pound')->nullable();
            $table->double('base_rate')->nullable();
            $table->double('duration')->nullable();
            $table->boolean('duration_is_hourly')->nullable();
            $table->double('free_shipping_above')->nullable();
            $table->string('type')->nullable();
            $table->boolean('active');
            $table->json('key_values')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipping_option_shipping_zone');
    }
}
