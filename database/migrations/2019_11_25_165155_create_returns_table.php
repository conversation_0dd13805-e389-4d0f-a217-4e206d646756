<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateReturnsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('returns', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->decimal('sub_total', 8, 2)->default(0)->nullable();
            $table->decimal('tax_amount', 8, 2)->default(0)->nullable();
            $table->decimal('shipping_amount', 8, 2)->default(0)->nullable();
            $table->decimal('grand_total', 8, 2)->default(0)->nullable();

            $table->decimal('discount_amount', 8, 2)->default(0)->nullable();
            $table->json('discount')->nullable();
            $table->unsignedInteger('discount_id')->nullable();

            $table->boolean('subscription')->nullable()->default(0);
            
            $table->json('products')->nullable();
            $table->json('shipping')->nullable();
            $table->json('payments')->nullable();
            $table->json('meta')->nullable();
            $table->json('reversions')->nullable();
            
            $table->string('status')->nullable();
            $table->string('shipping_status')->nullable();
            $table->string('payment_status')->nullable();

            $table->boolean('dropoff');

            $table->morphs('payment');
            $table->unsignedInteger('order_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('returns');
    }
}
