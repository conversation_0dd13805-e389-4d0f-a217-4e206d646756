<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateProductTypesTable extends Migration
{
    public function up()
    {
        Schema::create('product_types', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name');
            $table->string('creator_type')->nullable()->index();
            $table->json('fields')->nullable();
            $table->json('variations')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::drop('product_types');
    }
}
