<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateVariationInfosTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('variation_infos', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('product_id')->index();

            $table->float('list_price')->nullable();
            $table->float('store_price')->nullable();
            $table->float('online_price')->nullable();
            $table->float('sale_price')->nullable();
            $table->float('cost_price')->nullable();

            $table->string('barcode')->nullable();
            $table->string('gtni')->nullable();

            $table->string('sku')->nullable();
            $table->string('width')->nullable();
            $table->string('height')->nullable();
            $table->string('length')->nullable();
            $table->string('weight')->nullable();
            $table->integer('boxes')->nullable();
            $table->string('origin')->nullable();
            $table->string('system_code')->nullable();

            $table->integer('store_quantity')->nullable();
            $table->integer('max_quantity')->nullable();
            $table->integer('website_quantity')->nullable();
            $table->boolean('track_inventory')->nullable()->default(0);

            $table->json('pos_meta')->nullable();
            $table->string('store_title')->nullable();

            $table->boolean('visibility')->nullable()->default(1);

            $table->string('item_type')->default('physical');

            $table->json('meta')->nullable();
            $table->string('image')->nullable();
            $table->json('product_json')->nullable();

            $table->string('store_vendor')->nullable();
            $table->string('store_category')->nullable();
            $table->string('store_sub_category')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('variation_infos');
    }
}
