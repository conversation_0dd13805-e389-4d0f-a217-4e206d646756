<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class MakeCreatorsManyToMany extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('creator_product', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('creator_id')->unsigned()->nullable()->index();
            $table->integer('product_id')->unsigned()->nullable()->index();
            $table->timestamps();
        });

        Schema::table('products', function (Blueprint $table) {
            // $table->dropColumn('creator_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('creator_product');

        Schema::table('products', function (Blueprint $table) {
            // $table->integer('creator_id')->unsigned()->nullable();
        });
    }
}
