<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGiftNotesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gift_notes', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('gift_notes_setting_id');
            $table->text('occasionText')->nullable();
            $table->string('textDirection');
            $table->string('sku');
            $table->boolean('downloaded');
            $table->boolean('shipped');
            $table->unsignedInteger('order_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gift_notes');
    }
}
