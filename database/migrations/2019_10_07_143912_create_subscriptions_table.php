<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSubscriptionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('customer_id');
            $table->unsignedBigInteger('subscription_type_id');
            $table->unsignedBigInteger('subscription_group_id');
            $table->unsignedBigInteger('payment_id');
            $table->unsignedBigInteger('address_id');
            $table->json('shipping')->nullable();
            $table->json('payments')->nullable();
            $table->unsignedInteger('quantity');
            $table->string('status');
            $table->json('meta');
            $table->timestamps();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subscriptions');
    }
}
