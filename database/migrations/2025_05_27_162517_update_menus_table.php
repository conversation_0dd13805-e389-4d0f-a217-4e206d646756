<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Outl1ne\MenuBuilder\MenuBuilder;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasColumn(MenuBuilder::getMenusTableName(), 'locale')) {
            Schema::table(MenuBuilder::getMenusTableName(), function (Blueprint $table) {
                $table->dropColumn('locale');
            });
        }

        if (!Schema::hasColumn(MenuBuilder::getMenuItemsTableName(), 'locale')) {
            Schema::table(MenuBuilder::getMenusTableName(), function (Blueprint $table) {
                $table->string('locale')->default('en_US')->after('name');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn(MenuBuilder::getMenusTableName(), 'locale')) {
            Schema::table(MenuBuilder::getMenusTableName(), function (Blueprint $table) {
                $table->dropColumn('locale');
            });
        }

        if (!Schema::hasColumn(MenuBuilder::getMenuItemsTableName(), 'locale')) {
            Schema::table(MenuBuilder::getMenuItemsTableName(), function (Blueprint $table) {
                $table->string('locale')->default('en_US')->after('name');
            });
        }
    }
};
