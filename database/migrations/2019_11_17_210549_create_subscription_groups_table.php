<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSubscriptionGroupsTable extends Migration
{
    public function up()
    {
        Schema::create('subscription_groups', function (Blueprint $table) { 
            $table->bigIncrements('id');
            $table->string('name');
            $table->unsignedInteger('filter_id'); 
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('subscription_groups');
    }
}
