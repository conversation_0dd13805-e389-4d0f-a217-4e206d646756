<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_logs', function (Blueprint $table) {
            $table->id();
            $table->string('method')->nullable();
            $table->string('url')->nullable();
            $table->json('request');
            $table->json('response')->nullable();
            $table->string('provider')->nullable();
            $table->string('tag')->nullable();
            $table->string('unique_identifier')->unique();
            $table->unsignedSmallInteger('status_code')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_logs');
    }
};
