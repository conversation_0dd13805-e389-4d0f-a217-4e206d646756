[{"id": "1", "name": "{\"en\": \"English Books\"}", "slug": "{\"en\": \"english-books\"}", "type": null, "order_column": "1", "created_at": "2019-11-19 15:45:57", "updated_at": "2019-11-19 15:45:57"}, {"id": "2", "name": "{\"en\": \"Kinder V<PERSON>t\"}", "slug": "{\"en\": \"kinder-velt\"}", "type": null, "order_column": "2", "created_at": "2019-11-27 15:32:23", "updated_at": "2019-11-27 15:32:23"}, {"id": "3", "name": "{\"en\": \"cardgame\"}", "slug": "{\"en\": \"cardgame\"}", "type": null, "order_column": "3", "created_at": "2019-11-29 09:59:19", "updated_at": "2019-11-29 09:59:19"}, {"id": "4", "name": "{\"en\": \"speedgame\"}", "slug": "{\"en\": \"speedgame\"}", "type": null, "order_column": "4", "created_at": "2019-11-29 10:47:34", "updated_at": "2019-11-29 10:47:34"}, {"id": "5", "name": "{\"en\": \"puzzle\"}", "slug": "{\"en\": \"puzzle\"}", "type": null, "order_column": "5", "created_at": "2019-12-03 11:38:08", "updated_at": "2019-12-03 11:38:08"}, {"id": "6", "name": "{\"en\": \"playmat\"}", "slug": "{\"en\": \"playmat\"}", "type": null, "order_column": "6", "created_at": "2019-12-04 11:12:52", "updated_at": "2019-12-04 11:12:52"}, {"id": "7", "name": "{\"en\": \"playhouse\"}", "slug": "{\"en\": \"playhouse\"}", "type": null, "order_column": "7", "created_at": "2019-12-04 14:05:08", "updated_at": "2019-12-04 14:05:08"}, {"id": "8", "name": "{\"en\": \"kosher\"}", "slug": "{\"en\": \"kosher\"}", "type": null, "order_column": "8", "created_at": "2019-12-04 14:32:12", "updated_at": "2019-12-04 14:32:12"}, {"id": "9", "name": "{\"en\": \"tic-tac\"}", "slug": "{\"en\": \"tic-tac\"}", "type": null, "order_column": "9", "created_at": "2019-12-04 15:56:47", "updated_at": "2019-12-04 15:56:47"}, {"id": "10", "name": "{\"en\": \"rummy\"}", "slug": "{\"en\": \"rummy\"}", "type": null, "order_column": "10", "created_at": "2019-12-04 16:03:09", "updated_at": "2019-12-04 16:03:09"}, {"id": "11", "name": "{\"en\": \"electric menorah\"}", "slug": "{\"en\": \"electric-menorah\"}", "type": null, "order_column": "11", "created_at": "2019-12-09 13:18:00", "updated_at": "2019-12-09 13:18:00"}, {"id": "12", "name": "{\"en\": \"yair emanuel\"}", "slug": "{\"en\": \"yair-emanuel\"}", "type": null, "order_column": "12", "created_at": "2019-12-09 13:41:49", "updated_at": "2019-12-09 13:41:49"}, {"id": "13", "name": "{\"en\": \"Stands\"}", "slug": "{\"en\": \"stands\"}", "type": null, "order_column": "13", "created_at": "2019-12-09 14:08:58", "updated_at": "2019-12-09 14:08:58"}, {"id": "14", "name": "{\"en\": \"Oil Glasses\"}", "slug": "{\"en\": \"oil-glasses\"}", "type": null, "order_column": "14", "created_at": "2019-12-09 14:11:34", "updated_at": "2019-12-09 14:11:34"}, {"id": "15", "name": "{\"en\": \"Pre-Filled Oil Cups\"}", "slug": "{\"en\": \"pre-filled-oil-cups\"}", "type": null, "order_column": "15", "created_at": "2019-12-09 14:13:12", "updated_at": "2019-12-09 14:13:12"}, {"id": "16", "name": "{\"en\": \"Wicks\"}", "slug": "{\"en\": \"wicks\"}", "type": null, "order_column": "16", "created_at": "2019-12-09 14:16:39", "updated_at": "2019-12-09 14:16:39"}, {"id": "17", "name": "{\"en\": \"Lighters\"}", "slug": "{\"en\": \"lighters\"}", "type": null, "order_column": "17", "created_at": "2019-12-09 14:49:30", "updated_at": "2019-12-09 14:49:30"}, {"id": "18", "name": "{\"en\": \"Candles\"}", "slug": "{\"en\": \"candles\"}", "type": null, "order_column": "18", "created_at": "2019-12-09 14:50:58", "updated_at": "2019-12-09 14:50:58"}, {"id": "19", "name": "{\"en\": \"Trays\"}", "slug": "{\"en\": \"trays\"}", "type": null, "order_column": "19", "created_at": "2019-12-09 14:51:29", "updated_at": "2019-12-09 14:51:29"}, {"id": "20", "name": "{\"en\": \"candle menorah\"}", "slug": "{\"en\": \"candle-menorah\"}", "type": null, "order_column": "20", "created_at": "2019-12-09 15:05:02", "updated_at": "2019-12-09 15:05:02"}, {"id": "21", "name": "{\"en\": \"Oil Jug\"}", "slug": "{\"en\": \"oil-jug\"}", "type": null, "order_column": "21", "created_at": "2019-12-10 16:10:04", "updated_at": "2019-12-10 16:10:04"}, {"id": "22", "name": "{\"en\": \"Oil Menorah\"}", "slug": "{\"en\": \"oil-menorah\"}", "type": null, "order_column": "22", "created_at": "2019-12-11 11:16:46", "updated_at": "2019-12-11 11:16:46"}, {"id": "23", "name": "{\"en\": \"ornamental\"}", "slug": "{\"en\": \"ornamental\"}", "type": null, "order_column": "23", "created_at": "2019-12-11 14:21:49", "updated_at": "2019-12-11 14:21:49"}, {"id": "24", "name": "{\"en\": \"Handpainted\"}", "slug": "{\"en\": \"handpainted\"}", "type": null, "order_column": "24", "created_at": "2019-12-12 14:41:42", "updated_at": "2019-12-12 14:41:42"}, {"id": "25", "name": "{\"en\": \"Menorah Boxes\"}", "slug": "{\"en\": \"menorah-boxes\"}", "type": null, "order_column": "25", "created_at": "2019-12-12 16:46:22", "updated_at": "2019-12-12 16:46:22"}, {"id": "26", "name": "{\"en\": \"simcha'le\"}", "slug": "{\"en\": \"simchale\"}", "type": null, "order_column": "26", "created_at": "2019-12-13 09:47:04", "updated_at": "2019-12-13 09:47:04"}, {"id": "27", "name": "{\"en\": \"segulos\"}", "slug": "{\"en\": \"segulos\"}", "type": null, "order_column": "27", "created_at": "2019-12-17 14:10:54", "updated_at": "2019-12-17 14:10:54"}, {"id": "28", "name": "{\"en\": \"<PERSON><PERSON>\"}", "slug": "{\"en\": \"kappel\"}", "type": null, "order_column": "28", "created_at": "2020-01-08 10:14:51", "updated_at": "2020-01-08 10:14:51"}, {"id": "29", "name": "{\"en\": \"קאפל\"}", "slug": "{\"en\": \"kafl\"}", "type": null, "order_column": "29", "created_at": "2020-01-08 10:14:51", "updated_at": "2020-01-08 10:14:51"}, {"id": "30", "name": "{\"en\": \"קינדער וועלט\"}", "slug": "{\"en\": \"kindr-vvl\"}", "type": null, "order_column": "30", "created_at": "2020-01-10 11:37:02", "updated_at": "2020-01-10 11:37:02"}, {"id": "31", "name": "{\"en\": \"שבט הלוי\"}", "slug": "{\"en\": \"shb-hlvi\"}", "type": null, "order_column": "31", "created_at": "2020-01-13 11:24:55", "updated_at": "2020-01-13 11:24:55"}, {"id": "32", "name": "{\"en\": \"ז<PERSON><PERSON><PERSON><PERSON> מאיר\"}", "slug": "{\"en\": \"zrv-mair\"}", "type": null, "order_column": "32", "created_at": "2020-01-13 11:24:55", "updated_at": "2020-01-13 11:24:55"}, {"id": "33", "name": "{\"en\": \"<PERSON><PERSON>\"}", "slug": "{\"en\": \"ohr-olam\"}", "type": null, "order_column": "33", "created_at": "2020-01-15 09:52:43", "updated_at": "2020-01-15 09:52:43"}, {"id": "34", "name": "{\"en\": \"אור עולם\"}", "slug": "{\"en\": \"avr-vlm\"}", "type": null, "order_column": "34", "created_at": "2020-01-15 09:52:43", "updated_at": "2020-01-15 09:52:43"}, {"id": "35", "name": "{\"en\": \"yaeli\"}", "slug": "{\"en\": \"yaeli\"}", "type": null, "order_column": "35", "created_at": "2020-01-20 10:37:09", "updated_at": "2020-01-20 10:37:09"}, {"id": "36", "name": "{\"en\": \"<PERSON><PERSON><PERSON>\"}", "slug": "{\"en\": \"purim\"}", "type": null, "order_column": "36", "created_at": "2020-01-22 11:20:53", "updated_at": "2020-01-22 11:20:53"}, {"id": "37", "name": "{\"en\": \"Weekly Sale\"}", "slug": "{\"en\": \"weekly-sale\"}", "type": null, "order_column": "37", "created_at": "2020-02-17 15:45:23", "updated_at": "2020-02-17 15:45:23"}, {"id": "38", "name": "{\"en\": \"Subscription\"}", "slug": "{\"en\": \"subscription\"}", "type": null, "order_column": "38", "created_at": "2020-02-20 15:07:24", "updated_at": "2020-02-20 15:07:24"}, {"id": "39", "name": "{\"en\": \"Assorted\"}", "slug": "{\"en\": \"assorted\"}", "type": null, "order_column": "39", "created_at": "2020-03-01 14:17:53", "updated_at": "2020-03-01 14:17:53"}, {"id": "40", "name": "{\"en\": \"משכ<PERSON>\"}", "slug": "{\"en\": \"msh\"}", "type": null, "order_column": "40", "created_at": "2020-03-02 14:35:03", "updated_at": "2020-03-02 14:35:03"}, {"id": "41", "name": "{\"en\": \"Best Seller\"}", "slug": "{\"en\": \"best-seller\"}", "type": null, "order_column": "41", "created_at": "2020-03-15 15:50:16", "updated_at": "2020-03-15 15:50:16"}, {"id": "42", "name": "{\"en\": \"Couch\"}", "slug": "{\"en\": \"couch\"}", "type": null, "order_column": "42", "created_at": "2020-03-26 21:26:54", "updated_at": "2020-03-26 21:26:54"}, {"id": "43", "name": "{\"en\": \"<PERSON><PERSON><PERSON><PERSON>\"}", "slug": "{\"en\": \"avig<PERSON>-miller\"}", "type": null, "order_column": "43", "created_at": "2020-04-20 11:14:06", "updated_at": "2020-04-20 11:14:06"}, {"id": "44", "name": "{\"en\": \"<PERSON><PERSON><PERSON>\"}", "slug": "{\"en\": \"sefiras-haomer-cal\"}", "type": null, "order_column": "44", "created_at": "2020-04-20 12:17:51", "updated_at": "2020-04-20 12:17:51"}, {"id": "45", "name": "{\"en\": \"Sefira items\"}", "slug": "{\"en\": \"sefira-items\"}", "type": null, "order_column": "45", "created_at": "2020-04-20 15:07:56", "updated_at": "2020-04-20 15:07:56"}, {"id": "46", "name": "{\"en\": \"sefira items\"}", "slug": "{\"en\": \"sefira-items\"}", "type": null, "order_column": "46", "created_at": "2020-04-20 15:16:02", "updated_at": "2020-04-20 15:16:02"}, {"id": "47", "name": "{\"en\": \"card game\"}", "slug": "{\"en\": \"card-game\"}", "type": null, "order_column": "47", "created_at": "2020-04-21 13:21:42", "updated_at": "2020-04-21 13:21:42"}, {"id": "48", "name": "{\"en\": \"REB-SHAYALA\"}", "slug": "{\"en\": \"reb-shayala\"}", "type": null, "order_column": "48", "created_at": "2020-04-22 23:19:37", "updated_at": "2020-04-22 23:19:37"}, {"id": "49", "name": "{\"en\": \"reb-shayla\"}", "slug": "{\"en\": \"reb-shayla\"}", "type": null, "order_column": "49", "created_at": "2020-04-22 23:27:40", "updated_at": "2020-04-22 23:27:40"}, {"id": "50", "name": "{\"en\": \"Yom Ha<PERSON>at<PERSON>\"}", "slug": "{\"en\": \"yom-haatzmaut\"}", "type": null, "order_column": "50", "created_at": "2020-04-28 16:27:11", "updated_at": "2020-04-28 16:27:11"}, {"id": "51", "name": "{\"en\": \"<PERSON><PERSON><PERSON><PERSON>\"}", "slug": "{\"en\": \"tikkun-leil-shavuos\"}", "type": null, "order_column": "51", "created_at": "2020-05-04 14:39:03", "updated_at": "2020-05-04 14:39:03"}, {"id": "52", "name": "{\"en\": \"Upsherin Giftwrap\"}", "slug": "{\"en\": \"upsherin-giftwrap\"}", "type": null, "order_column": "52", "created_at": "2020-05-04 16:59:07", "updated_at": "2020-05-04 16:59:07"}, {"id": "53", "name": "{\"en\": \"<PERSON><PERSON>\"}", "slug": "{\"en\": \"birchas-harofeh-frames\"}", "type": null, "order_column": "53", "created_at": "2020-05-04 17:02:41", "updated_at": "2020-05-04 17:02:41"}, {"id": "54", "name": "{\"en\": \"<PERSON><PERSON><PERSON><PERSON>ase\"}", "slug": "{\"en\": \"shavuos-vase\"}", "type": null, "order_column": "54", "created_at": "2020-05-07 09:15:05", "updated_at": "2020-05-07 09:15:05"}, {"id": "55", "name": "{\"en\": \"Upsherin Sefarim\"}", "slug": "{\"en\": \"upsherin-sefarim\"}", "type": null, "order_column": "55", "created_at": "2020-05-07 09:27:05", "updated_at": "2020-05-07 09:27:05"}, {"id": "56", "name": "{\"en\": \"<PERSON><PERSON><PERSON>\"}", "slug": "{\"en\": \"tefillas-harofeh\"}", "type": null, "order_column": "56", "created_at": "2020-05-10 02:04:02", "updated_at": "2020-05-10 02:04:02"}, {"id": "57", "name": "{\"en\": \"shavuos kids\"}", "slug": "{\"en\": \"shavuos-kids\"}", "type": null, "order_column": "57", "created_at": "2020-05-21 15:07:09", "updated_at": "2020-05-21 15:07:09"}, {"id": "58", "name": "{\"en\": \"Tisha Ba<PERSON>\"}", "slug": "{\"en\": \"tisha-bav\"}", "type": null, "order_column": "58", "created_at": "2020-06-02 12:33:07", "updated_at": "2020-06-02 12:33:07"}, {"id": "59", "name": "{\"en\": \"Three Weeks\"}", "slug": "{\"en\": \"three-weeks\"}", "type": null, "order_column": "59", "created_at": "2020-06-02 12:33:07", "updated_at": "2020-06-02 12:33:07"}, {"id": "60", "name": "{\"en\": \"ספר קינות\"}", "slug": "{\"en\": \"sfr-kinvt\"}", "type": null, "order_column": "60", "created_at": "2020-06-17 14:40:46", "updated_at": "2020-06-17 14:40:46"}, {"id": "61", "name": "{\"en\": \"Knife\"}", "slug": "{\"en\": \"knife\"}", "type": null, "order_column": "61", "created_at": "2020-09-02 12:21:25", "updated_at": "2020-09-02 12:21:25"}, {"id": "62", "name": "{\"en\": \"Kesu<PERSON>\"}", "slug": "{\"en\": \"kesubah\"}", "type": null, "order_column": "62", "created_at": "2020-10-13 14:38:18", "updated_at": "2020-10-13 14:38:18"}, {"id": "63", "name": "{\"en\": \"Tenoim\"}", "slug": "{\"en\": \"tenoim\"}", "type": null, "order_column": "63", "created_at": "2020-10-13 14:39:55", "updated_at": "2020-10-13 14:39:55"}, {"id": "64", "name": "{\"en\": \"Jerusalem Stone\"}", "slug": "{\"en\": \"jerusalem-stone\"}", "type": null, "order_column": "64", "created_at": "2020-10-15 16:23:30", "updated_at": "2020-10-15 16:23:30"}, {"id": "65", "name": "{\"en\": \"Daily Learning\"}", "slug": "{\"en\": \"daily-learning\"}", "type": null, "order_column": "65", "created_at": "2020-10-20 15:39:41", "updated_at": "2020-10-20 15:39:41"}, {"id": "66", "name": "{\"en\": \"<PERSON><PERSON>h Glasses\"}", "slug": "{\"en\": \"menorah-glasses\"}", "type": null, "order_column": "66", "created_at": "2020-11-19 11:12:04", "updated_at": "2020-11-19 11:12:04"}, {"id": "67", "name": "{\"en\": \"Face Mask\"}", "slug": "{\"en\": \"face-mask\"}", "type": null, "order_column": "67", "created_at": "2020-12-22 15:43:44", "updated_at": "2020-12-22 15:43:44"}, {"id": "68", "name": "{\"en\": \"waterproof mezuzah case\"}", "slug": "{\"en\": \"waterproof-mezuzah-case\"}", "type": null, "order_column": "68", "created_at": "2021-01-04 10:48:48", "updated_at": "2021-01-04 10:48:48"}, {"id": "69", "name": "{\"en\": \"artscroll full set\"}", "slug": "{\"en\": \"artscroll-full-set\"}", "type": null, "order_column": "69", "created_at": "2021-01-27 12:40:10", "updated_at": "2021-01-27 12:40:10"}, {"id": "70", "name": "{\"en\": \"artscroll Sale\"}", "slug": "{\"en\": \"artscroll-sale\"}", "type": null, "order_column": "70", "created_at": "2021-01-27 12:53:46", "updated_at": "2021-01-27 12:53:46"}, {"id": "71", "name": "{\"en\": \"artscroll mishna\"}", "slug": "{\"en\": \"artscroll-mishna\"}", "type": null, "order_column": "71", "created_at": "2021-01-27 13:19:42", "updated_at": "2021-01-27 13:19:42"}, {"id": "72", "name": "{\"en\": \"$1 CD\"}", "slug": "{\"en\": \"1-cd\"}", "type": null, "order_column": "72", "created_at": "2021-01-28 14:13:39", "updated_at": "2021-01-28 14:13:39"}, {"id": "73", "name": "{\"en\": \"top sold 2020\"}", "slug": "{\"en\": \"top-sold-2020\"}", "type": null, "order_column": "73", "created_at": "2021-02-07 23:35:23", "updated_at": "2021-02-07 23:35:23"}, {"id": "74", "name": "{\"en\": \"Glam Design Plastic\"}", "slug": "{\"en\": \"glam-design-plastic\"}", "type": null, "order_column": "74", "created_at": "2021-02-22 11:30:07", "updated_at": "2021-02-22 11:30:07"}, {"id": "75", "name": "{\"en\": \"Geo Design Plastic\"}", "slug": "{\"en\": \"geo-design-plastic\"}", "type": null, "order_column": "75", "created_at": "2021-02-22 11:42:04", "updated_at": "2021-02-22 11:42:04"}, {"id": "76", "name": "{\"en\": \"Bella Design Plastic\"}", "slug": "{\"en\": \"bella-design-plastic\"}", "type": null, "order_column": "76", "created_at": "2021-02-22 12:00:55", "updated_at": "2021-02-22 12:00:55"}, {"id": "77", "name": "{\"en\": \"Flora Design Plastic\"}", "slug": "{\"en\": \"flora-design-plastic\"}", "type": null, "order_column": "77", "created_at": "2021-02-22 12:51:04", "updated_at": "2021-02-22 12:51:04"}, {"id": "78", "name": "{\"en\": \"Versa Design Plastic\"}", "slug": "{\"en\": \"versa-design-plastic\"}", "type": null, "order_column": "78", "created_at": "2021-02-22 12:54:44", "updated_at": "2021-02-22 12:54:44"}, {"id": "79", "name": "{\"en\": \"Aztec Design Plastic\"}", "slug": "{\"en\": \"aztec-design-plastic\"}", "type": null, "order_column": "79", "created_at": "2021-02-22 12:57:47", "updated_at": "2021-02-22 12:57:47"}, {"id": "80", "name": "{\"en\": \"Trend White Plastic\"}", "slug": "{\"en\": \"trend-white-plastic\"}", "type": null, "order_column": "80", "created_at": "2021-02-22 14:09:18", "updated_at": "2021-02-22 14:09:18"}, {"id": "81", "name": "{\"en\": \"Stone Design Plastic\"}", "slug": "{\"en\": \"stone-design-plastic\"}", "type": null, "order_column": "81", "created_at": "2021-02-22 14:16:42", "updated_at": "2021-02-22 14:16:42"}, {"id": "82", "name": "{\"en\": \"Gold Scratched Design Plastic\"}", "slug": "{\"en\": \"gold-scratched-design-plastic\"}", "type": null, "order_column": "82", "created_at": "2021-02-22 14:19:13", "updated_at": "2021-02-22 14:19:13"}, {"id": "83", "name": "{\"en\": \"Silver Scratched Design Plastic\"}", "slug": "{\"en\": \"silver-scratched-design-plastic\"}", "type": null, "order_column": "83", "created_at": "2021-02-22 15:41:27", "updated_at": "2021-02-22 15:41:27"}, {"id": "84", "name": "{\"en\": \"Matiz Design Plastic\"}", "slug": "{\"en\": \"matiz-design-plastic\"}", "type": null, "order_column": "84", "created_at": "2021-02-22 15:44:30", "updated_at": "2021-02-22 15:44:30"}, {"id": "85", "name": "{\"en\": \"Motif Design Plastic\"}", "slug": "{\"en\": \"motif-design-plastic\"}", "type": null, "order_column": "85", "created_at": "2021-02-22 15:46:54", "updated_at": "2021-02-22 15:46:54"}, {"id": "86", "name": "{\"en\": \"Ivory Victorian Design\"}", "slug": "{\"en\": \"ivory-victorian-design\"}", "type": null, "order_column": "86", "created_at": "2021-02-22 16:37:31", "updated_at": "2021-02-22 16:37:31"}, {"id": "87", "name": "{\"en\": \"Robin Blue Victorian Design\"}", "slug": "{\"en\": \"robin-blue-victorian-design\"}", "type": null, "order_column": "87", "created_at": "2021-02-22 16:39:58", "updated_at": "2021-02-22 16:39:58"}, {"id": "88", "name": "{\"en\": \"Gold Splash Premium\"}", "slug": "{\"en\": \"gold-splash-premium\"}", "type": null, "order_column": "88", "created_at": "2021-02-23 11:04:05", "updated_at": "2021-02-23 11:04:05"}, {"id": "89", "name": "{\"en\": \"Silver Splash Premium\"}", "slug": "{\"en\": \"silver-splash-premium\"}", "type": null, "order_column": "89", "created_at": "2021-02-23 11:14:57", "updated_at": "2021-02-23 11:14:57"}, {"id": "90", "name": "{\"en\": \"Gold Radial Premium\"}", "slug": "{\"en\": \"gold-radial-premium\"}", "type": null, "order_column": "90", "created_at": "2021-02-23 11:23:51", "updated_at": "2021-02-23 11:23:51"}, {"id": "91", "name": "{\"en\": \"Silver Radial Premium\"}", "slug": "{\"en\": \"silver-radial-premium\"}", "type": null, "order_column": "91", "created_at": "2021-02-23 12:51:08", "updated_at": "2021-02-23 12:51:08"}, {"id": "92", "name": "{\"en\": \"Silver Droplet Premium\"}", "slug": "{\"en\": \"silver-droplet-premium\"}", "type": null, "order_column": "92", "created_at": "2021-02-23 12:55:58", "updated_at": "2021-02-23 12:55:58"}, {"id": "93", "name": "{\"en\": \"Silver Ovals Premium\"}", "slug": "{\"en\": \"silver-ovals-premium\"}", "type": null, "order_column": "93", "created_at": "2021-02-23 13:02:18", "updated_at": "2021-02-23 13:02:18"}, {"id": "94", "name": "{\"en\": \"White & Gold Line Premium\"}", "slug": "{\"en\": \"white-gold-line-premium\"}", "type": null, "order_column": "94", "created_at": "2021-02-23 13:09:58", "updated_at": "2021-02-23 13:09:58"}, {"id": "95", "name": "{\"en\": \"Gold Leaf Premium\"}", "slug": "{\"en\": \"gold-leaf-premium\"}", "type": null, "order_column": "95", "created_at": "2021-02-23 13:26:38", "updated_at": "2021-02-23 13:26:38"}, {"id": "96", "name": "{\"en\": \"Silver Leaf Premium\"}", "slug": "{\"en\": \"silver-leaf-premium\"}", "type": null, "order_column": "96", "created_at": "2021-02-23 13:30:59", "updated_at": "2021-02-23 13:30:59"}, {"id": "97", "name": "{\"en\": \"Rose Gold Leaf Premium\"}", "slug": "{\"en\": \"rose-gold-leaf-premium\"}", "type": null, "order_column": "97", "created_at": "2021-02-23 13:39:03", "updated_at": "2021-02-23 13:39:03"}, {"id": "98", "name": "{\"en\": \"White Square\"}", "slug": "{\"en\": \"white-square\"}", "type": null, "order_column": "98", "created_at": "2021-02-23 13:46:47", "updated_at": "2021-02-23 13:46:47"}, {"id": "99", "name": "{\"en\": \"White Square Silver Line\"}", "slug": "{\"en\": \"white-square-silver-line\"}", "type": null, "order_column": "99", "created_at": "2021-02-23 13:53:24", "updated_at": "2021-02-23 13:53:24"}, {"id": "100", "name": "{\"en\": \"White Square Gold Line\"}", "slug": "{\"en\": \"white-square-gold-line\"}", "type": null, "order_column": "100", "created_at": "2021-02-23 13:59:19", "updated_at": "2021-02-23 13:59:19"}, {"id": "101", "name": "{\"en\": \"White Square Rose Gold Line\"}", "slug": "{\"en\": \"white-square-rose-gold-line\"}", "type": null, "order_column": "101", "created_at": "2021-02-23 14:30:18", "updated_at": "2021-02-23 14:30:18"}, {"id": "102", "name": "{\"en\": \"White Rectangular\"}", "slug": "{\"en\": \"white-rectangular\"}", "type": null, "order_column": "102", "created_at": "2021-02-23 14:34:42", "updated_at": "2021-02-23 14:34:42"}, {"id": "103", "name": "{\"en\": \"Exquisite Classic Gold Plastic\"}", "slug": "{\"en\": \"exquisite-classic-gold-plastic\"}", "type": null, "order_column": "103", "created_at": "2021-02-23 14:39:17", "updated_at": "2021-02-23 14:39:17"}, {"id": "104", "name": "{\"en\": \"Exquisite Classic Silver Plastic\"}", "slug": "{\"en\": \"exquisite-classic-silver-plastic\"}", "type": null, "order_column": "104", "created_at": "2021-02-23 14:47:30", "updated_at": "2021-02-23 14:47:30"}, {"id": "105", "name": "{\"en\": \"Exquisite Classic Rose Gold Plastic\"}", "slug": "{\"en\": \"exquisite-classic-rose-gold-plastic\"}", "type": null, "order_column": "105", "created_at": "2021-02-23 14:56:51", "updated_at": "2021-02-23 14:56:51"}, {"id": "106", "name": "{\"en\": \"shas sale\"}", "slug": "{\"en\": \"shas-sale\"}", "type": null, "order_column": "106", "created_at": "2021-05-07 12:13:51", "updated_at": "2021-05-07 12:13:51"}, {"id": "107", "name": "{\"en\": \"<PERSON>ikkin leil shavous\"}", "slug": "{\"en\": \"tikkin-leil-shavous\"}", "type": null, "order_column": "107", "created_at": "2021-05-10 15:49:26", "updated_at": "2021-05-10 15:49:26"}, {"id": "108", "name": "{\"en\": \"Classic\"}", "slug": "{\"en\": \"classic\"}", "type": null, "order_column": "108", "created_at": "2021-06-15 16:50:25", "updated_at": "2021-06-15 16:50:25"}, {"id": "109", "name": "{\"en\": \"Trendables\"}", "slug": "{\"en\": \"trendables\"}", "type": null, "order_column": "109", "created_at": "2021-06-15 16:51:04", "updated_at": "2021-06-15 16:51:04"}, {"id": "110", "name": "{\"en\": \"Roses\"}", "slug": "{\"en\": \"roses\"}", "type": null, "order_column": "110", "created_at": "2021-06-15 16:57:35", "updated_at": "2021-06-15 16:57:35"}, {"id": "111", "name": "{\"en\": \"Classic Silver Plates\"}", "slug": "{\"en\": \"classic-silver-plates\"}", "type": null, "order_column": "111", "created_at": "2021-06-16 10:11:58", "updated_at": "2021-06-16 10:11:58"}, {"id": "112", "name": "{\"en\": \"Classic Silver Design\"}", "slug": "{\"en\": \"classic-silver-design\"}", "type": null, "order_column": "112", "created_at": "2021-06-16 10:12:58", "updated_at": "2021-06-16 10:12:58"}, {"id": "113", "name": "{\"en\": \"Classic Gold Design Plates\"}", "slug": "{\"en\": \"classic-gold-design-plates\"}", "type": null, "order_column": "113", "created_at": "2021-06-16 10:14:41", "updated_at": "2021-06-16 10:14:41"}, {"id": "114", "name": "{\"en\": \"Classic Gold Design\"}", "slug": "{\"en\": \"classic-gold-design\"}", "type": null, "order_column": "114", "created_at": "2021-06-16 10:18:08", "updated_at": "2021-06-16 10:18:08"}, {"id": "115", "name": "{\"en\": \"Roses Design\"}", "slug": "{\"en\": \"roses-design\"}", "type": null, "order_column": "115", "created_at": "2021-06-16 10:23:43", "updated_at": "2021-06-16 10:23:43"}, {"id": "116", "name": "{\"en\": \"Classic Navy Design\"}", "slug": "{\"en\": \"classic-navy-design\"}", "type": null, "order_column": "116", "created_at": "2021-06-16 10:30:56", "updated_at": "2021-06-16 10:30:56"}, {"id": "117", "name": "{\"en\": \"Classic Burgundy Design\"}", "slug": "{\"en\": \"classic-burgundy-design\"}", "type": null, "order_column": "117", "created_at": "2021-06-16 10:35:28", "updated_at": "2021-06-16 10:35:28"}, {"id": "118", "name": "{\"en\": \"Classic Black Design\"}", "slug": "{\"en\": \"classic-black-design\"}", "type": null, "order_column": "118", "created_at": "2021-06-16 10:39:38", "updated_at": "2021-06-16 10:39:38"}, {"id": "119", "name": "{\"en\": \"Floral Brush Design\"}", "slug": "{\"en\": \"floral-brush-design\"}", "type": null, "order_column": "119", "created_at": "2021-06-16 10:43:29", "updated_at": "2021-06-16 10:43:29"}, {"id": "120", "name": "{\"en\": \"Floral Gold Brush Design\"}", "slug": "{\"en\": \"floral-gold-brush-design\"}", "type": null, "order_column": "120", "created_at": "2021-06-16 10:46:06", "updated_at": "2021-06-16 10:46:06"}, {"id": "121", "name": "{\"en\": \"Lunch<PERSON> Na<PERSON>kins\"}", "slug": "{\"en\": \"luncheon-napkins\"}", "type": null, "order_column": "121", "created_at": "2021-06-16 10:51:52", "updated_at": "2021-06-16 10:51:52"}, {"id": "122", "name": "{\"en\": \"Square Bottom Shot Glass\"}", "slug": "{\"en\": \"square-bottom-shot-glass\"}", "type": null, "order_column": "122", "created_at": "2021-06-16 10:59:03", "updated_at": "2021-06-16 10:59:03"}, {"id": "123", "name": "{\"en\": \"<PERSON>nn<PERSON>\"}", "slug": "{\"en\": \"kinnos\"}", "type": null, "order_column": "123", "created_at": "2021-06-25 11:44:47", "updated_at": "2021-06-25 11:44:47"}, {"id": "124", "name": "{\"en\": \"<PERSON><PERSON><PERSON>\"}", "slug": "{\"en\": \"shimta\"}", "type": null, "order_column": "124", "created_at": "2021-07-06 17:23:12", "updated_at": "2021-07-06 17:23:12"}, {"id": "125", "name": "{\"en\": \"shmita\"}", "slug": "{\"en\": \"shmita\"}", "type": null, "order_column": "125", "created_at": "2021-07-07 10:25:26", "updated_at": "2021-07-07 10:25:26"}, {"id": "126", "name": "{\"en\": \"top sold 5781\"}", "slug": "{\"en\": \"top-sold-5781\"}", "type": null, "order_column": "126", "created_at": "2021-09-19 11:47:36", "updated_at": "2021-09-19 11:47:36"}, {"id": "127", "name": "{\"en\": \"top seforim sold 5781\"}", "slug": "{\"en\": \"top-seforim-sold-5781\"}", "type": null, "order_column": "127", "created_at": "2021-09-30 14:45:59", "updated_at": "2021-09-30 14:45:59"}, {"id": "128", "name": "{\"en\": \"top seforim 5781\"}", "slug": "{\"en\": \"top-seforim-5781\"}", "type": null, "order_column": "128", "created_at": "2021-10-06 17:13:36", "updated_at": "2021-10-06 17:13:36"}, {"id": "129", "name": "{\"en\": \"Top Seforim 5781\"}", "slug": "{\"en\": \"top-seforim-5781\"}", "type": null, "order_column": "129", "created_at": "2021-10-07 13:22:51", "updated_at": "2021-10-07 13:22:51"}, {"id": "130", "name": "{\"en\": \"children\"}", "slug": "{\"en\": \"children\"}", "type": null, "order_column": "130", "created_at": "2021-10-12 13:10:01", "updated_at": "2021-10-12 13:10:01"}, {"id": "131", "name": "{\"en\": \"chanukah\"}", "slug": "{\"en\": \"chanukah\"}", "type": null, "order_column": "131", "created_at": "2021-11-02 13:02:51", "updated_at": "2021-11-02 13:02:51"}, {"id": "132", "name": "{\"en\": \"Black Friday\"}", "slug": "{\"en\": \"black-friday\"}", "type": null, "order_column": "132", "created_at": "2021-11-25 00:14:05", "updated_at": "2021-11-25 00:14:05"}, {"id": "133", "name": "{\"en\": \"Most sold in 2021\"}", "slug": "{\"en\": \"most-sold-in-2021\"}", "type": null, "order_column": "133", "created_at": "2021-12-31 11:40:30", "updated_at": "2021-12-31 11:40:30"}, {"id": "134", "name": "{\"en\": \"Havdalah\"}", "slug": "{\"en\": \"havdalah\"}", "type": null, "order_column": "134", "created_at": "2022-02-03 22:59:39", "updated_at": "2022-02-03 22:59:39"}, {"id": "135", "name": "{\"en\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"}", "slug": "{\"en\": \"mitgelebt\"}", "type": null, "order_column": "135", "created_at": "2022-02-09 03:53:10", "updated_at": "2022-02-09 03:53:10"}, {"id": "136", "name": "{\"en\": \"<PERSON><PERSON>\"}", "slug": "{\"en\": \"bris-pillow\"}", "type": null, "order_column": "136", "created_at": "2022-02-09 04:26:50", "updated_at": "2022-02-09 04:26:50"}, {"id": "137", "name": "{\"en\": \"Baby Toys\"}", "slug": "{\"en\": \"baby-toys\"}", "type": null, "order_column": "137", "created_at": "2022-02-09 05:00:50", "updated_at": "2022-02-09 05:00:50"}, {"id": "138", "name": "{\"en\": \"Gragger\"}", "slug": "{\"en\": \"gragger\"}", "type": null, "order_column": "138", "created_at": "2022-02-14 03:16:10", "updated_at": "2022-02-14 03:16:10"}, {"id": "139", "name": "{\"en\": \"Shalos V’teshuvos\"}", "slug": "{\"en\": \"shalos-v<PERSON><PERSON><PERSON>\"}", "type": null, "order_column": "139", "created_at": "2022-04-04 07:56:47", "updated_at": "2022-04-04 07:56:47"}, {"id": "140", "name": "{\"en\": \"Al Hatorah\"}", "slug": "{\"en\": \"al-hatorah\"}", "type": null, "order_column": "140", "created_at": "2022-04-04 07:57:58", "updated_at": "2022-04-04 07:57:58"}, {"id": "141", "name": "{\"en\": \"General halacha\"}", "slug": "{\"en\": \"general-halacha\"}", "type": null, "order_column": "141", "created_at": "2022-04-04 08:02:17", "updated_at": "2022-04-04 08:02:17"}, {"id": "142", "name": "{\"en\": \"Shabbos\"}", "slug": "{\"en\": \"shabbos\"}", "type": null, "order_column": "142", "created_at": "2022-04-04 08:05:54", "updated_at": "2022-04-04 08:05:54"}, {"id": "143", "name": "{\"en\": \"Jewish thought\"}", "slug": "{\"en\": \"jewish-thought\"}", "type": null, "order_column": "143", "created_at": "2022-04-04 08:06:55", "updated_at": "2022-04-04 08:06:55"}, {"id": "144", "name": "{\"en\": \"Biographies\"}", "slug": "{\"en\": \"biographies\"}", "type": null, "order_column": "144", "created_at": "2022-04-04 08:17:23", "updated_at": "2022-04-04 08:17:23"}, {"id": "145", "name": "{\"en\": \"top sold 2022\"}", "slug": "{\"en\": \"top-sold-2022\"}", "type": null, "order_column": "145", "created_at": "2022-12-29 16:06:10", "updated_at": "2022-12-29 16:06:10"}, {"id": "146", "name": "{\"en\": \"Summer Sale\"}", "slug": "{\"en\": \"summer-sale\"}", "type": null, "order_column": "146", "created_at": "2023-06-21 18:53:53", "updated_at": "2023-06-21 18:53:53"}, {"id": "147", "name": "{\"en\": \"<PERSON><PERSON><PERSON>\"}", "slug": "{\"en\": \"mitzva-note\"}", "type": null, "order_column": "147", "created_at": "2023-08-23 03:16:15", "updated_at": "2023-08-23 03:16:15"}, {"id": "148", "name": "{\"en\": \"Midwinter Clearance\"}", "slug": "{\"en\": \"midwinter-clearance\"}", "type": null, "order_column": "148", "created_at": "2024-01-09 11:38:37", "updated_at": "2024-01-09 11:38:37"}, {"id": "149", "name": "{\"en\": \"2023 Best Seller\"}", "slug": "{\"en\": \"2023-best-seller\"}", "type": null, "order_column": "149", "created_at": "2024-01-18 13:35:41", "updated_at": "2024-01-18 13:35:41"}, {"id": "150", "name": "{\"en\": \"artscroll sale 2024\"}", "slug": "{\"en\": \"artscroll-sale-2024\"}", "type": null, "order_column": "150", "created_at": "2024-02-22 11:15:33", "updated_at": "2024-02-22 11:15:33"}, {"id": "151", "name": "{\"en\": \"Onyx Collection\"}", "slug": "{\"en\": \"onyx-collection\"}", "type": null, "order_column": "151", "created_at": "2024-07-17 14:21:34", "updated_at": "2024-07-17 14:21:34"}, {"id": "152", "name": "{\"en\": \"Haus Honeycomb Collection\"}", "slug": "{\"en\": \"haus-honeycomb-collection\"}", "type": null, "order_column": "152", "created_at": "2024-09-17 12:48:35", "updated_at": "2024-09-17 12:48:35"}, {"id": "153", "name": "{\"en\": \"cookbook sale\"}", "slug": "{\"en\": \"cookbook-sale\"}", "type": null, "order_column": "153", "created_at": "2024-12-12 10:14:36", "updated_at": "2024-12-12 10:14:36"}, {"id": "154", "name": "{\"en\": \"2024 best seller\"}", "slug": "{\"en\": \"2024-best-seller\"}", "type": null, "order_column": "154", "created_at": "2025-01-09 17:25:02", "updated_at": "2025-01-09 17:25:02"}]