[{"id": "1", "key": "phone", "value": "************", "admin": null, "helper": "", "created_at": "2019-10-23 12:08:17", "updated_at": "2019-10-23 12:08:17"}, {"id": "2", "key": "store_address_line_1", "value": "5004 13th Ave.", "admin": null, "helper": "", "created_at": "2019-10-23 12:09:36", "updated_at": "2020-06-22 11:31:16"}, {"id": "3", "key": "store_address_line_2", "value": "", "admin": null, "helper": "", "created_at": "2019-10-23 12:10:12", "updated_at": "2019-10-23 12:10:12"}, {"id": "4", "key": "store_city", "value": "Brooklyn", "admin": null, "helper": "", "created_at": "2019-10-23 12:10:42", "updated_at": "2019-10-23 12:10:42"}, {"id": "5", "key": "store_postal_code", "value": "11219", "admin": null, "helper": "", "created_at": "2019-10-23 12:11:05", "updated_at": "2019-10-23 12:11:05"}, {"id": "6", "key": "store_country", "value": "US", "admin": null, "helper": "", "created_at": "2019-10-23 12:11:31", "updated_at": "2019-10-23 12:11:31"}, {"id": "7", "key": "store_state", "value": "NY", "admin": null, "helper": "", "created_at": "2019-10-23 12:11:57", "updated_at": "2019-10-23 12:11:57"}, {"id": "8", "key": "shipping_address_line_1", "value": "5004 13th Ave", "admin": null, "helper": "", "created_at": "2019-10-23 12:13:04", "updated_at": "2024-02-06 13:46:38"}, {"id": "9", "key": "shipping_address_line_2", "value": "", "admin": null, "helper": "", "created_at": "2019-10-23 12:13:34", "updated_at": "2024-02-06 13:46:48"}, {"id": "10", "key": "shipping_city", "value": "Brooklyn", "admin": null, "helper": "", "created_at": "2019-10-23 12:14:08", "updated_at": "2019-10-23 12:14:08"}, {"id": "11", "key": "shipping_postal_code", "value": "11219", "admin": null, "helper": "", "created_at": "2019-10-23 12:14:41", "updated_at": "2019-10-23 12:14:41"}, {"id": "12", "key": "shipping_country", "value": "US", "admin": null, "helper": "", "created_at": "2019-10-23 12:15:27", "updated_at": "2019-10-23 12:15:37"}, {"id": "13", "key": "shipping_state", "value": "NY", "admin": null, "helper": "", "created_at": "2019-10-23 12:16:02", "updated_at": "2019-10-23 12:16:02"}, {"id": "14", "key": "free_shipping_minimum", "value": "59.99", "admin": null, "helper": "This feild should be 1 cent less than free shipping, ex: for free shipping over $35 enter 34.99", "created_at": "2019-10-23 12:16:36", "updated_at": "2022-03-07 14:54:42"}, {"id": "15", "key": "email", "value": "<EMAIL>", "admin": null, "helper": "", "created_at": "2019-10-23 12:20:18", "updated_at": "2019-10-23 12:20:18"}, {"id": "16", "key": "store_name", "value": "Eichlers Boro Park", "admin": null, "helper": "", "created_at": "2019-10-23 13:02:02", "updated_at": "2020-06-23 16:59:07"}, {"id": "17", "key": "dimensional_factor", "value": "139", "admin": null, "helper": "", "created_at": "2019-11-04 16:55:15", "updated_at": "2019-11-04 16:55:15"}, {"id": "18", "key": "default_tax_rate", "value": "8", "admin": null, "helper": "", "created_at": "2019-11-21 12:20:57", "updated_at": "2019-11-21 12:20:57"}, {"id": "19", "key": "return_service", "value": "usps_parcel_select", "admin": null, "helper": "others = <ul>\n\t<li>usps_first_class_mail</li>\n\t<li>usps_media_mail</li>\n\t<li>usps_parcel_select</li>\n\t<li>usps_priority_mail</li>\n\t<li>usps_priority_mail_express</li>\n\t<li>ups_3_day_select</li>\n\t<li>ups_ground</li>\n\t<li>fedex_ground</li>\n\t<li>fedex_home_delivery</li>\n\t<li>fedex_express_saver</li>\n\t<li>fedex_standard_overnight</li>\n\t<li>fedex_2_day_freight</li>\n\t<li>fedex_3_day_freight</li>\n</ul>", "created_at": "2019-11-28 16:04:25", "updated_at": "2020-06-08 18:32:39"}, {"id": "20", "key": "return_expires", "value": "31", "admin": null, "helper": "", "created_at": "2019-12-02 11:10:58", "updated_at": "2019-12-02 11:10:58"}, {"id": "22", "key": "hours_to_send_abandoned_cart_email", "value": "5", "admin": null, "helper": "", "created_at": "2019-12-02 11:20:51", "updated_at": "2019-12-02 12:26:02"}, {"id": "25", "key": "inventory_updated_at", "value": "Don't Edit", "admin": "1", "helper": "", "created_at": "2019-12-17 12:49:34", "updated_at": "2024-10-15 21:15:02"}, {"id": "26", "key": "trending_time_duration", "value": "8,weeks", "admin": null, "helper": "Insert Number, comma, weeks\\days\\months for calculating trending", "created_at": "2019-12-26 10:41:03", "updated_at": "2021-03-01 16:37:05"}, {"id": "27", "key": "trending_minimum_items", "value": "5", "admin": null, "helper": "How many Items make it Trending", "created_at": "2019-12-26 11:19:39", "updated_at": "2019-12-26 11:19:39"}, {"id": "28", "key": "trending_minimum_customers", "value": "3", "admin": null, "helper": "How many customers bought it to make it Trending", "created_at": "2019-12-26 11:20:31", "updated_at": "2019-12-26 11:20:31"}, {"id": "29", "key": "frequently_bought_together_threshold", "value": "3", "admin": null, "helper": "", "created_at": "2019-12-26 11:45:42", "updated_at": "2019-12-26 11:45:42"}, {"id": "30", "key": "frequently_bought_together_confidence", "value": "30", "admin": null, "helper": "", "created_at": "2019-12-26 11:46:13", "updated_at": "2019-12-26 11:46:13"}, {"id": "31", "key": "frequent_date", "value": "2010-09-10", "admin": null, "helper": "From which date should we calculate", "created_at": "2020-05-13 14:55:40", "updated_at": "2020-05-13 14:55:40"}, {"id": "32", "key": "frequent_min_customer", "value": "3", "admin": null, "helper": "How many customers bought it together, to consider it frequently bought together", "created_at": "2020-05-13 16:18:23", "updated_at": "2020-05-13 16:18:23"}, {"id": "33", "key": "order_notifications_emails", "value": "<EMAIL>,<EMAIL>", "admin": null, "helper": "Add Emails Comma Seperated", "created_at": "2020-01-06 15:27:14", "updated_at": "2020-03-23 22:28:51"}, {"id": "34", "key": "minimum_search_score", "value": "100", "admin": "0", "helper": "", "created_at": "2020-01-13 19:47:55", "updated_at": "2020-01-13 19:47:55"}, {"id": "35", "key": "swiftype_update_bundle_ids", "value": "[]", "admin": "1", "helper": "", "created_at": "2020-01-12 15:20:45", "updated_at": "2020-01-12 15:20:45"}, {"id": "36", "key": "swiftype_delete_bundle_ids", "value": "[1]", "admin": "1", "helper": "", "created_at": "2020-01-12 15:20:45", "updated_at": "2020-01-12 15:20:45"}, {"id": "37", "key": "hours_after_delivery_to_send_survey", "value": "3", "admin": null, "helper": "", "created_at": "2020-01-22 12:02:41", "updated_at": "2020-01-22 12:02:41"}, {"id": "38", "key": "send_survey", "value": "false", "admin": null, "helper": "true or false", "created_at": "2020-01-22 12:03:25", "updated_at": "2020-04-28 19:18:39"}, {"id": "39", "key": "swiftype_popularity_score", "value": "5", "admin": "1", "helper": "", "created_at": "2020-01-28 14:59:06", "updated_at": "2020-01-28 14:59:06"}, {"id": "40", "key": "skus_that_are_free_in_subscriptions", "value": "", "admin": null, "helper": "insert comma seperated sku's", "created_at": "2020-02-19 18:08:21", "updated_at": "2020-03-12 10:56:06"}, {"id": "41", "key": "caregory_seo_title", "value": "Shop for {name} online at Shop Eichlers. Free shipping on most items above $59+", "admin": null, "helper": "{name} will be replaced with the Categories name..", "created_at": "2020-04-22 11:14:42", "updated_at": "2022-03-08 00:03:12"}, {"id": "42", "key": "swiftype_update_ids", "value": "[68613,66617,47475,66867,43558,49506,49902,49505,54850,60982,68070,70103,11350,70031,70085,70088,70089,70090,70083,70082,70199,40754,70489,57289,70341,70534,40365,66665,32967,44417,44109,49498,53120,50210,53482,54067,66644,66745,54782,69333,69330,69357,69332,69356,69334,69646,69647,63871,69909,69919,56944,70145,28602,60264,47303,46921,70423,68396,52255,33605,69756,69950,70113,70363,70174,27984,70453,69956,70536,28688,67600,64105,69157,52936,69915,69623,69949,69976,70141,70365,42384,66594,66855,54857,54856,49258,66761,68969,1582,69947,69975,70361,69966,70533,46478,27283,66596,28353,61483,52790,54840,68844,69573,52902,69933,69812,70164,47554,60057,54842,28472,28433,28729,66726,61576,54796,873,2010,55163,28615,66648,66649,66705,53484,53486,68693,69331,56569,65252,66872,67034,2965,49733,61412,54961,69636,69962,40752,43981,40231,66832,66833,47635,47321,66868,47251,44127,66932,51232,61176,70307,43983,70484,28040]", "admin": "1", "helper": "", "created_at": "2019-12-31 18:37:31", "updated_at": "2024-10-15 21:15:39"}, {"id": "43", "key": "swiftype_delete_ids", "value": "[66167,59192,48973,2750,27264,49429,40755,47289,44109,47502,53484,53486,44388,49404,69331,56569,65252,44200,28688,2167,972,48980,69686,69900,60771,60921,32034,46478,61155,61116,61315,28693,51984,62326,64916,28513,28515,68151,2784,49512,54854,54961,55360,33653,1869,47415,47435,61534,29300]", "admin": "1", "helper": "", "created_at": "2019-12-31 18:37:31", "updated_at": "2024-10-15 21:15:39"}, {"id": "44", "key": "exclude_from_free_shipping_does_not_apply_to", "value": "3", "admin": "0", "helper": "Enter comma seperated shipping option ids", "created_at": null, "updated_at": null}, {"id": "45", "key": "how_many_days_to_keep_inventory_track", "value": "14", "admin": "0", "helper": "", "created_at": "2020-05-21 09:53:41", "updated_at": "2020-05-21 09:53:41"}, {"id": "46", "key": "how_many_hours_we_keep_store_pickup_orders", "value": "48", "admin": null, "helper": "entet a number", "created_at": "2020-06-25 13:55:43", "updated_at": "2020-06-25 13:55:43"}, {"id": "47", "key": "google_type", "value": "products", "admin": "1", "helper": "", "created_at": null, "updated_at": "2023-06-24 01:00:22"}, {"id": "48", "key": "google_id", "value": "42240", "admin": "1", "helper": "", "created_at": null, "updated_at": "2023-06-25 01:00:03"}]