[{"id": "1", "menu_id": "1", "name": "Garments", "class": "App\\Classes\\Pages", "value": "1", "target": "_self", "parameters": null, "parent_id": null, "order": "4", "enabled": "1", "created_at": "2019-05-21 16:05:12", "updated_at": "2024-05-01 12:53:42"}, {"id": "2", "menu_id": "1", "name": "Yarmulke", "class": "App\\Classes\\Pages", "value": "77", "target": "_self", "parameters": null, "parent_id": "1", "order": "6", "enabled": "1", "created_at": "2019-05-21 16:05:27", "updated_at": "2022-02-15 17:46:24"}, {"id": "3", "menu_id": "1", "name": "Jewish Thought", "class": "App\\Classes\\Categories", "value": "17", "target": "_self", "parameters": null, "parent_id": "4", "order": "5", "enabled": "1", "created_at": "2019-05-21 16:29:34", "updated_at": "2023-02-17 12:55:27"}, {"id": "4", "menu_id": "1", "name": "Books", "class": "App\\Classes\\Pages", "value": "2", "target": "_self", "parameters": null, "parent_id": null, "order": "3", "enabled": "1", "created_at": "2019-05-21 16:30:05", "updated_at": "2024-05-01 12:53:41"}, {"id": "8", "menu_id": "1", "name": "Novels", "class": "App\\Classes\\Categories", "value": "18", "target": "_self", "parameters": null, "parent_id": "250", "order": "5", "enabled": "1", "created_at": "2019-05-22 11:29:55", "updated_at": "2021-01-10 12:02:09"}, {"id": "9", "menu_id": "1", "name": "Sefarim", "class": "App\\Classes\\Pages", "value": "4", "target": "_self", "parameters": null, "parent_id": null, "order": "2", "enabled": "1", "created_at": "2019-06-06 10:56:45", "updated_at": "2024-05-01 12:53:39"}, {"id": "10", "menu_id": "1", "name": "Judaica", "class": "App\\Classes\\Pages", "value": "5", "target": "_self", "parameters": null, "parent_id": null, "order": "5", "enabled": "1", "created_at": "2019-06-06 10:58:00", "updated_at": "2024-05-01 12:53:42"}, {"id": "11", "menu_id": "1", "name": "Holidays", "class": "App\\Classes\\Pages", "value": "6", "target": "_self", "parameters": null, "parent_id": null, "order": "1", "enabled": "1", "created_at": "2019-06-06 10:58:13", "updated_at": "2024-06-24 20:07:50"}, {"id": "12", "menu_id": "1", "name": "Media", "class": "App\\Classes\\Pages", "value": "78", "target": "_self", "parameters": null, "parent_id": null, "order": "7", "enabled": "1", "created_at": "2019-06-06 10:58:26", "updated_at": "2024-03-25 17:04:04"}, {"id": "13", "menu_id": "1", "name": "Children's Games & Toys", "class": "App\\Classes\\Pages", "value": "8", "target": "_self", "parameters": null, "parent_id": null, "order": "6", "enabled": "1", "created_at": "2019-06-06 10:58:52", "updated_at": "2024-03-25 17:04:03"}, {"id": "14", "menu_id": "1", "name": "Gifts", "class": "App\\Classes\\Pages", "value": "9", "target": "_self", "parameters": null, "parent_id": "10", "order": "9", "enabled": "0", "created_at": "2019-06-06 10:59:04", "updated_at": "2022-02-16 14:46:15"}, {"id": "22", "menu_id": "1", "name": "Chassidus Books", "class": "App\\Classes\\Categories", "value": "25", "target": "_self", "parameters": null, "parent_id": "284", "order": "1", "enabled": "0", "created_at": "2019-06-06 11:23:25", "updated_at": "2022-05-11 12:33:38"}, {"id": "23", "menu_id": "1", "name": "Biographies", "class": "App\\Classes\\Categories", "value": "26", "target": "_self", "parameters": null, "parent_id": "250", "order": "2", "enabled": "1", "created_at": "2019-06-06 11:25:02", "updated_at": "2021-01-10 12:02:09"}, {"id": "24", "menu_id": "1", "name": "Coffee Table Books", "class": "App\\Classes\\Categories", "value": "27", "target": "_self", "parameters": null, "parent_id": "250", "order": "7", "enabled": "1", "created_at": "2019-06-06 11:25:16", "updated_at": "2022-05-03 13:22:53"}, {"id": "26", "menu_id": "1", "name": "Mourning", "class": "App\\Classes\\Categories", "value": "29", "target": "_self", "parameters": null, "parent_id": "283", "order": "5", "enabled": "1", "created_at": "2019-06-06 11:25:37", "updated_at": "2020-01-16 15:36:51"}, {"id": "27", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "30", "target": "_self", "parameters": null, "parent_id": "284", "order": "2", "enabled": "1", "created_at": "2019-06-06 11:25:48", "updated_at": "2019-12-25 13:05:59"}, {"id": "28", "menu_id": "1", "name": "Pirkei Avos", "class": "App\\Classes\\Categories", "value": "32", "target": "_self", "parameters": null, "parent_id": "284", "order": "3", "enabled": "1", "created_at": "2019-06-06 11:26:07", "updated_at": "2019-12-25 13:05:59"}, {"id": "29", "menu_id": "1", "name": "Parenting & Chinuch", "class": "App\\Classes\\Categories", "value": "31", "target": "_self", "parameters": null, "parent_id": "283", "order": "6", "enabled": "1", "created_at": "2019-06-06 11:26:23", "updated_at": "2020-01-16 15:36:51"}, {"id": "30", "menu_id": "1", "name": "Science", "class": "App\\Classes\\Categories", "value": "33", "target": "_self", "parameters": null, "parent_id": "250", "order": "8", "enabled": "1", "created_at": "2019-06-06 11:26:32", "updated_at": "2022-05-03 13:22:53"}, {"id": "31", "menu_id": "1", "name": "Self Help", "class": "App\\Classes\\Categories", "value": "34", "target": "_self", "parameters": null, "parent_id": "283", "order": "7", "enabled": "1", "created_at": "2019-06-06 11:26:42", "updated_at": "2020-01-16 15:36:51"}, {"id": "62", "menu_id": "1", "name": "Gartels", "class": "App\\Classes\\Categories", "value": "65", "target": "_self", "parameters": null, "parent_id": "1", "order": "10", "enabled": "1", "created_at": "2019-06-07 10:57:25", "updated_at": "2022-04-28 12:26:28"}, {"id": "63", "menu_id": "1", "name": "Kittels", "class": "App\\Classes\\Categories", "value": "66", "target": "_self", "parameters": null, "parent_id": "1", "order": "11", "enabled": "1", "created_at": "2019-06-07 10:57:50", "updated_at": "2022-04-28 12:26:28"}, {"id": "64", "menu_id": "1", "name": "Rain Coats", "class": "App\\Classes\\Categories", "value": "67", "target": "_self", "parameters": null, "parent_id": "1", "order": "12", "enabled": "0", "created_at": "2019-06-07 10:58:06", "updated_at": "2022-04-28 12:26:28"}, {"id": "68", "menu_id": "1", "name": "<PERSON><PERSON> and <PERSON><PERSON>in Bags", "class": "App\\Classes\\Pages", "value": "88", "target": "_self", "parameters": null, "parent_id": "1", "order": "7", "enabled": "1", "created_at": "2019-06-07 10:59:20", "updated_at": "2022-04-28 12:26:28"}, {"id": "69", "menu_id": "1", "name": "Talleisim & Atarahs", "class": "App\\Classes\\AutomatedCategories", "value": "54", "target": "_self", "parameters": null, "parent_id": "1", "order": "8", "enabled": "1", "created_at": "2019-06-07 10:59:37", "updated_at": "2022-04-28 12:26:28"}, {"id": "71", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "13", "target": "_self", "parameters": null, "parent_id": "1", "order": "9", "enabled": "1", "created_at": "2019-06-07 11:00:13", "updated_at": "2022-04-28 12:26:28"}, {"id": "74", "menu_id": "1", "name": "Succos", "class": "App\\Classes\\Pages", "value": "23", "target": "_self", "parameters": null, "parent_id": "254", "order": "7", "enabled": "1", "created_at": "2019-06-07 11:23:06", "updated_at": "2024-09-05 11:32:52"}, {"id": "78", "menu_id": "1", "name": "Seder Plate", "class": "App\\Classes\\Categories", "value": "81", "target": "_self", "parameters": null, "parent_id": "338", "order": "3", "enabled": "1", "created_at": "2019-06-07 11:24:36", "updated_at": "2020-03-25 18:45:16"}, {"id": "79", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>es", "class": "App\\Classes\\Categories", "value": "82", "target": "_self", "parameters": null, "parent_id": "338", "order": "5", "enabled": "1", "created_at": "2019-06-07 11:24:53", "updated_at": "2020-03-25 18:45:16"}, {"id": "80", "menu_id": "1", "name": "Pillow Cases", "class": "App\\Classes\\Categories", "value": "83", "target": "_self", "parameters": null, "parent_id": "338", "order": "8", "enabled": "1", "created_at": "2019-06-07 11:25:11", "updated_at": "2020-03-25 18:45:16"}, {"id": "81", "menu_id": "1", "name": "Afikomen Bags", "class": "App\\Classes\\Categories", "value": "84", "target": "_self", "parameters": null, "parent_id": "338", "order": "9", "enabled": "0", "created_at": "2019-06-07 11:25:27", "updated_at": "2020-03-25 18:45:16"}, {"id": "93", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "156", "target": "_self", "parameters": null, "parent_id": "258", "order": "1", "enabled": "1", "created_at": "2019-06-11 10:38:52", "updated_at": "2023-12-17 16:56:52"}, {"id": "94", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "134", "target": "_self", "parameters": null, "parent_id": "258", "order": "2", "enabled": "1", "created_at": "2019-06-11 10:39:07", "updated_at": "2023-12-17 16:56:52"}, {"id": "99", "menu_id": "1", "name": "<PERSON>hofar", "class": "App\\Classes\\Pages", "value": "92", "target": "_self", "parameters": null, "parent_id": "254", "order": "1", "enabled": "1", "created_at": "2019-06-11 11:01:30", "updated_at": "2020-08-23 02:09:41"}, {"id": "102", "menu_id": "1", "name": "Seforim & Books", "class": "App\\Classes\\Pages", "value": "122", "target": "_self", "parameters": null, "parent_id": "254", "order": "3", "enabled": "1", "created_at": "2019-06-11 11:02:05", "updated_at": "2022-02-16 14:43:07"}, {"id": "103", "menu_id": "1", "name": "honey & Apple Dishes", "class": "App\\Classes\\Categories", "value": "237", "target": "_self", "parameters": null, "parent_id": "254", "order": "5", "enabled": "1", "created_at": "2019-06-11 11:02:21", "updated_at": "2022-02-16 14:43:07"}, {"id": "120", "menu_id": "1", "name": "Gift Cards", "class": "App\\Classes\\Categories", "value": "118", "target": "_self", "parameters": null, "parent_id": "14", "order": "1", "enabled": "1", "created_at": "2019-06-11 14:05:34", "updated_at": "2019-06-13 12:34:04"}, {"id": "122", "menu_id": "1", "name": "Gold Stamping", "class": "App\\Classes\\Pages", "value": "30", "target": "_self", "parameters": null, "parent_id": "14", "order": "2", "enabled": "1", "created_at": "2019-06-11 14:05:59", "updated_at": "2024-09-18 12:17:19"}, {"id": "123", "menu_id": "1", "name": "Engraving", "class": "App\\Classes\\Pages", "value": "60", "target": "_self", "parameters": null, "parent_id": "14", "order": "3", "enabled": "1", "created_at": "2019-06-11 14:06:14", "updated_at": "2024-09-18 12:17:30"}, {"id": "124", "menu_id": "1", "name": "Plates", "class": "App\\Classes\\Pages", "value": "60", "target": "_self", "parameters": null, "parent_id": "14", "order": "4", "enabled": "1", "created_at": "2019-06-11 14:06:25", "updated_at": "2024-09-18 12:17:42"}, {"id": "125", "menu_id": "1", "name": "Pens", "class": "App\\Classes\\Categories", "value": "123", "target": "_self", "parameters": null, "parent_id": "14", "order": "5", "enabled": "1", "created_at": "2019-06-11 14:06:33", "updated_at": "2019-12-23 16:40:38"}, {"id": "129", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "291", "target": "_self", "parameters": null, "parent_id": "251", "order": "3", "enabled": "1", "created_at": "2019-06-13 12:34:52", "updated_at": "2024-03-04 02:41:22"}, {"id": "130", "menu_id": "1", "name": "Talmud Sets", "class": "App\\Classes\\Categories", "value": "127", "target": "_self", "parameters": null, "parent_id": "251", "order": "2", "enabled": "1", "created_at": "2019-06-13 12:35:10", "updated_at": "2024-03-04 02:41:22"}, {"id": "131", "menu_id": "1", "name": "Mesivtas", "class": "App\\Classes\\Categories", "value": "128", "target": "_self", "parameters": null, "parent_id": "251", "order": "8", "enabled": "0", "created_at": "2019-06-13 12:35:46", "updated_at": "2024-03-04 02:41:22"}, {"id": "132", "menu_id": "1", "name": "Mefarshim", "class": "App\\Classes\\Pages", "value": "55", "target": "_self", "parameters": null, "parent_id": "251", "order": "4", "enabled": "1", "created_at": "2019-06-13 12:35:59", "updated_at": "2024-03-04 02:41:22"}, {"id": "133", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "130", "target": "_self", "parameters": null, "parent_id": "251", "order": "5", "enabled": "1", "created_at": "2019-06-13 12:36:10", "updated_at": "2024-03-04 02:41:22"}, {"id": "134", "menu_id": "1", "name": "Chumash", "class": "App\\Classes\\Pages", "value": "159", "target": "_self", "parameters": null, "parent_id": "271", "order": "1", "enabled": "1", "created_at": "2019-06-13 12:37:04", "updated_at": "2023-03-02 14:08:26"}, {"id": "135", "menu_id": "1", "name": "Nach", "class": "App\\Classes\\Pages", "value": "89", "target": "_self", "parameters": null, "parent_id": "271", "order": "2", "enabled": "1", "created_at": "2019-06-13 12:37:17", "updated_at": "2020-09-02 09:38:58"}, {"id": "137", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "134", "target": "_self", "parameters": null, "parent_id": "252", "order": "1", "enabled": "1", "created_at": "2019-06-13 12:38:00", "updated_at": "2019-09-20 10:09:05"}, {"id": "138", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "135", "target": "_self", "parameters": null, "parent_id": "252", "order": "2", "enabled": "0", "created_at": "2019-06-13 12:38:14", "updated_at": "2020-01-16 15:37:18"}, {"id": "139", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "136", "target": "_self", "parameters": null, "parent_id": "252", "order": "4", "enabled": "0", "created_at": "2019-06-13 12:38:23", "updated_at": "2020-01-16 15:37:11"}, {"id": "140", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "149", "target": "_self", "parameters": null, "parent_id": "252", "order": "5", "enabled": "1", "created_at": "2019-06-13 12:38:40", "updated_at": "2020-01-16 15:37:09"}, {"id": "141", "menu_id": "1", "name": "Kabbalah", "class": "App\\Classes\\Pages", "value": "137", "target": "_self", "parameters": null, "parent_id": "272", "order": "2", "enabled": "1", "created_at": "2019-06-13 12:40:05", "updated_at": "2023-04-19 15:59:20"}, {"id": "142", "menu_id": "1", "name": "Midrash", "class": "App\\Classes\\Categories", "value": "138", "target": "_self", "parameters": null, "parent_id": "272", "order": "4", "enabled": "1", "created_at": "2019-06-13 12:40:16", "updated_at": "2019-12-23 23:01:42"}, {"id": "143", "menu_id": "1", "name": "Mussar & Machshava", "class": "App\\Classes\\Categories", "value": "139", "target": "_self", "parameters": null, "parent_id": "272", "order": "1", "enabled": "1", "created_at": "2019-06-13 12:40:35", "updated_at": "2019-12-23 23:01:42"}, {"id": "145", "menu_id": "1", "name": "Siddurim", "class": "App\\Classes\\Categories", "value": "141", "target": "_self", "parameters": null, "parent_id": "253", "order": "1", "enabled": "1", "created_at": "2019-06-13 12:41:04", "updated_at": "2019-12-30 14:09:48"}, {"id": "146", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "142", "target": "_self", "parameters": null, "parent_id": "253", "order": "3", "enabled": "1", "created_at": "2019-06-13 12:54:01", "updated_at": "2020-01-02 14:50:57"}, {"id": "147", "menu_id": "1", "name": "Machzorim", "class": "App\\Classes\\Pages", "value": "80", "target": "_self", "parameters": null, "parent_id": "253", "order": "7", "enabled": "1", "created_at": "2019-06-13 12:54:22", "updated_at": "2020-06-23 07:18:15"}, {"id": "148", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "144", "target": "_self", "parameters": null, "parent_id": "253", "order": "8", "enabled": "1", "created_at": "2019-06-13 12:55:01", "updated_at": "2020-01-07 11:24:20"}, {"id": "149", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "136", "target": "_self", "parameters": null, "parent_id": "253", "order": "5", "enabled": "1", "created_at": "2019-06-13 12:55:11", "updated_at": "2020-06-08 14:40:58"}, {"id": "150", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "146", "target": "_self", "parameters": null, "parent_id": "251", "order": "9", "enabled": "0", "created_at": "2019-06-13 12:57:47", "updated_at": "2024-03-04 02:41:22"}, {"id": "151", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "147", "target": "_self", "parameters": null, "parent_id": "251", "order": "10", "enabled": "0", "created_at": "2019-06-13 12:58:01", "updated_at": "2024-03-04 02:41:22"}, {"id": "152", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "148", "target": "_self", "parameters": null, "parent_id": "272", "order": "3", "enabled": "1", "created_at": "2019-06-13 12:58:21", "updated_at": "2019-12-23 23:01:42"}, {"id": "154", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "31", "target": "_self", "parameters": null, "parent_id": "10", "order": "6", "enabled": "1", "created_at": "2019-06-13 13:45:40", "updated_at": "2022-02-16 14:46:15"}, {"id": "155", "menu_id": "1", "name": "Washing Cups", "class": "App\\Classes\\Pages", "value": "110", "target": "_self", "parameters": null, "parent_id": "10", "order": "5", "enabled": "1", "created_at": "2019-06-13 13:45:58", "updated_at": "2022-02-16 14:46:15"}, {"id": "156", "menu_id": "1", "name": "Shabbos & Yom Tov", "class": "App\\Classes\\Pages", "value": "72", "target": "_self", "parameters": null, "parent_id": "10", "order": "1", "enabled": "1", "created_at": "2019-06-13 13:46:19", "updated_at": "2020-06-23 11:34:44"}, {"id": "157", "menu_id": "1", "name": "Challah Boards", "class": "App\\Classes\\Categories", "value": "155", "target": "_self", "parameters": null, "parent_id": "156", "order": "12", "enabled": "1", "created_at": "2019-06-13 13:46:32", "updated_at": "2022-02-14 17:42:07"}, {"id": "158", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "354", "target": "_self", "parameters": null, "parent_id": "156", "order": "11", "enabled": "1", "created_at": "2019-06-13 13:46:45", "updated_at": "2022-02-14 17:42:07"}, {"id": "159", "menu_id": "1", "name": "<PERSON><PERSON> Covers", "class": "App\\Classes\\Categories", "value": "157", "target": "_self", "parameters": null, "parent_id": "156", "order": "13", "enabled": "1", "created_at": "2019-06-13 13:47:13", "updated_at": "2022-02-14 17:42:07"}, {"id": "160", "menu_id": "1", "name": "Kiddush Cups", "class": "App\\Classes\\Pages", "value": "144", "target": "_self", "parameters": null, "parent_id": "156", "order": "9", "enabled": "1", "created_at": "2019-06-13 13:56:27", "updated_at": "2022-05-23 15:59:53"}, {"id": "161", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>ers", "class": "App\\Classes\\Categories", "value": "159", "target": "_self", "parameters": null, "parent_id": "156", "order": "7", "enabled": "1", "created_at": "2019-06-13 13:56:37", "updated_at": "2022-02-14 17:42:07"}, {"id": "162", "menu_id": "1", "name": "Bencher Holders", "class": "App\\Classes\\Categories", "value": "160", "target": "_self", "parameters": null, "parent_id": "156", "order": "15", "enabled": "1", "created_at": "2019-06-13 13:56:52", "updated_at": "2022-02-14 17:42:07"}, {"id": "163", "menu_id": "1", "name": "Salt and Pepper Shakers", "class": "App\\Classes\\Categories", "value": "161", "target": "_self", "parameters": null, "parent_id": "156", "order": "8", "enabled": "1", "created_at": "2019-06-13 13:57:04", "updated_at": "2022-02-14 17:42:07"}, {"id": "164", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "162", "target": "_self", "parameters": null, "parent_id": "156", "order": "14", "enabled": "1", "created_at": "2019-06-13 13:57:16", "updated_at": "2022-02-14 17:42:07"}, {"id": "166", "menu_id": "1", "name": "Candlesticks", "class": "App\\Classes\\Categories", "value": "164", "target": "_self", "parameters": null, "parent_id": "156", "order": "1", "enabled": "1", "created_at": "2019-06-13 13:57:43", "updated_at": "2020-03-12 11:22:05"}, {"id": "167", "menu_id": "1", "name": "Candelabras", "class": "App\\Classes\\Categories", "value": "165", "target": "_self", "parameters": null, "parent_id": "156", "order": "2", "enabled": "1", "created_at": "2019-06-13 13:58:07", "updated_at": "2020-03-12 11:22:05"}, {"id": "169", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON> Sets", "class": "App\\Classes\\Categories", "value": "167", "target": "_self", "parameters": null, "parent_id": "156", "order": "16", "enabled": "1", "created_at": "2019-06-13 13:58:33", "updated_at": "2022-02-14 17:42:07"}, {"id": "171", "menu_id": "1", "name": "Besomim", "class": "App\\Classes\\Categories", "value": "169", "target": "_self", "parameters": null, "parent_id": "156", "order": "17", "enabled": "1", "created_at": "2019-06-13 13:59:09", "updated_at": "2022-02-14 17:42:07"}, {"id": "179", "menu_id": "1", "name": "Candles", "class": "App\\Classes\\Pages", "value": "33", "target": "_self", "parameters": null, "parent_id": "10", "order": "3", "enabled": "1", "created_at": "2019-06-13 14:01:42", "updated_at": "2022-02-16 14:46:15"}, {"id": "180", "menu_id": "1", "name": "Tzedakah Boxes", "class": "App\\Classes\\Categories", "value": "177", "target": "_self", "parameters": null, "parent_id": "156", "order": "6", "enabled": "1", "created_at": "2019-06-13 14:02:00", "updated_at": "2022-02-14 17:42:07"}, {"id": "194", "menu_id": "1", "name": "Children's Music", "class": "App\\Classes\\AutomatedCategories", "value": "171", "target": "_self", "parameters": null, "parent_id": "13", "order": "6", "enabled": "1", "created_at": "2019-06-13 14:34:52", "updated_at": "2020-06-17 15:54:39"}, {"id": "195", "menu_id": "1", "name": "Toys", "class": "App\\Classes\\Categories", "value": "190", "target": "_self", "parameters": null, "parent_id": "13", "order": "1", "enabled": "1", "created_at": "2019-06-13 14:35:01", "updated_at": "2019-12-23 16:40:42"}, {"id": "196", "menu_id": "1", "name": "Games & Card games", "class": "App\\Classes\\AutomatedCategories", "value": "70", "target": "_self", "parameters": null, "parent_id": "13", "order": "3", "enabled": "1", "created_at": "2019-06-13 14:35:24", "updated_at": "2020-04-23 14:48:02"}, {"id": "197", "menu_id": "1", "name": "Arts & Crafts", "class": "App\\Classes\\Pages", "value": "123", "target": "_self", "parameters": null, "parent_id": "13", "order": "4", "enabled": "1", "created_at": "2019-06-13 14:35:35", "updated_at": "2021-08-23 16:46:35"}, {"id": "199", "menu_id": "1", "name": "Education", "class": "App\\Classes\\Categories", "value": "338", "target": "_self", "parameters": null, "parent_id": "13", "order": "5", "enabled": "1", "created_at": "2019-06-13 14:36:12", "updated_at": "2020-04-22 18:17:56"}, {"id": "200", "menu_id": "1", "name": "Dating and Marriage", "class": "App\\Classes\\Categories", "value": "195", "target": "_self", "parameters": null, "parent_id": "283", "order": "3", "enabled": "1", "created_at": "2019-06-14 11:32:32", "updated_at": "2020-01-16 15:36:51"}, {"id": "204", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "157", "target": "_self", "parameters": null, "parent_id": "258", "order": "3", "enabled": "1", "created_at": "2019-06-18 14:18:49", "updated_at": "2023-12-17 16:56:52"}, {"id": "206", "menu_id": "1", "name": "Women's", "class": "App\\Classes\\Categories", "value": "201", "target": "_self", "parameters": null, "parent_id": "283", "order": "8", "enabled": "1", "created_at": "2019-06-20 11:36:36", "updated_at": "2020-01-16 15:36:51"}, {"id": "214", "menu_id": "1", "name": "Tanach Books", "class": "App\\Classes\\AutomatedCategories", "value": "406", "target": "_self", "parameters": null, "parent_id": "284", "order": "5", "enabled": "1", "created_at": "2019-06-20 13:48:31", "updated_at": "2022-05-19 10:39:59"}, {"id": "216", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "211", "target": "_self", "parameters": null, "parent_id": "259", "order": "3", "enabled": "1", "created_at": "2019-06-20 14:10:37", "updated_at": "2020-05-17 02:30:10"}, {"id": "217", "menu_id": "1", "name": "Prayer", "class": "App\\Classes\\Categories", "value": "209", "target": "_self", "parameters": null, "parent_id": "284", "order": "4", "enabled": "1", "created_at": "2019-06-20 14:10:54", "updated_at": "2019-12-25 13:05:59"}, {"id": "218", "menu_id": "1", "name": "Health", "class": "App\\Classes\\Categories", "value": "212", "target": "_self", "parameters": null, "parent_id": "283", "order": "4", "enabled": "1", "created_at": "2019-06-20 15:20:52", "updated_at": "2020-01-16 15:36:51"}, {"id": "219", "menu_id": "1", "name": "Reference", "class": "App\\Classes\\Categories", "value": "213", "target": "_self", "parameters": null, "parent_id": "250", "order": "1", "enabled": "1", "created_at": "2019-06-24 09:49:56", "updated_at": "2021-01-10 12:02:09"}, {"id": "220", "menu_id": "1", "name": "Infants to Toddlers", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/kids/205?filters=Age_Baby%2FTodder&page=1", "target": "_self", "parameters": null, "parent_id": "249", "order": "2", "enabled": "1", "created_at": "2019-06-25 11:39:48", "updated_at": "2024-09-18 12:18:59"}, {"id": "221", "menu_id": "1", "name": "Kids", "class": "App\\Classes\\AutomatedCategories", "value": "171", "target": "_self", "parameters": null, "parent_id": "249", "order": "3", "enabled": "1", "created_at": "2019-06-25 11:39:57", "updated_at": "2022-05-26 10:26:47"}, {"id": "222", "menu_id": "1", "name": "Young Readers", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/kids/205?filters=Age_Young%20Reader", "target": "_self", "parameters": null, "parent_id": "249", "order": "4", "enabled": "1", "created_at": "2019-06-25 11:40:10", "updated_at": "2024-09-18 12:19:20"}, {"id": "224", "menu_id": "1", "name": "Comics", "class": "App\\Classes\\Categories", "value": "203", "target": "_self", "parameters": null, "parent_id": "249", "order": "5", "enabled": "1", "created_at": "2019-06-25 11:40:31", "updated_at": "2022-05-26 10:26:47"}, {"id": "226", "menu_id": "1", "name": "Historical Fiction", "class": "App\\Classes\\Categories", "value": "214", "target": "_self", "parameters": null, "parent_id": "250", "order": "3", "enabled": "1", "created_at": "2019-06-26 16:00:14", "updated_at": "2021-01-10 12:02:15"}, {"id": "227", "menu_id": "1", "name": "Books Category", "class": "App\\Classes\\Pages", "value": "96", "target": "_self", "parameters": null, "parent_id": "4", "order": "10", "enabled": "1", "created_at": "2019-06-26 16:00:27", "updated_at": "2022-05-11 16:17:42"}, {"id": "230", "menu_id": "1", "name": "Videos & Plays", "class": "App\\Classes\\Categories", "value": "221", "target": "_self", "parameters": null, "parent_id": "12", "order": "6", "enabled": "1", "created_at": "2019-07-08 12:40:35", "updated_at": "2020-06-23 11:28:52"}, {"id": "232", "menu_id": "1", "name": "Concert DVD", "class": "App\\Classes\\AutomatedCategories", "value": "172", "target": "_self", "parameters": null, "parent_id": "12", "order": "4", "enabled": "1", "created_at": "2019-07-08 12:41:06", "updated_at": "2020-06-23 11:30:22"}, {"id": "236", "menu_id": "1", "name": "General <PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "90", "target": "_self", "parameters": null, "parent_id": "252", "order": "6", "enabled": "1", "created_at": "2019-07-10 14:51:26", "updated_at": "2020-09-10 11:44:50"}, {"id": "237", "menu_id": "1", "name": "Tanach Commentaries", "class": "App\\Classes\\Pages", "value": "86", "target": "_self", "parameters": null, "parent_id": "271", "order": "3", "enabled": "1", "created_at": "2019-07-10 15:22:10", "updated_at": "2020-09-02 10:10:10"}, {"id": "239", "menu_id": "1", "name": "Trays", "class": "App\\Classes\\Categories", "value": "231", "target": "_self", "parameters": null, "parent_id": "156", "order": "4", "enabled": "1", "created_at": "2019-07-26 11:28:11", "updated_at": "2022-02-14 17:42:07"}, {"id": "240", "menu_id": "1", "name": "<PERSON>ris <PERSON> & Pidyon HaBen", "class": "App\\Classes\\Categories", "value": "232", "target": "_self", "parameters": null, "parent_id": "283", "order": "2", "enabled": "1", "created_at": "2019-08-20 10:47:48", "updated_at": "2020-01-16 15:36:51"}, {"id": "242", "menu_id": "1", "name": "Match Boxes", "class": "App\\Classes\\Categories", "value": "233", "target": "_self", "parameters": null, "parent_id": "156", "order": "5", "enabled": "1", "created_at": "2019-08-21 16:53:09", "updated_at": "2022-02-14 17:42:07"}, {"id": "243", "menu_id": "1", "name": "Kiddush Wine Fountain", "class": "App\\Classes\\Categories", "value": "234", "target": "_self", "parameters": null, "parent_id": "156", "order": "10", "enabled": "1", "created_at": "2019-09-04 10:15:38", "updated_at": "2022-02-14 17:42:07"}, {"id": "245", "menu_id": "1", "name": "Cookbooks", "class": "App\\Classes\\Pages", "value": "15", "target": "_self", "parameters": null, "parent_id": "4", "order": "2", "enabled": "1", "created_at": "2019-09-20 09:44:49", "updated_at": "2021-01-10 12:07:13"}, {"id": "246", "menu_id": "1", "name": "History", "class": "App\\Classes\\Categories", "value": "20", "target": "_self", "parameters": null, "parent_id": "250", "order": "4", "enabled": "1", "created_at": "2019-09-20 09:46:42", "updated_at": "2021-01-10 12:02:09"}, {"id": "247", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "343", "target": "_self", "parameters": null, "parent_id": "283", "order": "9", "enabled": "1", "created_at": "2019-09-20 09:49:06", "updated_at": "2021-11-03 23:35:31"}, {"id": "248", "menu_id": "1", "name": "Children's Books", "class": "App\\Classes\\Pages", "value": "127", "target": "_self", "parameters": null, "parent_id": "4", "order": "1", "enabled": "1", "created_at": "2019-09-20 10:02:36", "updated_at": "2021-10-21 13:06:57"}, {"id": "249", "menu_id": "1", "name": "Children's Books", "class": "App\\Classes\\Pages", "value": "18", "target": "_self", "parameters": null, "parent_id": "13", "order": "7", "enabled": "1", "created_at": "2019-09-20 10:02:48", "updated_at": "2020-01-16 15:36:09"}, {"id": "250", "menu_id": "1", "name": "Literature", "class": "App\\Classes\\Pages", "value": "19", "target": "_self", "parameters": null, "parent_id": "4", "order": "6", "enabled": "1", "created_at": "2019-09-20 10:04:17", "updated_at": "2022-05-11 16:17:41"}, {"id": "251", "menu_id": "1", "name": "Gemara & Mishna", "class": "App\\Classes\\Pages", "value": "20", "target": "_self", "parameters": null, "parent_id": "9", "order": "2", "enabled": "1", "created_at": "2019-09-20 10:07:56", "updated_at": "2024-03-04 02:41:22"}, {"id": "252", "menu_id": "1", "name": "Halacha & Minhagim", "class": "App\\Classes\\Pages", "value": "21", "target": "_self", "parameters": null, "parent_id": "9", "order": "5", "enabled": "1", "created_at": "2019-09-20 10:08:08", "updated_at": "2024-03-04 02:41:22"}, {"id": "253", "menu_id": "1", "name": "Siddurim and Tefillah", "class": "App\\Classes\\Pages", "value": "22", "target": "_self", "parameters": null, "parent_id": "9", "order": "8", "enabled": "1", "created_at": "2019-09-20 10:08:21", "updated_at": "2024-03-04 02:41:22"}, {"id": "254", "menu_id": "1", "name": "Tishrei", "class": "App\\Classes\\Pages", "value": "27", "target": "_self", "parameters": null, "parent_id": "1220", "order": "1", "enabled": "1", "created_at": "2019-09-20 10:10:51", "updated_at": "2023-09-26 12:41:26"}, {"id": "258", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "26", "target": "_self", "parameters": null, "parent_id": "11", "order": "4", "enabled": "1", "created_at": "2019-09-20 10:11:42", "updated_at": "2024-03-25 17:04:22"}, {"id": "259", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "28", "target": "_self", "parameters": null, "parent_id": "11", "order": "6", "enabled": "1", "created_at": "2019-09-20 10:11:50", "updated_at": "2024-03-25 17:03:44"}, {"id": "264", "menu_id": "1", "name": "Shtenders", "class": "App\\Classes\\Pages", "value": "120", "target": "_self", "parameters": null, "parent_id": "259", "order": "6", "enabled": "1", "created_at": "2019-09-20 10:29:26", "updated_at": "2021-08-18 13:02:39"}, {"id": "266", "menu_id": "1", "name": "Minhagim", "class": "App\\Classes\\Categories", "value": "244", "target": "_self", "parameters": null, "parent_id": "252", "order": "8", "enabled": "1", "created_at": "2019-11-22 12:13:40", "updated_at": "2020-01-16 15:37:09"}, {"id": "267", "menu_id": "1", "name": "Cookware", "class": "App\\Classes\\Pages", "value": "81", "target": "_self", "parameters": null, "parent_id": "10", "order": "7", "enabled": "1", "created_at": "2019-11-25 11:11:23", "updated_at": "2022-02-16 14:46:15"}, {"id": "268", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "87", "target": "_self", "parameters": null, "parent_id": "9", "order": "6", "enabled": "1", "created_at": "2019-11-25 12:52:57", "updated_at": "2024-03-04 02:41:22"}, {"id": "270", "menu_id": "1", "name": "Puzzles", "class": "App\\Classes\\AutomatedCategories", "value": "67", "target": "_self", "parameters": null, "parent_id": "13", "order": "2", "enabled": "1", "created_at": "2019-12-02 14:21:40", "updated_at": "2020-04-22 11:40:39"}, {"id": "271", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "47", "target": "_self", "parameters": null, "parent_id": "9", "order": "9", "enabled": "1", "created_at": "2019-12-09 12:42:38", "updated_at": "2024-03-04 02:41:23"}, {"id": "272", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>, Mac<PERSON>hava, & Chassidus", "class": "App\\Classes\\Pages", "value": "48", "target": "_self", "parameters": null, "parent_id": "9", "order": "7", "enabled": "1", "created_at": "2019-12-09 12:45:03", "updated_at": "2024-03-04 02:41:22"}, {"id": "273", "menu_id": "1", "name": "Midrash", "class": "App\\Classes\\Categories", "value": "138", "target": "_self", "parameters": null, "parent_id": "271", "order": "4", "enabled": "1", "created_at": "2019-12-09 12:45:54", "updated_at": "2019-12-23 15:21:44"}, {"id": "275", "menu_id": "1", "name": "Rambam", "class": "App\\Classes\\Categories", "value": "254", "target": "_self", "parameters": null, "parent_id": "252", "order": "7", "enabled": "1", "created_at": "2019-12-10 10:46:51", "updated_at": "2020-01-16 15:37:09"}, {"id": "276", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "49", "target": "_self", "parameters": null, "parent_id": "9", "order": "4", "enabled": "0", "created_at": "2019-12-10 10:49:24", "updated_at": "2024-03-04 02:41:22"}, {"id": "277", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "255", "target": "_self", "parameters": null, "parent_id": "251", "order": "11", "enabled": "0", "created_at": "2019-12-10 10:49:50", "updated_at": "2024-03-04 02:41:22"}, {"id": "279", "menu_id": "1", "name": "Gifts", "class": "App\\Classes\\Pages", "value": "131", "target": "_self", "parameters": null, "parent_id": "10", "order": "8", "enabled": "1", "created_at": "2019-12-23 15:20:45", "updated_at": "2022-02-16 14:46:15"}, {"id": "280", "menu_id": "1", "name": "Honey & Apple Dishes", "class": "App\\Classes\\Categories", "value": "237", "target": "_self", "parameters": null, "parent_id": "156", "order": "18", "enabled": "1", "created_at": "2019-12-23 23:01:11", "updated_at": "2022-02-14 17:42:07"}, {"id": "281", "menu_id": "1", "name": "Drush", "class": "App\\Classes\\Categories", "value": "258", "target": "_self", "parameters": null, "parent_id": "272", "order": "5", "enabled": "1", "created_at": "2019-12-24 12:07:20", "updated_at": "2019-12-30 12:37:19"}, {"id": "282", "menu_id": "1", "name": "Toldos & Sippurim", "class": "App\\Classes\\Categories", "value": "259", "target": "_self", "parameters": null, "parent_id": "9", "order": "10", "enabled": "1", "created_at": "2019-12-24 15:09:50", "updated_at": "2024-03-04 02:41:23"}, {"id": "283", "menu_id": "1", "name": "Jewish Life & Events", "class": "App\\Classes\\Pages", "value": "51", "target": "_self", "parameters": null, "parent_id": "4", "order": "4", "enabled": "1", "created_at": "2019-12-25 13:04:27", "updated_at": "2022-05-10 14:15:49"}, {"id": "284", "menu_id": "1", "name": "Torah & Tefillah", "class": "App\\Classes\\Pages", "value": "50", "target": "_self", "parameters": null, "parent_id": "4", "order": "7", "enabled": "1", "created_at": "2019-12-25 13:04:41", "updated_at": "2022-05-11 16:17:42"}, {"id": "285", "menu_id": "1", "name": "All Music", "class": "App\\Classes\\Categories", "value": "260", "target": "_self", "parameters": null, "parent_id": "12", "order": "1", "enabled": "1", "created_at": "2019-12-26 13:22:16", "updated_at": "2020-06-16 00:58:17"}, {"id": "286", "menu_id": "1", "name": "Classes and Shiurim", "class": "App\\Classes\\AutomatedCategories", "value": "186", "target": "_self", "parameters": null, "parent_id": "12", "order": "5", "enabled": "1", "created_at": "2019-12-26 13:23:02", "updated_at": "2020-07-27 12:11:03"}, {"id": "287", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "82", "target": "_self", "parameters": null, "parent_id": "253", "order": "4", "enabled": "1", "created_at": "2019-12-30 12:43:06", "updated_at": "2020-07-07 00:08:27"}, {"id": "288", "menu_id": "1", "name": "Benchers, Tefillos & Segulos", "class": "App\\Classes\\Pages", "value": "79", "target": "_self", "parameters": null, "parent_id": "253", "order": "6", "enabled": "1", "created_at": "2019-12-30 14:09:08", "updated_at": "2022-05-10 15:30:07"}, {"id": "289", "menu_id": "1", "name": "Garment Accessories", "class": "App\\Classes\\Categories", "value": "225", "target": "_self", "parameters": null, "parent_id": "1", "order": "13", "enabled": "1", "created_at": "2019-12-30 16:15:00", "updated_at": "2022-04-28 12:26:28"}, {"id": "290", "menu_id": "1", "name": "Candles & Accessories", "class": "App\\Classes\\Pages", "value": "33", "target": "_self", "parameters": null, "parent_id": "156", "order": "3", "enabled": "1", "created_at": "2020-01-01 10:57:05", "updated_at": "2020-07-07 12:34:54"}, {"id": "299", "menu_id": "1", "name": "Children's Siddurim", "class": "App\\Classes\\AutomatedCategories", "value": "50", "target": "_self", "parameters": null, "parent_id": "253", "order": "2", "enabled": "1", "created_at": "2020-01-02 14:50:44", "updated_at": "2020-03-24 18:24:04"}, {"id": "304", "menu_id": "1", "name": "Gifts For Any Occasion", "class": "App\\Classes\\Pages", "value": "131", "target": "_self", "parameters": null, "parent_id": null, "order": "8", "enabled": "1", "created_at": "2020-01-06 14:43:28", "updated_at": "2024-03-25 17:04:04"}, {"id": "305", "menu_id": "1", "name": "Bar Mitzvah Gifts", "class": "App\\Classes\\Categories", "value": "279", "target": "_self", "parameters": null, "parent_id": "304", "order": "6", "enabled": "1", "created_at": "2020-01-06 14:43:52", "updated_at": "2021-11-14 11:57:18"}, {"id": "306", "menu_id": "1", "name": "Upsherin Gifts", "class": "App\\Classes\\Pages", "value": "71", "target": "_self", "parameters": null, "parent_id": "304", "order": "5", "enabled": "1", "created_at": "2020-01-06 14:44:05", "updated_at": "2021-11-14 11:57:18"}, {"id": "307", "menu_id": "1", "name": "Elegant Shabbos Table Gifts", "class": "App\\Classes\\Categories", "value": "280", "target": "_self", "parameters": null, "parent_id": "304", "order": "8", "enabled": "1", "created_at": "2020-01-06 14:44:57", "updated_at": "2021-11-14 11:57:18"}, {"id": "309", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "82", "target": "_self", "parameters": null, "parent_id": "304", "order": "9", "enabled": "1", "created_at": "2020-01-07 11:24:11", "updated_at": "2021-11-14 11:57:18"}, {"id": "310", "menu_id": "1", "name": "Kids", "class": "App\\Classes\\Categories", "value": "205", "target": "_self", "parameters": null, "parent_id": "248", "order": "1", "enabled": "0", "created_at": "2020-01-16 15:35:19", "updated_at": "2022-02-15 17:16:14"}, {"id": "311", "menu_id": "1", "name": "All Children's Books", "class": "App\\Classes\\AutomatedCategories", "value": "12", "target": "_self", "parameters": null, "parent_id": "249", "order": "1", "enabled": "1", "created_at": "2020-01-16 15:35:28", "updated_at": "2020-01-16 15:35:45"}, {"id": "312", "menu_id": "1", "name": "Upsherin Gifts", "class": "App\\Classes\\Pages", "value": "71", "target": "_self", "parameters": null, "parent_id": "13", "order": "8", "enabled": "1", "created_at": "2020-01-16 15:35:48", "updated_at": "2021-05-26 14:14:58"}, {"id": "313", "menu_id": "1", "name": "All Jewish Life & Events", "class": "App\\Classes\\AutomatedCategories", "value": "13", "target": "_self", "parameters": null, "parent_id": "283", "order": "1", "enabled": "1", "created_at": "2020-01-16 15:36:30", "updated_at": "2020-01-16 15:36:51"}, {"id": "314", "menu_id": "1", "name": "Tur & Shulchan Aruch", "class": "App\\Classes\\AutomatedCategories", "value": "14", "target": "_self", "parameters": null, "parent_id": "252", "order": "3", "enabled": "1", "created_at": "2020-01-16 15:36:41", "updated_at": "2020-01-16 15:37:09"}, {"id": "315", "menu_id": "1", "name": "Subscribe & Earn", "class": "App\\Classes\\AutomatedCategories", "value": "19", "target": "_self", "parameters": null, "parent_id": "251", "order": "7", "enabled": "1", "created_at": "2020-01-16 15:37:55", "updated_at": "2024-03-04 02:41:22"}, {"id": "317", "menu_id": "1", "name": "Gemara By <PERSON>chta", "class": "App\\Classes\\Pages", "value": "74", "target": "_self", "parameters": null, "parent_id": "9", "order": "1", "enabled": "1", "created_at": "2020-02-05 12:34:32", "updated_at": "2024-03-04 02:41:22"}, {"id": "318", "menu_id": "1", "name": "Waterdale Collection", "class": "App\\Classes\\Pages", "value": "116", "target": "_self", "parameters": null, "parent_id": "304", "order": "4", "enabled": "1", "created_at": "2020-02-10 12:00:51", "updated_at": "2021-11-14 11:57:18"}, {"id": "319", "menu_id": "1", "name": "Mefarshim By Mesechta", "class": "App\\Classes\\Pages", "value": "55", "target": "_self", "parameters": null, "parent_id": "251", "order": "1", "enabled": "1", "created_at": "2020-02-10 15:46:27", "updated_at": "2024-03-04 02:41:22"}, {"id": "320", "menu_id": "1", "name": "Purim Media", "class": "App\\Classes\\AutomatedCategories", "value": "350", "target": "_self", "parameters": null, "parent_id": "258", "order": "5", "enabled": "1", "created_at": "2020-02-11 11:56:21", "updated_at": "2023-12-17 16:56:52"}, {"id": "323", "menu_id": "1", "name": "Graggers", "class": "App\\Classes\\AutomatedCategories", "value": "546", "target": "_self", "parameters": null, "parent_id": "258", "order": "6", "enabled": "1", "created_at": "2020-02-13 12:13:45", "updated_at": "2024-02-14 10:01:01"}, {"id": "324", "menu_id": "1", "name": "All Purim", "class": "App\\Classes\\AutomatedCategories", "value": "17", "target": "_self", "parameters": null, "parent_id": "258", "order": "8", "enabled": "1", "created_at": "2020-02-13 12:13:57", "updated_at": "2023-12-17 16:56:52"}, {"id": "325", "menu_id": "1", "name": "<PERSON> Gifts", "class": "App\\Classes\\Categories", "value": "287", "target": "_self", "parameters": null, "parent_id": "304", "order": "7", "enabled": "1", "created_at": "2020-02-17 10:59:46", "updated_at": "2021-11-14 11:57:18"}, {"id": "327", "menu_id": "1", "name": "Children's Music", "class": "App\\Classes\\AutomatedCategories", "value": "171", "target": "_self", "parameters": null, "parent_id": "12", "order": "3", "enabled": "1", "created_at": "2020-02-28 10:27:11", "updated_at": "2020-06-23 11:30:22"}, {"id": "328", "menu_id": "1", "name": "Yiddish", "class": "App\\Classes\\Pages", "value": "62", "target": "_self", "parameters": null, "parent_id": "9", "order": "3", "enabled": "1", "created_at": "2020-03-04 10:42:49", "updated_at": "2024-03-04 02:41:22"}, {"id": "329", "menu_id": "1", "name": "Yiddish Comics", "class": "App\\Classes\\AutomatedCategories", "value": "297", "target": "_self", "parameters": null, "parent_id": "328", "order": "1", "enabled": "1", "created_at": "2020-03-04 10:44:20", "updated_at": "2021-06-17 14:57:49"}, {"id": "330", "menu_id": "1", "name": "Literature", "class": "App\\Classes\\Categories", "value": "298", "target": "_self", "parameters": null, "parent_id": "328", "order": "2", "enabled": "0", "created_at": "2020-03-04 10:45:04", "updated_at": "2020-03-23 16:34:48"}, {"id": "331", "menu_id": "1", "name": "Married Life", "class": "App\\Classes\\Pages", "value": "63", "target": "_self", "parameters": null, "parent_id": null, "order": "9", "enabled": "0", "created_at": "2020-03-05 14:40:17", "updated_at": "2024-03-25 17:04:13"}, {"id": "332", "menu_id": "1", "name": "Books on Marriage", "class": "App\\Classes\\Categories", "value": "195", "target": "_self", "parameters": null, "parent_id": "331", "order": "1", "enabled": "0", "created_at": "2020-03-05 14:41:24", "updated_at": "2022-06-26 02:11:35"}, {"id": "333", "menu_id": "1", "name": "Married Women", "class": "App\\Classes\\Categories", "value": "201", "target": "_self", "parameters": null, "parent_id": "331", "order": "2", "enabled": "0", "created_at": "2020-03-05 14:41:50", "updated_at": "2024-09-18 12:19:39"}, {"id": "334", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "26", "target": "_self", "parameters": null, "parent_id": "331", "order": "3", "enabled": "0", "created_at": "2020-03-05 14:44:35", "updated_at": "2022-06-26 02:11:50"}, {"id": "335", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "85", "target": "_self", "parameters": null, "parent_id": "338", "order": "10", "enabled": "0", "created_at": "2020-03-12 10:29:04", "updated_at": "2020-03-25 18:45:16"}, {"id": "336", "menu_id": "1", "name": "<PERSON><PERSON><PERSON> Covers", "class": "App\\Classes\\Categories", "value": "323", "target": "_self", "parameters": null, "parent_id": "338", "order": "7", "enabled": "1", "created_at": "2020-03-12 10:32:45", "updated_at": "2020-03-25 18:45:16"}, {"id": "337", "menu_id": "1", "name": "Kiddush Cups", "class": "App\\Classes\\Pages", "value": "144", "target": "_self", "parameters": null, "parent_id": "338", "order": "4", "enabled": "1", "created_at": "2020-03-12 10:41:27", "updated_at": "2023-03-16 13:46:23"}, {"id": "339", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>s", "class": "App\\Classes\\Pages", "value": "142", "target": "_self", "parameters": null, "parent_id": "338", "order": "2", "enabled": "1", "created_at": "2020-03-12 12:50:06", "updated_at": "2022-03-22 00:14:16"}, {"id": "340", "menu_id": "1", "name": "Gift Cards", "class": "App\\Classes\\Pages", "value": "45", "target": "_self", "parameters": null, "parent_id": "304", "order": "1", "enabled": "1", "created_at": "2020-03-12 14:26:29", "updated_at": "2021-11-14 11:57:05"}, {"id": "341", "menu_id": "1", "name": "Toys & games", "class": "App\\Classes\\Pages", "value": "70", "target": "_self", "parameters": null, "parent_id": "304", "order": "3", "enabled": "1", "created_at": "2020-03-12 15:06:33", "updated_at": "2021-11-14 11:57:13"}, {"id": "342", "menu_id": "1", "name": "For The Pesach Table", "class": "App\\Classes\\AutomatedCategories", "value": "30", "target": "_self", "parameters": null, "parent_id": "338", "order": "1", "enabled": "1", "created_at": "2020-03-12 15:45:22", "updated_at": "2020-03-19 15:38:27"}, {"id": "344", "menu_id": "1", "name": "Washing Cups", "class": "App\\Classes\\Pages", "value": "110", "target": "_self", "parameters": null, "parent_id": "338", "order": "6", "enabled": "1", "created_at": "2020-03-12 16:35:56", "updated_at": "2021-03-07 23:37:08"}, {"id": "346", "menu_id": "1", "name": "Yiddish Mussar", "class": "App\\Classes\\AutomatedCategories", "value": "34", "target": "_self", "parameters": null, "parent_id": "328", "order": "3", "enabled": "1", "created_at": "2020-03-23 16:30:45", "updated_at": "2020-03-23 16:30:57"}, {"id": "347", "menu_id": "1", "name": "Yiddish Toldos & Sippurim", "class": "App\\Classes\\AutomatedCategories", "value": "35", "target": "_self", "parameters": null, "parent_id": "328", "order": "4", "enabled": "1", "created_at": "2020-03-23 16:33:00", "updated_at": "2020-03-23 16:33:12"}, {"id": "348", "menu_id": "1", "name": "Yiddish Kids Books", "class": "App\\Classes\\AutomatedCategories", "value": "36", "target": "_self", "parameters": null, "parent_id": "328", "order": "5", "enabled": "1", "created_at": "2020-03-23 16:34:20", "updated_at": "2020-03-23 16:34:48"}, {"id": "349", "menu_id": "1", "name": "Yiddish Siddurim", "class": "App\\Classes\\AutomatedCategories", "value": "37", "target": "_self", "parameters": null, "parent_id": "328", "order": "6", "enabled": "0", "created_at": "2020-03-23 16:34:33", "updated_at": "2020-05-04 01:20:29"}, {"id": "350", "menu_id": "1", "name": "Yiddish Chassidus", "class": "App\\Classes\\AutomatedCategories", "value": "39", "target": "_self", "parameters": null, "parent_id": "328", "order": "7", "enabled": "1", "created_at": "2020-03-23 16:35:50", "updated_at": "2020-03-23 16:36:22"}, {"id": "351", "menu_id": "1", "name": "Yiddish Biographies", "class": "App\\Classes\\AutomatedCategories", "value": "40", "target": "_self", "parameters": null, "parent_id": "328", "order": "8", "enabled": "1", "created_at": "2020-03-23 16:36:05", "updated_at": "2020-03-23 16:36:26"}, {"id": "352", "menu_id": "1", "name": "Yiddish Novels", "class": "App\\Classes\\AutomatedCategories", "value": "49", "target": "_self", "parameters": null, "parent_id": "328", "order": "9", "enabled": "1", "created_at": "2020-03-23 16:37:46", "updated_at": "2021-06-17 17:37:34"}, {"id": "353", "menu_id": "1", "name": "Yiddish Halacha", "class": "App\\Classes\\AutomatedCategories", "value": "47", "target": "_self", "parameters": null, "parent_id": "328", "order": "10", "enabled": "1", "created_at": "2020-03-23 16:37:56", "updated_at": "2020-03-23 16:38:16"}, {"id": "356", "menu_id": "1", "name": "Set Your Table", "class": "App\\Classes\\Pages", "value": "72", "target": "_self", "parameters": null, "parent_id": "11", "order": "7", "enabled": "1", "created_at": "2020-04-19 23:35:50", "updated_at": "2024-05-01 12:53:37"}, {"id": "357", "menu_id": "1", "name": "Candlesticks", "class": "App\\Classes\\Categories", "value": "164", "target": "_self", "parameters": null, "parent_id": "356", "order": "1", "enabled": "1", "created_at": "2020-04-19 23:35:50", "updated_at": "2020-04-19 23:35:50"}, {"id": "358", "menu_id": "1", "name": "Candelabras", "class": "App\\Classes\\Categories", "value": "165", "target": "_self", "parameters": null, "parent_id": "356", "order": "2", "enabled": "1", "created_at": "2020-04-19 23:35:50", "updated_at": "2020-04-19 23:35:50"}, {"id": "359", "menu_id": "1", "name": "Candles & Accessories", "class": "App\\Classes\\Pages", "value": "33", "target": "_self", "parameters": null, "parent_id": "356", "order": "3", "enabled": "1", "created_at": "2020-04-19 23:35:50", "updated_at": "2024-09-11 13:28:59"}, {"id": "360", "menu_id": "1", "name": "Shabbos & Yom Tov Candles", "class": "App\\Classes\\AutomatedCategories", "value": "177", "target": "_self", "parameters": null, "parent_id": "359", "order": "1", "enabled": "1", "created_at": "2020-04-19 23:35:50", "updated_at": "2024-09-11 13:21:24"}, {"id": "361", "menu_id": "1", "name": "Oil & Paraffin", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Accessories_Oil%20%26%20Paraffin", "target": "_self", "parameters": null, "parent_id": "359", "order": "2", "enabled": "1", "created_at": "2020-04-19 23:35:50", "updated_at": "2024-09-11 13:24:37"}, {"id": "362", "menu_id": "1", "name": "Pre-Filled Oil & Paraffin", "class": "App\\Classes\\Categories", "value": "272", "target": "_self", "parameters": null, "parent_id": "359", "order": "3", "enabled": "0", "created_at": "2020-04-19 23:35:50", "updated_at": "2024-09-11 13:25:07"}, {"id": "363", "menu_id": "1", "name": "Oil & Candle Glasses", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Accessories_Oil%20%26%20Candle%20Glasses", "target": "_self", "parameters": null, "parent_id": "359", "order": "4", "enabled": "1", "created_at": "2020-04-19 23:35:50", "updated_at": "2024-09-11 13:25:29"}, {"id": "364", "menu_id": "1", "name": "Wicks & Holders", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Accessories_Wicks%20%26%20Holders", "target": "_self", "parameters": null, "parent_id": "359", "order": "5", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2024-09-11 13:25:47"}, {"id": "365", "menu_id": "1", "name": "Lighters & Matches", "class": "App\\Classes\\Categories", "value": "277", "target": "_self", "parameters": null, "parent_id": "359", "order": "6", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2020-04-19 23:35:51"}, {"id": "366", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Type_Havdalah", "target": "_self", "parameters": null, "parent_id": "359", "order": "7", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2024-09-11 13:26:09"}, {"id": "367", "menu_id": "1", "name": "Yahrzeit Candles", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Type_Yahrzeit", "target": "_self", "parameters": null, "parent_id": "359", "order": "8", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2024-09-11 13:26:54"}, {"id": "368", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Type_<PERSON><PERSON>h", "target": "_self", "parameters": null, "parent_id": "359", "order": "9", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2024-09-11 13:27:38"}, {"id": "369", "menu_id": "1", "name": "Trays", "class": "App\\Classes\\Categories", "value": "231", "target": "_self", "parameters": null, "parent_id": "356", "order": "4", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2020-04-19 23:35:51"}, {"id": "370", "menu_id": "1", "name": "Match Boxes", "class": "App\\Classes\\Categories", "value": "233", "target": "_self", "parameters": null, "parent_id": "356", "order": "5", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2020-04-19 23:35:51"}, {"id": "371", "menu_id": "1", "name": "Tzedakah Boxes", "class": "App\\Classes\\Categories", "value": "177", "target": "_self", "parameters": null, "parent_id": "356", "order": "6", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2020-04-19 23:35:51"}, {"id": "372", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>ers", "class": "App\\Classes\\Categories", "value": "159", "target": "_self", "parameters": null, "parent_id": "356", "order": "7", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2020-04-19 23:35:51"}, {"id": "373", "menu_id": "1", "name": "Salt and Pepper Shakers", "class": "App\\Classes\\Categories", "value": "161", "target": "_self", "parameters": null, "parent_id": "356", "order": "8", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2020-04-19 23:35:51"}, {"id": "374", "menu_id": "1", "name": "Kiddush Cups", "class": "App\\Classes\\Categories", "value": "158", "target": "_self", "parameters": null, "parent_id": "356", "order": "9", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2020-04-19 23:35:51"}, {"id": "375", "menu_id": "1", "name": "Kiddush Wine Fountain", "class": "App\\Classes\\Categories", "value": "234", "target": "_self", "parameters": null, "parent_id": "356", "order": "10", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2020-04-19 23:35:51"}, {"id": "376", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "354", "target": "_self", "parameters": null, "parent_id": "356", "order": "11", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2024-09-11 13:29:22"}, {"id": "377", "menu_id": "1", "name": "Challah Boards", "class": "App\\Classes\\Categories", "value": "155", "target": "_self", "parameters": null, "parent_id": "356", "order": "12", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2020-04-19 23:35:51"}, {"id": "378", "menu_id": "1", "name": "<PERSON><PERSON> Covers", "class": "App\\Classes\\Categories", "value": "157", "target": "_self", "parameters": null, "parent_id": "356", "order": "13", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2020-04-19 23:35:51"}, {"id": "379", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "162", "target": "_self", "parameters": null, "parent_id": "356", "order": "14", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2020-04-19 23:35:51"}, {"id": "380", "menu_id": "1", "name": "Bencher Holders", "class": "App\\Classes\\Categories", "value": "160", "target": "_self", "parameters": null, "parent_id": "356", "order": "15", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2020-04-19 23:35:51"}, {"id": "381", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON> Sets", "class": "App\\Classes\\Categories", "value": "167", "target": "_self", "parameters": null, "parent_id": "356", "order": "16", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2020-04-19 23:35:51"}, {"id": "382", "menu_id": "1", "name": "Besomim", "class": "App\\Classes\\Categories", "value": "169", "target": "_self", "parameters": null, "parent_id": "356", "order": "17", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2020-04-19 23:35:51"}, {"id": "383", "menu_id": "1", "name": "Honey & Apple Dishes", "class": "App\\Classes\\Categories", "value": "237", "target": "_self", "parameters": null, "parent_id": "356", "order": "18", "enabled": "1", "created_at": "2020-04-19 23:35:51", "updated_at": "2020-04-19 23:35:51"}, {"id": "384", "menu_id": "1", "name": "Candles & Accessories", "class": "App\\Classes\\Pages", "value": "52", "target": "_self", "parameters": null, "parent_id": "259", "order": "7", "enabled": "1", "created_at": "2020-04-26 19:49:16", "updated_at": "2020-05-17 02:30:10"}, {"id": "385", "menu_id": "1", "name": "Shabbos & Yom Tov Candles", "class": "App\\Classes\\AutomatedCategories", "value": "177", "target": "_self", "parameters": null, "parent_id": "384", "order": "1", "enabled": "1", "created_at": "2020-04-26 19:49:16", "updated_at": "2024-09-18 12:08:20"}, {"id": "386", "menu_id": "1", "name": "Oil & Paraffin", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Accessories_Oil%20%26%20Paraffin", "target": "_self", "parameters": null, "parent_id": "384", "order": "2", "enabled": "1", "created_at": "2020-04-26 19:49:16", "updated_at": "2024-09-18 12:11:12"}, {"id": "387", "menu_id": "1", "name": "Pre-Filled Oil & Paraffin", "class": "App\\Classes\\Categories", "value": "272", "target": "_self", "parameters": null, "parent_id": "384", "order": "3", "enabled": "0", "created_at": "2020-04-26 19:49:16", "updated_at": "2024-09-18 12:11:48"}, {"id": "388", "menu_id": "1", "name": "Oil & Candle Glasses", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Accessories_Oil%20%26%20Candle%20Glasses", "target": "_self", "parameters": null, "parent_id": "384", "order": "4", "enabled": "1", "created_at": "2020-04-26 19:49:16", "updated_at": "2024-09-18 12:11:56"}, {"id": "389", "menu_id": "1", "name": "Wicks & Holders", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Accessories_Wicks%20%26%20Holders", "target": "_self", "parameters": null, "parent_id": "384", "order": "5", "enabled": "1", "created_at": "2020-04-26 19:49:16", "updated_at": "2024-09-18 12:12:31"}, {"id": "390", "menu_id": "1", "name": "Lighters & Matches", "class": "App\\Classes\\Categories", "value": "277", "target": "_self", "parameters": null, "parent_id": "384", "order": "6", "enabled": "0", "created_at": "2020-04-26 19:49:16", "updated_at": "2022-05-24 12:11:18"}, {"id": "391", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "168", "target": "_self", "parameters": null, "parent_id": "384", "order": "7", "enabled": "0", "created_at": "2020-04-26 19:49:16", "updated_at": "2022-05-24 12:11:18"}, {"id": "392", "menu_id": "1", "name": "Yahrzeit Candles", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Type_Yahrzeit", "target": "_self", "parameters": null, "parent_id": "384", "order": "8", "enabled": "1", "created_at": "2020-04-26 19:49:16", "updated_at": "2024-09-18 12:13:39"}, {"id": "393", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "276", "target": "_self", "parameters": null, "parent_id": "384", "order": "9", "enabled": "0", "created_at": "2020-04-26 19:49:16", "updated_at": "2022-05-24 12:11:18"}, {"id": "394", "menu_id": "1", "name": "Set Your Table", "class": "App\\Classes\\Pages", "value": "72", "target": "_self", "parameters": null, "parent_id": "259", "order": "8", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-05-17 02:30:10"}, {"id": "395", "menu_id": "1", "name": "Candlesticks", "class": "App\\Classes\\Categories", "value": "164", "target": "_self", "parameters": null, "parent_id": "394", "order": "1", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:05"}, {"id": "396", "menu_id": "1", "name": "Candelabras", "class": "App\\Classes\\Categories", "value": "165", "target": "_self", "parameters": null, "parent_id": "394", "order": "2", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:05"}, {"id": "397", "menu_id": "1", "name": "Candles & Accessories", "class": "App\\Classes\\Pages", "value": "52", "target": "_self", "parameters": null, "parent_id": "394", "order": "3", "enabled": "0", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:47"}, {"id": "398", "menu_id": "1", "name": "Shabbos & Yom Tov Candles", "class": "App\\Classes\\AutomatedCategories", "value": "177", "target": "_self", "parameters": null, "parent_id": "397", "order": "1", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2024-09-18 12:09:25"}, {"id": "399", "menu_id": "1", "name": "Oil & Paraffin", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Accessories_Oil%20%26%20Paraffin", "target": "_self", "parameters": null, "parent_id": "397", "order": "2", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2024-09-18 12:09:51"}, {"id": "400", "menu_id": "1", "name": "Pre-Filled Oil & Paraffin", "class": "App\\Classes\\Categories", "value": "272", "target": "_self", "parameters": null, "parent_id": "397", "order": "3", "enabled": "0", "created_at": "2020-04-26 19:51:05", "updated_at": "2024-09-18 12:10:08"}, {"id": "401", "menu_id": "1", "name": "Oil & Candle Glasses", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Accessories_Oil%20%26%20Candle%20Glasses", "target": "_self", "parameters": null, "parent_id": "397", "order": "4", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2024-09-18 12:11:42"}, {"id": "402", "menu_id": "1", "name": "Wicks & Holders", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Accessories_Wicks%20%26%20Holders", "target": "_self", "parameters": null, "parent_id": "397", "order": "5", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2024-09-18 12:12:21"}, {"id": "403", "menu_id": "1", "name": "Lighters & Matches", "class": "App\\Classes\\Categories", "value": "277", "target": "_self", "parameters": null, "parent_id": "397", "order": "6", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2022-05-24 13:29:12"}, {"id": "404", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Type_Havdalah", "target": "_self", "parameters": null, "parent_id": "397", "order": "7", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2024-09-18 12:12:56"}, {"id": "405", "menu_id": "1", "name": "Yahrzeit Candles", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Type_Yahrzeit", "target": "_self", "parameters": null, "parent_id": "397", "order": "8", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2024-09-18 12:13:31"}, {"id": "406", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "276", "target": "_self", "parameters": null, "parent_id": "397", "order": "9", "enabled": "0", "created_at": "2020-04-26 19:51:05", "updated_at": "2024-09-18 12:13:07"}, {"id": "407", "menu_id": "1", "name": "Trays", "class": "App\\Classes\\Categories", "value": "231", "target": "_self", "parameters": null, "parent_id": "394", "order": "4", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:05"}, {"id": "408", "menu_id": "1", "name": "Match Boxes", "class": "App\\Classes\\Categories", "value": "233", "target": "_self", "parameters": null, "parent_id": "394", "order": "5", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:05"}, {"id": "409", "menu_id": "1", "name": "Tzedakah Boxes", "class": "App\\Classes\\Categories", "value": "177", "target": "_self", "parameters": null, "parent_id": "394", "order": "6", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:05"}, {"id": "410", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>ers", "class": "App\\Classes\\Categories", "value": "159", "target": "_self", "parameters": null, "parent_id": "394", "order": "7", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:05"}, {"id": "411", "menu_id": "1", "name": "Salt and Pepper Shakers", "class": "App\\Classes\\Categories", "value": "161", "target": "_self", "parameters": null, "parent_id": "394", "order": "8", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:05"}, {"id": "412", "menu_id": "1", "name": "Kiddush Cups", "class": "App\\Classes\\Categories", "value": "158", "target": "_self", "parameters": null, "parent_id": "394", "order": "9", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:05"}, {"id": "413", "menu_id": "1", "name": "Kiddush Wine Fountain", "class": "App\\Classes\\Categories", "value": "234", "target": "_self", "parameters": null, "parent_id": "394", "order": "10", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:05"}, {"id": "414", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "354", "target": "_self", "parameters": null, "parent_id": "394", "order": "11", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2024-09-18 12:14:05"}, {"id": "415", "menu_id": "1", "name": "Challah Boards", "class": "App\\Classes\\Categories", "value": "155", "target": "_self", "parameters": null, "parent_id": "394", "order": "12", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:05"}, {"id": "416", "menu_id": "1", "name": "<PERSON><PERSON> Covers", "class": "App\\Classes\\Categories", "value": "157", "target": "_self", "parameters": null, "parent_id": "394", "order": "13", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:05"}, {"id": "417", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "162", "target": "_self", "parameters": null, "parent_id": "394", "order": "14", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:05"}, {"id": "418", "menu_id": "1", "name": "Bencher Holders", "class": "App\\Classes\\Categories", "value": "160", "target": "_self", "parameters": null, "parent_id": "394", "order": "15", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:05"}, {"id": "419", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON> Sets", "class": "App\\Classes\\Categories", "value": "167", "target": "_self", "parameters": null, "parent_id": "394", "order": "16", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:05"}, {"id": "420", "menu_id": "1", "name": "Besomim", "class": "App\\Classes\\Categories", "value": "169", "target": "_self", "parameters": null, "parent_id": "394", "order": "17", "enabled": "1", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:51:05"}, {"id": "421", "menu_id": "1", "name": "Honey & Apple Dishes", "class": "App\\Classes\\Categories", "value": "237", "target": "_self", "parameters": null, "parent_id": "394", "order": "18", "enabled": "0", "created_at": "2020-04-26 19:51:05", "updated_at": "2020-04-26 19:52:03"}, {"id": "424", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "71", "target": "_self", "parameters": null, "parent_id": "259", "order": "4", "enabled": "1", "created_at": "2020-05-17 02:29:21", "updated_at": "2020-05-17 02:30:10"}, {"id": "425", "menu_id": "1", "name": "Vases", "class": "App\\Classes\\AutomatedCategories", "value": "121", "target": "_self", "parameters": null, "parent_id": "259", "order": "1", "enabled": "1", "created_at": "2020-05-17 02:29:59", "updated_at": "2020-05-17 02:30:10"}, {"id": "427", "menu_id": "1", "name": "Kittels", "class": "App\\Classes\\Categories", "value": "66", "target": "_self", "parameters": null, "parent_id": "254", "order": "6", "enabled": "1", "created_at": "2020-05-24 23:09:20", "updated_at": "2022-02-16 14:43:07"}, {"id": "428", "menu_id": "1", "name": "Sets On Shas", "class": "App\\Classes\\AutomatedCategories", "value": "132", "target": "_self", "parameters": null, "parent_id": "251", "order": "6", "enabled": "1", "created_at": "2020-05-25 15:22:11", "updated_at": "2024-03-04 02:41:22"}, {"id": "429", "menu_id": "1", "name": "Chassidic Music", "class": "App\\Classes\\AutomatedCategories", "value": "176", "target": "_self", "parameters": null, "parent_id": "12", "order": "2", "enabled": "1", "created_at": "2020-06-23 11:28:45", "updated_at": "2020-06-23 11:30:19"}, {"id": "430", "menu_id": "1", "name": "Shtenders", "class": "App\\Classes\\Pages", "value": "120", "target": "_self", "parameters": null, "parent_id": "10", "order": "4", "enabled": "1", "created_at": "2020-07-08 12:25:21", "updated_at": "2022-02-16 14:46:15"}, {"id": "431", "menu_id": "1", "name": "Music on USB", "class": "App\\Classes\\AutomatedCategories", "value": "187", "target": "_self", "parameters": null, "parent_id": "12", "order": "7", "enabled": "1", "created_at": "2020-07-28 12:38:29", "updated_at": "2020-07-28 12:42:41"}, {"id": "432", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "87", "target": "_self", "parameters": null, "parent_id": "11", "order": "8", "enabled": "1", "created_at": "2020-09-02 09:52:00", "updated_at": "2024-05-01 12:53:37"}, {"id": "433", "menu_id": "1", "name": "Judaica Gifts", "class": "App\\Classes\\Pages", "value": "83", "target": "_self", "parameters": null, "parent_id": "304", "order": "2", "enabled": "1", "created_at": "2020-09-02 10:31:46", "updated_at": "2021-11-14 11:57:05"}, {"id": "434", "menu_id": "1", "name": "Wedding Department", "class": "App\\Classes\\Pages", "value": "36", "target": "_self", "parameters": null, "parent_id": "10", "order": "2", "enabled": "1", "created_at": "2020-10-15 12:35:15", "updated_at": "2020-10-15 12:35:31"}, {"id": "435", "menu_id": "1", "name": "Comics", "class": "App\\Classes\\Pages", "value": "113", "target": "_self", "parameters": null, "parent_id": "4", "order": "3", "enabled": "1", "created_at": "2021-01-10 12:02:23", "updated_at": "2022-05-10 14:15:49"}, {"id": "437", "menu_id": "1", "name": "Yiddish", "class": "App\\Classes\\Pages", "value": "62", "target": "_self", "parameters": null, "parent_id": "4", "order": "8", "enabled": "1", "created_at": "2021-01-10 12:09:02", "updated_at": "2022-05-11 16:17:42"}, {"id": "438", "menu_id": "1", "name": "Yiddish Comics", "class": "App\\Classes\\AutomatedCategories", "value": "297", "target": "_self", "parameters": null, "parent_id": "437", "order": "1", "enabled": "1", "created_at": "2021-01-10 12:09:02", "updated_at": "2021-06-01 15:04:02"}, {"id": "439", "menu_id": "1", "name": "Literature", "class": "App\\Classes\\Categories", "value": "298", "target": "_self", "parameters": null, "parent_id": "437", "order": "2", "enabled": "0", "created_at": "2021-01-10 12:09:02", "updated_at": "2021-01-10 12:09:02"}, {"id": "440", "menu_id": "1", "name": "Yiddish Mussar", "class": "App\\Classes\\AutomatedCategories", "value": "34", "target": "_self", "parameters": null, "parent_id": "437", "order": "3", "enabled": "1", "created_at": "2021-01-10 12:09:02", "updated_at": "2021-01-10 12:09:02"}, {"id": "441", "menu_id": "1", "name": "Yiddish Toldos & Sippurim", "class": "App\\Classes\\AutomatedCategories", "value": "35", "target": "_self", "parameters": null, "parent_id": "437", "order": "4", "enabled": "1", "created_at": "2021-01-10 12:09:02", "updated_at": "2021-01-10 12:09:02"}, {"id": "442", "menu_id": "1", "name": "Yiddish Kids Books", "class": "App\\Classes\\AutomatedCategories", "value": "36", "target": "_self", "parameters": null, "parent_id": "437", "order": "5", "enabled": "1", "created_at": "2021-01-10 12:09:02", "updated_at": "2021-01-10 12:09:02"}, {"id": "443", "menu_id": "1", "name": "Yiddish Siddurim", "class": "App\\Classes\\AutomatedCategories", "value": "37", "target": "_self", "parameters": null, "parent_id": "437", "order": "6", "enabled": "0", "created_at": "2021-01-10 12:09:02", "updated_at": "2021-01-10 12:09:02"}, {"id": "444", "menu_id": "1", "name": "Yiddish Chassidus", "class": "App\\Classes\\AutomatedCategories", "value": "39", "target": "_self", "parameters": null, "parent_id": "437", "order": "7", "enabled": "1", "created_at": "2021-01-10 12:09:02", "updated_at": "2021-01-10 12:09:02"}, {"id": "445", "menu_id": "1", "name": "Yiddish Biographies", "class": "App\\Classes\\AutomatedCategories", "value": "40", "target": "_self", "parameters": null, "parent_id": "437", "order": "8", "enabled": "1", "created_at": "2021-01-10 12:09:02", "updated_at": "2021-01-10 12:09:02"}, {"id": "446", "menu_id": "1", "name": "Yiddish Novels", "class": "App\\Classes\\AutomatedCategories", "value": "49", "target": "_self", "parameters": null, "parent_id": "437", "order": "10", "enabled": "1", "created_at": "2021-01-10 12:09:02", "updated_at": "2021-06-17 17:36:43"}, {"id": "447", "menu_id": "1", "name": "Yiddish Halacha", "class": "App\\Classes\\AutomatedCategories", "value": "47", "target": "_self", "parameters": null, "parent_id": "437", "order": "9", "enabled": "1", "created_at": "2021-01-10 12:09:02", "updated_at": "2021-06-17 17:34:57"}, {"id": "448", "menu_id": "1", "name": "Jewish Life & Events", "class": "App\\Classes\\Pages", "value": "51", "target": "_self", "parameters": null, "parent_id": "250", "order": "9", "enabled": "1", "created_at": "2021-01-10 12:09:43", "updated_at": "2022-05-03 13:22:53"}, {"id": "449", "menu_id": "1", "name": "All Jewish Life & Events", "class": "App\\Classes\\AutomatedCategories", "value": "13", "target": "_self", "parameters": null, "parent_id": "448", "order": "1", "enabled": "1", "created_at": "2021-01-10 12:09:44", "updated_at": "2021-01-10 12:09:44"}, {"id": "450", "menu_id": "1", "name": "<PERSON>ris <PERSON> & Pidyon HaBen", "class": "App\\Classes\\Categories", "value": "232", "target": "_self", "parameters": null, "parent_id": "448", "order": "2", "enabled": "1", "created_at": "2021-01-10 12:09:44", "updated_at": "2021-01-10 12:09:44"}, {"id": "451", "menu_id": "1", "name": "Dating and Marriage", "class": "App\\Classes\\Categories", "value": "195", "target": "_self", "parameters": null, "parent_id": "448", "order": "3", "enabled": "1", "created_at": "2021-01-10 12:09:44", "updated_at": "2021-01-10 12:09:44"}, {"id": "452", "menu_id": "1", "name": "Health", "class": "App\\Classes\\Categories", "value": "212", "target": "_self", "parameters": null, "parent_id": "448", "order": "4", "enabled": "1", "created_at": "2021-01-10 12:09:44", "updated_at": "2021-01-10 12:09:44"}, {"id": "453", "menu_id": "1", "name": "Mourning", "class": "App\\Classes\\Categories", "value": "29", "target": "_self", "parameters": null, "parent_id": "448", "order": "5", "enabled": "1", "created_at": "2021-01-10 12:09:44", "updated_at": "2021-01-10 12:09:44"}, {"id": "454", "menu_id": "1", "name": "Parenting & Chinuch", "class": "App\\Classes\\Categories", "value": "31", "target": "_self", "parameters": null, "parent_id": "448", "order": "6", "enabled": "1", "created_at": "2021-01-10 12:09:44", "updated_at": "2021-01-10 12:09:44"}, {"id": "455", "menu_id": "1", "name": "Self Help", "class": "App\\Classes\\Categories", "value": "34", "target": "_self", "parameters": null, "parent_id": "448", "order": "7", "enabled": "1", "created_at": "2021-01-10 12:09:44", "updated_at": "2021-01-10 12:09:44"}, {"id": "456", "menu_id": "1", "name": "Women's", "class": "App\\Classes\\Categories", "value": "201", "target": "_self", "parameters": null, "parent_id": "448", "order": "8", "enabled": "1", "created_at": "2021-01-10 12:09:44", "updated_at": "2021-01-10 12:09:44"}, {"id": "457", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "343", "target": "_self", "parameters": null, "parent_id": "284", "order": "6", "enabled": "1", "created_at": "2021-01-10 12:10:30", "updated_at": "2022-02-07 16:16:59"}, {"id": "469", "menu_id": "1", "name": "Wedding", "class": "App\\Classes\\Pages", "value": "36", "target": "_self", "parameters": null, "parent_id": "304", "order": "10", "enabled": "1", "created_at": "2021-03-17 15:01:24", "updated_at": "2021-11-14 11:57:13"}, {"id": "470", "menu_id": "1", "name": "Table Settings", "class": "App\\Classes\\Pages", "value": "115", "target": "_self", "parameters": null, "parent_id": "259", "order": "2", "enabled": "1", "created_at": "2021-05-03 15:20:17", "updated_at": "2021-05-03 15:20:40"}, {"id": "471", "menu_id": "1", "name": "Gemara Sets", "class": "App\\Classes\\Categories", "value": "127", "target": "_self", "parameters": null, "parent_id": "259", "order": "5", "enabled": "1", "created_at": "2021-05-03 15:21:27", "updated_at": "2021-05-03 15:21:39"}, {"id": "472", "menu_id": "1", "name": "Shop by Department", "class": "App\\Classes\\Pages", "value": "118", "target": "_self", "parameters": null, "parent_id": null, "order": "10", "enabled": "1", "created_at": "2021-07-14 15:24:20", "updated_at": "2024-06-24 20:07:59"}, {"id": "473", "menu_id": "1", "name": "Music", "class": "App\\Classes\\Pages", "value": "78", "target": "_self", "parameters": null, "parent_id": "866", "order": "1", "enabled": "1", "created_at": "2021-07-14 15:26:52", "updated_at": "2022-06-13 11:27:28"}, {"id": "474", "menu_id": "1", "name": "Waterdale", "class": "App\\Classes\\Pages", "value": "116", "target": "_self", "parameters": null, "parent_id": "971", "order": "9", "enabled": "1", "created_at": "2021-07-14 15:27:04", "updated_at": "2022-06-10 12:31:00"}, {"id": "475", "menu_id": "1", "name": "Judaica", "class": "App\\Classes\\Pages", "value": "33", "target": "_self", "parameters": null, "parent_id": "472", "order": "17", "enabled": "1", "created_at": "2021-07-14 16:04:58", "updated_at": "2023-04-05 13:10:14"}, {"id": "478", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "72", "target": "_self", "parameters": null, "parent_id": "472", "order": "9", "enabled": "1", "created_at": "2021-07-14 16:06:24", "updated_at": "2023-04-05 13:10:13"}, {"id": "479", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "82", "target": "_self", "parameters": null, "parent_id": "472", "order": "18", "enabled": "1", "created_at": "2021-07-14 16:09:08", "updated_at": "2023-04-05 13:10:15"}, {"id": "481", "menu_id": "1", "name": "Benchers, Tefillos & Segulos", "class": "App\\Classes\\Pages", "value": "79", "target": "_self", "parameters": null, "parent_id": "663", "order": "27", "enabled": "1", "created_at": "2021-07-14 16:09:32", "updated_at": "2022-06-13 11:26:30"}, {"id": "483", "menu_id": "1", "name": "New Releases", "class": "App\\Classes\\Pages", "value": "69", "target": "_self", "parameters": null, "parent_id": "472", "order": "2", "enabled": "1", "created_at": "2021-07-14 16:10:08", "updated_at": "2023-04-05 13:10:06"}, {"id": "485", "menu_id": "1", "name": "Books By Category", "class": "App\\Classes\\Pages", "value": "96", "target": "_self", "parameters": null, "parent_id": "472", "order": "3", "enabled": "1", "created_at": "2021-07-14 16:10:39", "updated_at": "2023-04-05 13:10:06"}, {"id": "487", "menu_id": "1", "name": "Toys & Games", "class": "App\\Classes\\Pages", "value": "70", "target": "_self", "parameters": null, "parent_id": "472", "order": "12", "enabled": "1", "created_at": "2021-07-14 16:14:37", "updated_at": "2023-04-05 13:10:14"}, {"id": "491", "menu_id": "1", "name": "Table Settings", "class": "App\\Classes\\Pages", "value": "115", "target": "_self", "parameters": null, "parent_id": "493", "order": "1", "enabled": "1", "created_at": "2021-07-14 16:16:16", "updated_at": "2022-06-13 11:51:12"}, {"id": "492", "menu_id": "1", "name": "Wash Cups", "class": "App\\Classes\\Pages", "value": "110", "target": "_self", "parameters": null, "parent_id": "475", "order": "3", "enabled": "1", "created_at": "2021-07-14 16:16:31", "updated_at": "2022-06-14 10:38:51"}, {"id": "493", "menu_id": "1", "name": "<PERSON>sher Kitchen", "class": "App\\Classes\\Pages", "value": "81", "target": "_self", "parameters": null, "parent_id": "472", "order": "19", "enabled": "1", "created_at": "2021-07-14 16:16:49", "updated_at": "2023-04-05 13:10:15"}, {"id": "494", "menu_id": "1", "name": "Around The Year & Holidays", "class": "App\\Classes\\Pages", "value": "6", "target": "_self", "parameters": null, "parent_id": "472", "order": "10", "enabled": "1", "created_at": "2021-07-14 16:18:51", "updated_at": "2023-04-05 13:10:13"}, {"id": "495", "menu_id": "1", "name": "<PERSON>hofars", "class": "App\\Classes\\Pages", "value": "92", "target": "_self", "parameters": null, "parent_id": "475", "order": "4", "enabled": "1", "created_at": "2021-07-14 16:19:02", "updated_at": "2022-06-14 10:38:51"}, {"id": "499", "menu_id": "1", "name": "iKippahs", "class": "App\\Classes\\Pages", "value": "107", "target": "_self", "parameters": null, "parent_id": "971", "order": "10", "enabled": "1", "created_at": "2021-07-14 16:20:01", "updated_at": "2022-06-10 12:31:12"}, {"id": "503", "menu_id": "1", "name": "All Media", "class": "App\\Classes\\Pages", "value": "7", "target": "_self", "parameters": null, "parent_id": "473", "order": "1", "enabled": "1", "created_at": "2021-07-14 16:21:06", "updated_at": "2022-06-10 12:46:37"}, {"id": "504", "menu_id": "1", "name": "Machzorim", "class": "App\\Classes\\Pages", "value": "80", "target": "_self", "parameters": null, "parent_id": "254", "order": "4", "enabled": "1", "created_at": "2021-08-06 00:35:11", "updated_at": "2022-02-16 14:43:07"}, {"id": "506", "menu_id": "1", "name": "Knives", "class": "App\\Classes\\AutomatedCategories", "value": "314", "target": "_self", "parameters": null, "parent_id": "254", "order": "2", "enabled": "1", "created_at": "2021-08-30 14:10:27", "updated_at": "2021-08-30 14:10:44"}, {"id": "507", "menu_id": "1", "name": "Set Your Table", "class": "App\\Classes\\Pages", "value": "72", "target": "_self", "parameters": null, "parent_id": "254", "order": "8", "enabled": "1", "created_at": "2021-08-30 14:12:08", "updated_at": "2022-02-16 14:43:07"}, {"id": "508", "menu_id": "1", "name": "Cookbooks", "class": "App\\Classes\\Categories", "value": "35", "target": "_self", "parameters": null, "parent_id": "245", "order": "1", "enabled": "0", "created_at": "2021-10-05 16:22:54", "updated_at": "2021-10-05 16:25:58"}, {"id": "510", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "168", "target": "_self", "parameters": null, "parent_id": "11", "order": "3", "enabled": "1", "created_at": "2021-10-14 23:00:07", "updated_at": "2023-12-17 16:56:52"}, {"id": "511", "menu_id": "1", "name": "Menorahs", "class": "App\\Classes\\Categories", "value": "89", "target": "_self", "parameters": null, "parent_id": "510", "order": "1", "enabled": "1", "created_at": "2021-10-14 23:00:07", "updated_at": "2021-10-14 23:00:07"}, {"id": "512", "menu_id": "1", "name": "Candles, Oil, and Wick", "class": "App\\Classes\\Pages", "value": "33", "target": "_self", "parameters": null, "parent_id": "510", "order": "2", "enabled": "1", "created_at": "2021-10-14 23:00:07", "updated_at": "2021-10-14 23:00:07"}, {"id": "513", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "559", "target": "_self", "parameters": null, "parent_id": "510", "order": "3", "enabled": "1", "created_at": "2021-10-14 23:00:07", "updated_at": "2024-09-18 12:07:06"}, {"id": "514", "menu_id": "1", "name": "Chanukah Accessories", "class": "App\\Classes\\Categories", "value": "88", "target": "_self", "parameters": null, "parent_id": "510", "order": "4", "enabled": "1", "created_at": "2021-10-14 23:00:07", "updated_at": "2021-10-14 23:00:07"}, {"id": "515", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "199", "target": "_self", "parameters": null, "parent_id": "510", "order": "5", "enabled": "1", "created_at": "2021-10-14 23:00:07", "updated_at": "2021-10-14 23:00:07"}, {"id": "516", "menu_id": "1", "name": "Chanukah Games & Toys", "class": "App\\Classes\\AutomatedCategories", "value": "68", "target": "_self", "parameters": null, "parent_id": "510", "order": "6", "enabled": "1", "created_at": "2021-10-14 23:00:07", "updated_at": "2021-10-14 23:00:07"}, {"id": "519", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "343", "target": "_self", "parameters": null, "parent_id": "448", "order": "9", "enabled": "1", "created_at": "2021-11-03 09:44:20", "updated_at": "2021-11-03 11:11:46"}, {"id": "520", "menu_id": "1", "name": "Books", "class": "App\\Classes\\Pages", "value": "2", "target": "_self", "parameters": null, "parent_id": "304", "order": "16", "enabled": "1", "created_at": "2021-11-14 11:56:29", "updated_at": "2021-12-05 12:41:26"}, {"id": "521", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "179", "target": "_self", "parameters": null, "parent_id": "304", "order": "15", "enabled": "1", "created_at": "2021-11-14 12:10:49", "updated_at": "2021-11-14 12:14:34"}, {"id": "522", "menu_id": "1", "name": "Cookbooks", "class": "App\\Classes\\Pages", "value": "15", "target": "_self", "parameters": null, "parent_id": "304", "order": "14", "enabled": "1", "created_at": "2021-11-14 12:10:50", "updated_at": "2021-11-14 12:13:14"}, {"id": "523", "menu_id": "1", "name": "Shtenders", "class": "App\\Classes\\Pages", "value": "120", "target": "_self", "parameters": null, "parent_id": "304", "order": "12", "enabled": "1", "created_at": "2021-11-14 12:10:52", "updated_at": "2021-11-14 12:11:52"}, {"id": "524", "menu_id": "1", "name": "Washing Cups", "class": "App\\Classes\\Pages", "value": "110", "target": "_self", "parameters": null, "parent_id": "304", "order": "11", "enabled": "1", "created_at": "2021-11-14 12:10:54", "updated_at": "2021-11-14 12:11:33"}, {"id": "525", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "31", "target": "_self", "parameters": null, "parent_id": "304", "order": "13", "enabled": "1", "created_at": "2021-11-14 12:11:36", "updated_at": "2021-11-14 12:12:24"}, {"id": "529", "menu_id": "1", "name": "Yarmulke", "class": "App\\Classes\\Categories", "value": "16", "target": "_self", "parameters": null, "parent_id": "1", "order": "1", "enabled": "0", "created_at": "2022-02-14 14:46:15", "updated_at": "2022-02-15 17:46:03"}, {"id": "531", "menu_id": "1", "name": "Jewish Thought", "class": "App\\Classes\\Categories", "value": "17", "target": "_self", "parameters": null, "parent_id": "3", "order": "1", "enabled": "0", "created_at": "2022-02-14 17:40:16", "updated_at": "2022-02-15 17:14:28"}, {"id": "532", "menu_id": "1", "name": "Candles & Accessories", "class": "App\\Classes\\Categories", "value": "217", "target": "_self", "parameters": null, "parent_id": "290", "order": "1", "enabled": "1", "created_at": "2022-02-14 17:42:02", "updated_at": "2022-02-14 17:42:22"}, {"id": "533", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "95", "target": "_self", "parameters": null, "parent_id": "94", "order": "1", "enabled": "0", "created_at": "2022-02-15 17:14:50", "updated_at": "2022-02-15 17:48:42"}, {"id": "534", "menu_id": "1", "name": "<PERSON><PERSON> and <PERSON><PERSON>in Bags", "class": "App\\Classes\\Categories", "value": "74", "target": "_self", "parameters": null, "parent_id": "1", "order": "2", "enabled": "0", "created_at": "2022-02-15 17:44:13", "updated_at": "2022-02-15 17:46:04"}, {"id": "535", "menu_id": "1", "name": "Atarahs", "class": "App\\Classes\\Categories", "value": "64", "target": "_self", "parameters": null, "parent_id": "1", "order": "5", "enabled": "0", "created_at": "2022-02-15 17:44:55", "updated_at": "2022-02-15 17:46:24"}, {"id": "536", "menu_id": "1", "name": "Talleisim", "class": "App\\Classes\\Categories", "value": "68", "target": "_self", "parameters": null, "parent_id": "1", "order": "3", "enabled": "0", "created_at": "2022-02-15 17:44:57", "updated_at": "2022-02-15 17:46:10"}, {"id": "537", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "70", "target": "_self", "parameters": null, "parent_id": "1", "order": "4", "enabled": "0", "created_at": "2022-02-15 17:45:33", "updated_at": "2022-02-15 17:46:16"}, {"id": "538", "menu_id": "1", "name": "Mezuzahs Cases", "class": "App\\Classes\\Categories", "value": "152", "target": "_self", "parameters": null, "parent_id": "154", "order": "2", "enabled": "0", "created_at": "2022-02-16 14:42:25", "updated_at": "2022-02-16 14:43:13"}, {"id": "539", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "151", "target": "_self", "parameters": null, "parent_id": "154", "order": "1", "enabled": "0", "created_at": "2022-02-16 14:42:26", "updated_at": "2022-02-16 14:43:15"}, {"id": "540", "menu_id": "1", "name": "Washing Cups", "class": "App\\Classes\\Categories", "value": "219", "target": "_self", "parameters": null, "parent_id": "155", "order": "1", "enabled": "0", "created_at": "2022-02-16 14:43:26", "updated_at": "2022-02-16 14:43:40"}, {"id": "541", "menu_id": "1", "name": "Shtenders", "class": "App\\Classes\\Categories", "value": "176", "target": "_self", "parameters": null, "parent_id": "430", "order": "1", "enabled": "0", "created_at": "2022-02-16 14:43:41", "updated_at": "2022-02-16 14:43:54"}, {"id": "542", "menu_id": "1", "name": "Wedding", "class": "App\\Classes\\Categories", "value": "174", "target": "_self", "parameters": null, "parent_id": "434", "order": "1", "enabled": "0", "created_at": "2022-02-16 14:45:57", "updated_at": "2022-02-16 14:46:15"}, {"id": "543", "menu_id": "1", "name": "Kesubos & Tenoim", "class": "App\\Classes\\Categories", "value": "175", "target": "_self", "parameters": null, "parent_id": "434", "order": "2", "enabled": "0", "created_at": "2022-02-16 14:48:18", "updated_at": "2022-02-16 14:48:44"}, {"id": "544", "menu_id": "1", "name": "Purim Seforim & Books", "class": "App\\Classes\\Categories", "value": "198", "target": "_self", "parameters": null, "parent_id": "204", "order": "1", "enabled": "0", "created_at": "2022-02-17 10:23:46", "updated_at": "2022-02-17 10:23:57"}, {"id": "550", "menu_id": "1", "name": "iKippa", "class": "App\\Classes\\Pages", "value": "107", "target": "_self", "parameters": null, "parent_id": "2", "order": "1", "enabled": "1", "created_at": "2022-04-28 12:26:18", "updated_at": "2022-04-28 12:26:49"}, {"id": "552", "menu_id": "1", "name": "Novels", "class": "App\\Classes\\Pages", "value": "147", "target": "_self", "parameters": null, "parent_id": "4", "order": "9", "enabled": "1", "created_at": "2022-05-03 13:22:29", "updated_at": "2022-05-11 16:17:42"}, {"id": "553", "menu_id": "1", "name": "Short Stories", "class": "App\\Classes\\Categories", "value": "215", "target": "_self", "parameters": null, "parent_id": "250", "order": "6", "enabled": "1", "created_at": "2022-05-03 13:22:34", "updated_at": "2022-05-03 13:22:53"}, {"id": "556", "menu_id": "1", "name": "Children's Books", "class": "App\\Classes\\Pages", "value": "127", "target": "_self", "parameters": null, "parent_id": "485", "order": "2", "enabled": "1", "created_at": "2022-05-10 14:26:18", "updated_at": "2022-05-23 16:46:53"}, {"id": "558", "menu_id": "1", "name": "Comics", "class": "App\\Classes\\Pages", "value": "113", "target": "_self", "parameters": null, "parent_id": "556", "order": "2", "enabled": "1", "created_at": "2022-05-10 14:31:02", "updated_at": "2022-05-23 16:51:01"}, {"id": "559", "menu_id": "1", "name": "Stories of Tzaddikim", "class": "App\\Classes\\AutomatedCategories", "value": "331", "target": "_self", "parameters": null, "parent_id": "556", "order": "5", "enabled": "1", "created_at": "2022-05-10 14:32:14", "updated_at": "2022-05-23 16:51:01"}, {"id": "560", "menu_id": "1", "name": "Educational Books", "class": "App\\Classes\\AutomatedCategories", "value": "334", "target": "_self", "parameters": null, "parent_id": "556", "order": "3", "enabled": "1", "created_at": "2022-05-10 14:32:16", "updated_at": "2022-05-23 16:51:01"}, {"id": "561", "menu_id": "1", "name": "English Books", "class": "App\\Classes\\AutomatedCategories", "value": "7", "target": "_self", "parameters": null, "parent_id": "568", "order": "1", "enabled": "1", "created_at": "2022-05-10 14:32:17", "updated_at": "2022-05-24 12:20:23"}, {"id": "562", "menu_id": "1", "name": "New Releases", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/kids/205?new=true", "target": "_self", "parameters": null, "parent_id": "556", "order": "1", "enabled": "1", "created_at": "2022-05-10 14:32:18", "updated_at": "2022-05-19 16:28:41"}, {"id": "563", "menu_id": "1", "name": "Yiddish Books", "class": "App\\Classes\\Pages", "value": "62", "target": "_self", "parameters": null, "parent_id": "568", "order": "3", "enabled": "1", "created_at": "2022-05-10 14:32:18", "updated_at": "2022-05-24 12:25:10"}, {"id": "564", "menu_id": "1", "name": "Facts & Features", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/kids/205?filters=Educational%20Books_Facts%20%26%20Features", "target": "_self", "parameters": null, "parent_id": "560", "order": "1", "enabled": "1", "created_at": "2022-05-10 14:43:10", "updated_at": "2022-05-19 16:29:48"}, {"id": "565", "menu_id": "1", "name": "Parsha Books", "class": "App\\Classes\\AutomatedCategories", "value": "333", "target": "_self", "parameters": null, "parent_id": "560", "order": "3", "enabled": "1", "created_at": "2022-05-10 14:43:11", "updated_at": "2022-05-10 16:49:14"}, {"id": "566", "menu_id": "1", "name": "Hebrew Books", "class": "App\\Classes\\AutomatedCategories", "value": "62", "target": "_self", "parameters": null, "parent_id": "568", "order": "2", "enabled": "1", "created_at": "2022-05-10 14:48:12", "updated_at": "2022-05-24 12:17:26"}, {"id": "568", "menu_id": "1", "name": "Books by Language", "class": "App\\Classes\\Pages", "value": "2", "target": "_self", "parameters": null, "parent_id": "485", "order": "3", "enabled": "1", "created_at": "2022-05-10 14:50:48", "updated_at": "2022-05-24 12:15:50"}, {"id": "569", "menu_id": "1", "name": "Kriah & Education", "class": "App\\Classes\\Categories", "value": "338", "target": "_self", "parameters": null, "parent_id": "560", "order": "2", "enabled": "1", "created_at": "2022-05-10 14:52:05", "updated_at": "2022-05-10 16:49:14"}, {"id": "570", "menu_id": "1", "name": "Story Time", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/kids/205?filters=Storytime_Short%20Stories", "target": "_self", "parameters": null, "parent_id": "556", "order": "6", "enabled": "1", "created_at": "2022-05-10 14:53:08", "updated_at": "2022-05-23 16:51:01"}, {"id": "571", "menu_id": "1", "name": "Short Stories", "class": "App\\Classes\\AutomatedCategories", "value": "335", "target": "_self", "parameters": null, "parent_id": "556", "order": "4", "enabled": "1", "created_at": "2022-05-10 14:54:22", "updated_at": "2022-05-23 16:51:01"}, {"id": "572", "menu_id": "1", "name": "Cookbooks", "class": "App\\Classes\\Pages", "value": "15", "target": "_self", "parameters": null, "parent_id": "485", "order": "34", "enabled": "1", "created_at": "2022-05-10 16:52:20", "updated_at": "2022-11-13 03:20:40"}, {"id": "573", "menu_id": "1", "name": "General Cookbooks", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "categories/general-cookbooks/35?filters=Cookbooks_General%20Cookbooks", "target": "_self", "parameters": null, "parent_id": "572", "order": "1", "enabled": "1", "created_at": "2022-05-10 16:52:20", "updated_at": "2022-05-10 16:54:06"}, {"id": "574", "menu_id": "1", "name": "<PERSON><PERSON> Cookbooks", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "categories/general-cookbooks/35?filters=Cookbooks_Challah", "target": "_self", "parameters": null, "parent_id": "572", "order": "2", "enabled": "1", "created_at": "2022-05-10 16:52:20", "updated_at": "2022-05-10 16:54:36"}, {"id": "575", "menu_id": "1", "name": "Pesach Cookbooks", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "categories/general-cookbooks/35?filters=Cookbooks_Pesach", "target": "_self", "parameters": null, "parent_id": "572", "order": "5", "enabled": "1", "created_at": "2022-05-10 16:52:20", "updated_at": "2022-05-10 16:56:30"}, {"id": "576", "menu_id": "1", "name": "Kids Cookbooks", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "categories/general-cookbooks/35?filters=Cookbooks_Kids", "target": "_self", "parameters": null, "parent_id": "572", "order": "4", "enabled": "1", "created_at": "2022-05-10 16:52:20", "updated_at": "2022-05-10 16:55:53"}, {"id": "577", "menu_id": "1", "name": "Healthy Cookbooks", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "categories/general-cookbooks/35?filters=Cookbooks_Healthy", "target": "_self", "parameters": null, "parent_id": "572", "order": "3", "enabled": "1", "created_at": "2022-05-10 16:52:20", "updated_at": "2022-05-10 16:55:18"}, {"id": "586", "menu_id": "1", "name": "Jewish Life & Events", "class": "App\\Classes\\Pages", "value": "51", "target": "_self", "parameters": null, "parent_id": "485", "order": "27", "enabled": "1", "created_at": "2022-05-11 15:13:12", "updated_at": "2022-11-13 03:20:40"}, {"id": "588", "menu_id": "1", "name": "<PERSON>ris <PERSON> & Pidyon HaBen", "class": "App\\Classes\\Categories", "value": "232", "target": "_self", "parameters": null, "parent_id": "485", "order": "29", "enabled": "1", "created_at": "2022-05-11 15:13:12", "updated_at": "2022-11-13 03:20:40"}, {"id": "589", "menu_id": "1", "name": "Dating and Marriage", "class": "App\\Classes\\Categories", "value": "195", "target": "_self", "parameters": null, "parent_id": "485", "order": "30", "enabled": "1", "created_at": "2022-05-11 15:13:12", "updated_at": "2022-11-13 03:20:40"}, {"id": "590", "menu_id": "1", "name": "Health", "class": "App\\Classes\\Categories", "value": "212", "target": "_self", "parameters": null, "parent_id": "485", "order": "33", "enabled": "1", "created_at": "2022-05-11 15:13:12", "updated_at": "2022-11-13 03:20:40"}, {"id": "591", "menu_id": "1", "name": "Mourning", "class": "App\\Classes\\Categories", "value": "29", "target": "_self", "parameters": null, "parent_id": "485", "order": "31", "enabled": "1", "created_at": "2022-05-11 15:13:12", "updated_at": "2022-11-13 03:20:40"}, {"id": "592", "menu_id": "1", "name": "Parenting & Chinuch", "class": "App\\Classes\\Categories", "value": "31", "target": "_self", "parameters": null, "parent_id": "485", "order": "28", "enabled": "1", "created_at": "2022-05-11 15:13:12", "updated_at": "2022-11-13 03:20:40"}, {"id": "593", "menu_id": "1", "name": "Self Help", "class": "App\\Classes\\Categories", "value": "34", "target": "_self", "parameters": null, "parent_id": "485", "order": "17", "enabled": "1", "created_at": "2022-05-11 15:13:12", "updated_at": "2022-05-24 12:27:59"}, {"id": "594", "menu_id": "1", "name": "Women's", "class": "App\\Classes\\Categories", "value": "201", "target": "_self", "parameters": null, "parent_id": "485", "order": "32", "enabled": "1", "created_at": "2022-05-11 15:13:12", "updated_at": "2022-11-13 03:20:40"}, {"id": "596", "menu_id": "1", "name": "All Literature", "class": "App\\Classes\\Pages", "value": "19", "target": "_self", "parameters": null, "parent_id": "485", "order": "7", "enabled": "1", "created_at": "2022-05-11 15:21:38", "updated_at": "2022-05-24 12:27:58"}, {"id": "597", "menu_id": "1", "name": "Reference", "class": "App\\Classes\\Categories", "value": "213", "target": "_self", "parameters": null, "parent_id": "485", "order": "16", "enabled": "1", "created_at": "2022-05-11 15:21:38", "updated_at": "2022-05-24 12:27:58"}, {"id": "598", "menu_id": "1", "name": "Biographies", "class": "App\\Classes\\Categories", "value": "26", "target": "_self", "parameters": null, "parent_id": "485", "order": "12", "enabled": "1", "created_at": "2022-05-11 15:21:38", "updated_at": "2022-05-24 12:27:58"}, {"id": "599", "menu_id": "1", "name": "Historical Fiction", "class": "App\\Classes\\Categories", "value": "214", "target": "_self", "parameters": null, "parent_id": "485", "order": "9", "enabled": "1", "created_at": "2022-05-11 15:21:38", "updated_at": "2022-05-24 12:27:58"}, {"id": "600", "menu_id": "1", "name": "History", "class": "App\\Classes\\Categories", "value": "20", "target": "_self", "parameters": null, "parent_id": "485", "order": "13", "enabled": "1", "created_at": "2022-05-11 15:21:38", "updated_at": "2022-05-24 12:27:58"}, {"id": "601", "menu_id": "1", "name": "Novels", "class": "App\\Classes\\Categories", "value": "18", "target": "_self", "parameters": null, "parent_id": "485", "order": "8", "enabled": "1", "created_at": "2022-05-11 15:21:38", "updated_at": "2022-05-24 12:27:58"}, {"id": "602", "menu_id": "1", "name": "Short Stories", "class": "App\\Classes\\Categories", "value": "215", "target": "_self", "parameters": null, "parent_id": "485", "order": "10", "enabled": "1", "created_at": "2022-05-11 15:21:38", "updated_at": "2022-05-24 12:27:58"}, {"id": "603", "menu_id": "1", "name": "Coffee Table Books", "class": "App\\Classes\\Categories", "value": "27", "target": "_self", "parameters": null, "parent_id": "485", "order": "18", "enabled": "1", "created_at": "2022-05-11 15:21:39", "updated_at": "2022-05-24 12:27:59"}, {"id": "604", "menu_id": "1", "name": "Science", "class": "App\\Classes\\Categories", "value": "33", "target": "_self", "parameters": null, "parent_id": "485", "order": "15", "enabled": "1", "created_at": "2022-05-11 15:21:39", "updated_at": "2022-05-24 12:27:58"}, {"id": "615", "menu_id": "1", "name": "All Torah & Tefillah", "class": "App\\Classes\\Pages", "value": "50", "target": "_self", "parameters": null, "parent_id": "485", "order": "19", "enabled": "1", "created_at": "2022-05-11 16:15:47", "updated_at": "2022-05-24 12:27:59"}, {"id": "617", "menu_id": "1", "name": "Mussar & Machshava", "class": "App\\Classes\\Categories", "value": "30", "target": "_self", "parameters": null, "parent_id": "485", "order": "22", "enabled": "1", "created_at": "2022-05-11 16:15:47", "updated_at": "2022-11-13 03:20:40"}, {"id": "618", "menu_id": "1", "name": "Pirkei Avos", "class": "App\\Classes\\Categories", "value": "32", "target": "_self", "parameters": null, "parent_id": "647", "order": "8", "enabled": "1", "created_at": "2022-05-11 16:15:47", "updated_at": "2022-05-12 11:22:15"}, {"id": "619", "menu_id": "1", "name": "Prayer", "class": "App\\Classes\\Categories", "value": "209", "target": "_self", "parameters": null, "parent_id": "485", "order": "24", "enabled": "1", "created_at": "2022-05-11 16:15:47", "updated_at": "2022-11-13 03:20:40"}, {"id": "620", "menu_id": "1", "name": "Tanach Books", "class": "App\\Classes\\AutomatedCategories", "value": "406", "target": "_self", "parameters": null, "parent_id": "485", "order": "20", "enabled": "1", "created_at": "2022-05-11 16:15:47", "updated_at": "2022-05-24 12:27:59"}, {"id": "621", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "343", "target": "_self", "parameters": null, "parent_id": "485", "order": "21", "enabled": "1", "created_at": "2022-05-11 16:15:47", "updated_at": "2022-05-24 12:27:59"}, {"id": "622", "menu_id": "1", "name": "Family Purity", "class": "App\\Classes\\AutomatedCategories", "value": "411", "target": "_self", "parameters": null, "parent_id": "621", "order": "3", "enabled": "1", "created_at": "2022-05-11 16:15:47", "updated_at": "2022-05-19 15:12:44"}, {"id": "624", "menu_id": "1", "name": "Shabbos", "class": "App\\Classes\\AutomatedCategories", "value": "357", "target": "_self", "parameters": null, "parent_id": "621", "order": "1", "enabled": "1", "created_at": "2022-05-11 16:15:47", "updated_at": "2022-05-19 15:12:43"}, {"id": "628", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "324", "target": "_self", "parameters": null, "parent_id": "621", "order": "2", "enabled": "1", "created_at": "2022-05-12 10:55:04", "updated_at": "2022-05-19 15:12:43"}, {"id": "629", "menu_id": "1", "name": "Prayer", "class": "App\\Classes\\AutomatedCategories", "value": "413", "target": "_self", "parameters": null, "parent_id": "621", "order": "5", "enabled": "1", "created_at": "2022-05-12 10:58:08", "updated_at": "2022-05-19 15:12:44"}, {"id": "630", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "412", "target": "_self", "parameters": null, "parent_id": "621", "order": "4", "enabled": "1", "created_at": "2022-05-12 10:58:10", "updated_at": "2022-05-19 15:12:44"}, {"id": "631", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "415", "target": "_self", "parameters": null, "parent_id": "621", "order": "7", "enabled": "1", "created_at": "2022-05-12 10:58:48", "updated_at": "2022-05-19 15:12:44"}, {"id": "635", "menu_id": "1", "name": "English Books", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "categories/kids/205?filters=Language_English", "target": "_self", "parameters": null, "parent_id": "634", "order": "1", "enabled": "1", "created_at": "2022-05-12 11:09:17", "updated_at": "2022-05-12 11:09:17"}, {"id": "636", "menu_id": "1", "name": "Hebrew Books", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "categories/kids/205?filters=Language_Hebrew", "target": "_self", "parameters": null, "parent_id": "634", "order": "2", "enabled": "1", "created_at": "2022-05-12 11:09:17", "updated_at": "2022-05-12 11:09:17"}, {"id": "637", "menu_id": "1", "name": "Yiddish Books", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "categories/kids/205?filters=Language_Yiddish", "target": "_self", "parameters": null, "parent_id": "634", "order": "3", "enabled": "1", "created_at": "2022-05-12 11:09:17", "updated_at": "2022-05-12 11:09:17"}, {"id": "640", "menu_id": "1", "name": "Facts & Features", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "categories/kids/205?filters=Educational%20Books_Facts%20%26%20Features", "target": "_self", "parameters": null, "parent_id": "639", "order": "1", "enabled": "1", "created_at": "2022-05-12 11:09:17", "updated_at": "2022-05-12 11:09:17"}, {"id": "641", "menu_id": "1", "name": "Kriah & Education", "class": "App\\Classes\\Categories", "value": "338", "target": "_self", "parameters": null, "parent_id": "639", "order": "2", "enabled": "1", "created_at": "2022-05-12 11:09:17", "updated_at": "2022-05-12 11:09:17"}, {"id": "642", "menu_id": "1", "name": "Parsha Books", "class": "App\\Classes\\AutomatedCategories", "value": "333", "target": "_self", "parameters": null, "parent_id": "639", "order": "3", "enabled": "1", "created_at": "2022-05-12 11:09:17", "updated_at": "2022-05-12 11:09:17"}, {"id": "646", "menu_id": "1", "name": "Teens", "class": "App\\Classes\\AutomatedCategories", "value": "168", "target": "_self", "parameters": null, "parent_id": "485", "order": "5", "enabled": "1", "created_at": "2022-05-12 11:09:43", "updated_at": "2022-05-24 12:28:07"}, {"id": "647", "menu_id": "1", "name": "Around the Year", "class": "App\\Classes\\AutomatedCategories", "value": "417", "target": "_self", "parameters": null, "parent_id": "485", "order": "25", "enabled": "1", "created_at": "2022-05-12 11:10:25", "updated_at": "2022-11-13 03:20:40"}, {"id": "648", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "418", "target": "_self", "parameters": null, "parent_id": "647", "order": "1", "enabled": "1", "created_at": "2022-05-12 11:12:02", "updated_at": "2022-05-19 13:47:18"}, {"id": "649", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "419", "target": "_self", "parameters": null, "parent_id": "647", "order": "2", "enabled": "1", "created_at": "2022-05-12 11:12:17", "updated_at": "2022-05-19 14:20:41"}, {"id": "650", "menu_id": "1", "name": "Sukkos", "class": "App\\Classes\\Categories", "value": "197", "target": "_self", "parameters": null, "parent_id": "647", "order": "3", "enabled": "1", "created_at": "2022-05-12 11:12:20", "updated_at": "2022-05-19 13:20:55"}, {"id": "651", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "220", "target": "_self", "parameters": null, "parent_id": "647", "order": "4", "enabled": "1", "created_at": "2022-05-12 11:12:22", "updated_at": "2022-05-19 13:44:11"}, {"id": "652", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "420", "target": "_self", "parameters": null, "parent_id": "647", "order": "5", "enabled": "1", "created_at": "2022-05-12 11:12:26", "updated_at": "2022-05-19 14:21:30"}, {"id": "653", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "374", "target": "_self", "parameters": null, "parent_id": "647", "order": "6", "enabled": "1", "created_at": "2022-05-12 11:12:29", "updated_at": "2022-05-19 13:43:33"}, {"id": "654", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "404", "target": "_self", "parameters": null, "parent_id": "647", "order": "7", "enabled": "1", "created_at": "2022-05-12 11:12:32", "updated_at": "2022-05-19 13:42:58"}, {"id": "655", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "421", "target": "_self", "parameters": null, "parent_id": "647", "order": "9", "enabled": "1", "created_at": "2022-05-12 11:12:43", "updated_at": "2022-05-19 14:26:15"}, {"id": "656", "menu_id": "1", "name": "3 Weeks & Tisha B'Av", "class": "App\\Classes\\AutomatedCategories", "value": "422", "target": "_self", "parameters": null, "parent_id": "647", "order": "10", "enabled": "1", "created_at": "2022-05-12 11:12:47", "updated_at": "2022-05-19 14:26:26"}, {"id": "657", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "416", "target": "_self", "parameters": null, "parent_id": "485", "order": "23", "enabled": "1", "created_at": "2022-05-12 11:24:22", "updated_at": "2022-11-13 03:20:40"}, {"id": "658", "menu_id": "1", "name": "Comics", "class": "App\\Classes\\Pages", "value": "113", "target": "_self", "parameters": null, "parent_id": "485", "order": "4", "enabled": "1", "created_at": "2022-05-18 16:47:41", "updated_at": "2022-05-23 16:51:01"}, {"id": "659", "menu_id": "1", "name": "Business", "class": "App\\Classes\\AutomatedCategories", "value": "414", "target": "_self", "parameters": null, "parent_id": "621", "order": "6", "enabled": "1", "created_at": "2022-05-19 12:52:54", "updated_at": "2022-05-19 15:12:44"}, {"id": "660", "menu_id": "1", "name": "Pirkei Avos", "class": "App\\Classes\\Categories", "value": "32", "target": "_self", "parameters": null, "parent_id": "485", "order": "26", "enabled": "1", "created_at": "2022-05-19 13:20:17", "updated_at": "2022-11-13 03:20:40"}, {"id": "661", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "292", "target": "_self", "parameters": null, "parent_id": "485", "order": "11", "enabled": "1", "created_at": "2022-05-19 14:27:22", "updated_at": "2022-05-24 12:27:58"}, {"id": "662", "menu_id": "1", "name": "Holocaust", "class": "App\\Classes\\Categories", "value": "295", "target": "_self", "parameters": null, "parent_id": "485", "order": "14", "enabled": "1", "created_at": "2022-05-19 14:27:52", "updated_at": "2022-05-24 12:27:58"}, {"id": "663", "menu_id": "1", "name": "Seforim By Category", "class": "App\\Classes\\Pages", "value": "75", "target": "_self", "parameters": null, "parent_id": "472", "order": "4", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2023-04-05 13:10:07"}, {"id": "664", "menu_id": "1", "name": "New Releases", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/a/categories/seforim/4?new=true", "target": "_self", "parameters": null, "parent_id": "663", "order": "1", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-25 15:45:34"}, {"id": "665", "menu_id": "1", "name": "Gemara By <PERSON>chta", "class": "App\\Classes\\Pages", "value": "74", "target": "_self", "parameters": null, "parent_id": "663", "order": "3", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-25 15:45:34"}, {"id": "666", "menu_id": "1", "name": "Mefarshim By Mesechta", "class": "App\\Classes\\Pages", "value": "55", "target": "_self", "parameters": null, "parent_id": "663", "order": "4", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-25 15:45:34"}, {"id": "667", "menu_id": "1", "name": "Talmud Sets", "class": "App\\Classes\\Categories", "value": "127", "target": "_self", "parameters": null, "parent_id": "663", "order": "7", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-25 15:45:34"}, {"id": "668", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "291", "target": "_self", "parameters": null, "parent_id": "663", "order": "6", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-25 15:45:34"}, {"id": "670", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "130", "target": "_self", "parameters": null, "parent_id": "663", "order": "9", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-25 15:45:34"}, {"id": "671", "menu_id": "1", "name": "Sets On Shas", "class": "App\\Classes\\AutomatedCategories", "value": "132", "target": "_self", "parameters": null, "parent_id": "663", "order": "8", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-25 15:45:34"}, {"id": "672", "menu_id": "1", "name": "Subscribe & Earn", "class": "App\\Classes\\AutomatedCategories", "value": "19", "target": "_self", "parameters": null, "parent_id": "663", "order": "5", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-25 15:45:34"}, {"id": "677", "menu_id": "1", "name": "Yiddish", "class": "App\\Classes\\Pages", "value": "62", "target": "_self", "parameters": null, "parent_id": "472", "order": "5", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2023-04-05 13:10:11"}, {"id": "678", "menu_id": "1", "name": "Yiddish Comics", "class": "App\\Classes\\AutomatedCategories", "value": "297", "target": "_self", "parameters": null, "parent_id": "677", "order": "1", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-19 14:31:52"}, {"id": "680", "menu_id": "1", "name": "Yiddish Mussar", "class": "App\\Classes\\AutomatedCategories", "value": "34", "target": "_self", "parameters": null, "parent_id": "677", "order": "2", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-19 14:37:49"}, {"id": "681", "menu_id": "1", "name": "Yiddish Toldos & Sippurim", "class": "App\\Classes\\AutomatedCategories", "value": "35", "target": "_self", "parameters": null, "parent_id": "677", "order": "3", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-19 14:37:49"}, {"id": "682", "menu_id": "1", "name": "Yiddish Kids Books", "class": "App\\Classes\\AutomatedCategories", "value": "36", "target": "_self", "parameters": null, "parent_id": "677", "order": "4", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-19 14:37:49"}, {"id": "683", "menu_id": "1", "name": "Yiddish Siddurim", "class": "App\\Classes\\AutomatedCategories", "value": "37", "target": "_self", "parameters": null, "parent_id": "677", "order": "5", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-19 14:37:49"}, {"id": "684", "menu_id": "1", "name": "Yiddish Chassidus", "class": "App\\Classes\\AutomatedCategories", "value": "39", "target": "_self", "parameters": null, "parent_id": "677", "order": "6", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-19 14:37:49"}, {"id": "685", "menu_id": "1", "name": "Yiddish Biographies", "class": "App\\Classes\\AutomatedCategories", "value": "40", "target": "_self", "parameters": null, "parent_id": "677", "order": "7", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-19 14:37:49"}, {"id": "686", "menu_id": "1", "name": "Yiddish Novels", "class": "App\\Classes\\AutomatedCategories", "value": "49", "target": "_self", "parameters": null, "parent_id": "677", "order": "8", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-19 14:37:49"}, {"id": "687", "menu_id": "1", "name": "Yiddish Halacha", "class": "App\\Classes\\AutomatedCategories", "value": "47", "target": "_self", "parameters": null, "parent_id": "677", "order": "9", "enabled": "1", "created_at": "2022-05-19 14:31:52", "updated_at": "2022-05-19 14:37:49"}, {"id": "690", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "134", "target": "_self", "parameters": null, "parent_id": "663", "order": "11", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-05-25 15:45:35"}, {"id": "691", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "135", "target": "_self", "parameters": null, "parent_id": "663", "order": "13", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-05-25 15:45:35"}, {"id": "692", "menu_id": "1", "name": "Tur & Shulchan Aruch", "class": "App\\Classes\\AutomatedCategories", "value": "14", "target": "_self", "parameters": null, "parent_id": "663", "order": "12", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-05-25 15:45:35"}, {"id": "693", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "136", "target": "_self", "parameters": null, "parent_id": "663", "order": "14", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-05-25 15:45:35"}, {"id": "694", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "149", "target": "_self", "parameters": null, "parent_id": "663", "order": "15", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-05-25 15:45:35"}, {"id": "696", "menu_id": "1", "name": "Rambam", "class": "App\\Classes\\Categories", "value": "254", "target": "_self", "parameters": null, "parent_id": "663", "order": "16", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-05-25 15:45:35"}, {"id": "697", "menu_id": "1", "name": "Minhagim", "class": "App\\Classes\\Categories", "value": "244", "target": "_self", "parameters": null, "parent_id": "663", "order": "17", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-06-13 10:57:24"}, {"id": "698", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "87", "target": "_self", "parameters": null, "parent_id": "663", "order": "18", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-06-13 10:57:24"}, {"id": "699", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>, Mac<PERSON>hava, & Chassidus", "class": "App\\Classes\\Pages", "value": "48", "target": "_self", "parameters": null, "parent_id": "663", "order": "20", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-06-13 10:57:24"}, {"id": "700", "menu_id": "1", "name": "Mussar & Machshava", "class": "App\\Classes\\Categories", "value": "139", "target": "_self", "parameters": null, "parent_id": "663", "order": "21", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-06-13 10:57:24"}, {"id": "701", "menu_id": "1", "name": "Kabbalah", "class": "App\\Classes\\Pages", "value": "137", "target": "_self", "parameters": null, "parent_id": "663", "order": "22", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2023-04-19 15:59:57"}, {"id": "702", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "148", "target": "_self", "parameters": null, "parent_id": "663", "order": "23", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-06-13 10:57:24"}, {"id": "703", "menu_id": "1", "name": "Midrash", "class": "App\\Classes\\Categories", "value": "138", "target": "_self", "parameters": null, "parent_id": "663", "order": "24", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-06-13 10:57:24"}, {"id": "704", "menu_id": "1", "name": "Drush", "class": "App\\Classes\\Categories", "value": "258", "target": "_self", "parameters": null, "parent_id": "663", "order": "25", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-06-13 10:57:24"}, {"id": "709", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "82", "target": "_self", "parameters": null, "parent_id": "663", "order": "28", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-06-13 11:26:30"}, {"id": "713", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "144", "target": "_self", "parameters": null, "parent_id": "663", "order": "29", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-06-13 11:26:30"}, {"id": "714", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "47", "target": "_self", "parameters": null, "parent_id": "663", "order": "30", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-06-13 11:26:30"}, {"id": "715", "menu_id": "1", "name": "Chumash", "class": "App\\Classes\\Pages", "value": "159", "target": "_self", "parameters": null, "parent_id": "663", "order": "31", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2023-03-02 14:09:49"}, {"id": "716", "menu_id": "1", "name": "Nach", "class": "App\\Classes\\Pages", "value": "89", "target": "_self", "parameters": null, "parent_id": "663", "order": "32", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-06-13 11:26:30"}, {"id": "717", "menu_id": "1", "name": "Tanach Commentaries", "class": "App\\Classes\\Pages", "value": "86", "target": "_self", "parameters": null, "parent_id": "663", "order": "33", "enabled": "1", "created_at": "2022-05-19 14:31:53", "updated_at": "2022-06-13 11:26:30"}, {"id": "720", "menu_id": "1", "name": "<PERSON><PERSON><PERSON> by Topic", "class": "App\\Classes\\Pages", "value": "90", "target": "_self", "parameters": null, "parent_id": "663", "order": "10", "enabled": "1", "created_at": "2022-05-19 14:54:19", "updated_at": "2022-05-25 15:45:34"}, {"id": "722", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Hashkomas%20Haboker", "target": "_self", "parameters": null, "parent_id": "720", "order": "1", "enabled": "1", "created_at": "2022-05-19 14:54:19", "updated_at": "2022-05-19 16:30:34"}, {"id": "723", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_T<PERSON>tzis", "target": "_self", "parameters": null, "parent_id": "720", "order": "2", "enabled": "1", "created_at": "2022-05-19 14:54:19", "updated_at": "2022-05-19 16:30:47"}, {"id": "724", "menu_id": "1", "name": "Tefillah", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Tefilah%20%2F%20Beis%20Hakneses%20%2F%20Krias%20Hatorah", "target": "_self", "parameters": null, "parent_id": "720", "order": "3", "enabled": "1", "created_at": "2022-05-19 14:54:19", "updated_at": "2022-05-19 16:31:31"}, {"id": "725", "menu_id": "1", "name": "Brachos", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Berachos", "target": "_self", "parameters": null, "parent_id": "720", "order": "4", "enabled": "1", "created_at": "2022-05-19 14:54:19", "updated_at": "2022-05-19 16:31:45"}, {"id": "726", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Seudah", "target": "_self", "parameters": null, "parent_id": "720", "order": "5", "enabled": "1", "created_at": "2022-05-19 14:54:19", "updated_at": "2022-05-19 16:31:51"}, {"id": "727", "menu_id": "1", "name": "Shabbos", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Shabbos", "target": "_self", "parameters": null, "parent_id": "720", "order": "6", "enabled": "1", "created_at": "2022-05-19 14:54:19", "updated_at": "2022-05-19 16:31:55"}, {"id": "728", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Yom%20Tov", "target": "_self", "parameters": null, "parent_id": "720", "order": "7", "enabled": "1", "created_at": "2022-05-19 14:54:19", "updated_at": "2022-05-23 00:04:04"}, {"id": "729", "menu_id": "1", "name": "Niddah & Nashim", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Niddah%20%2F%20Nashim", "target": "_self", "parameters": null, "parent_id": "720", "order": "13", "enabled": "1", "created_at": "2022-05-19 15:12:52", "updated_at": "2022-05-23 00:04:04"}, {"id": "730", "menu_id": "1", "name": "Marriage", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Nissuin%20-%20Marriage", "target": "_self", "parameters": null, "parent_id": "720", "order": "12", "enabled": "1", "created_at": "2022-05-19 15:12:53", "updated_at": "2022-05-23 00:04:04"}, {"id": "731", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Gittin", "target": "_self", "parameters": null, "parent_id": "720", "order": "11", "enabled": "1", "created_at": "2022-05-19 15:12:55", "updated_at": "2022-05-23 00:04:04"}, {"id": "732", "menu_id": "1", "name": "Bar Mitzvah & Tefillin", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Bar%20Mitzvah%20%26%20Tefillin", "target": "_self", "parameters": null, "parent_id": "720", "order": "10", "enabled": "1", "created_at": "2022-05-19 15:12:56", "updated_at": "2022-05-23 00:04:04"}, {"id": "733", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "categories/general-halacha/227?filters=Halacha%20Topics_Upsherin%20%2F%20Peias%20Harosh%20V%27Hazakan", "target": "_self", "parameters": null, "parent_id": "720", "order": "9", "enabled": "1", "created_at": "2022-05-19 15:12:57", "updated_at": "2022-05-23 00:04:04"}, {"id": "734", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Bris%20Milah", "target": "_self", "parameters": null, "parent_id": "720", "order": "8", "enabled": "1", "created_at": "2022-05-19 15:12:59", "updated_at": "2022-05-23 00:04:04"}, {"id": "735", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Aveilus", "target": "_self", "parameters": null, "parent_id": "720", "order": "16", "enabled": "1", "created_at": "2022-05-19 15:22:23", "updated_at": "2022-05-23 00:04:04"}, {"id": "736", "menu_id": "1", "name": "Mikvaos & Tahara", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "categories/general-halacha/227?filters=Halacha%20Topics_Mikvaos%20%2F%20Tahara", "target": "_self", "parameters": null, "parent_id": "720", "order": "15", "enabled": "1", "created_at": "2022-05-19 15:22:26", "updated_at": "2022-05-23 00:04:04"}, {"id": "737", "menu_id": "1", "name": "Tznius", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Tznius", "target": "_self", "parameters": null, "parent_id": "720", "order": "14", "enabled": "1", "created_at": "2022-05-19 15:22:27", "updated_at": "2022-05-23 00:04:04"}, {"id": "738", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Lashon%20Hara", "target": "_self", "parameters": null, "parent_id": "720", "order": "20", "enabled": "1", "created_at": "2022-05-19 15:34:00", "updated_at": "2022-05-23 00:04:04"}, {"id": "739", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Shechita", "target": "_self", "parameters": null, "parent_id": "720", "order": "19", "enabled": "1", "created_at": "2022-05-19 15:34:01", "updated_at": "2022-05-23 00:04:04"}, {"id": "740", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Isur%20V%27Heter", "target": "_self", "parameters": null, "parent_id": "720", "order": "18", "enabled": "1", "created_at": "2022-05-19 15:34:01", "updated_at": "2022-05-23 00:04:04"}, {"id": "741", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Kashrus", "target": "_self", "parameters": null, "parent_id": "720", "order": "17", "enabled": "1", "created_at": "2022-05-19 15:34:02", "updated_at": "2022-05-23 00:04:04"}, {"id": "742", "menu_id": "1", "name": "Sta\"m", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Sta\"m", "target": "_self", "parameters": null, "parent_id": "720", "order": "24", "enabled": "1", "created_at": "2022-05-19 15:42:18", "updated_at": "2022-05-23 00:04:04"}, {"id": "743", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Mitzvos", "target": "_self", "parameters": null, "parent_id": "720", "order": "23", "enabled": "1", "created_at": "2022-05-19 15:42:18", "updated_at": "2022-05-23 00:04:04"}, {"id": "744", "menu_id": "1", "name": "Talmud Torah", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Talmud%20Torah", "target": "_self", "parameters": null, "parent_id": "720", "order": "22", "enabled": "1", "created_at": "2022-05-19 15:42:19", "updated_at": "2022-05-23 00:04:04"}, {"id": "745", "menu_id": "1", "name": "Tzedaka", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Tzedakah", "target": "_self", "parameters": null, "parent_id": "720", "order": "21", "enabled": "1", "created_at": "2022-05-19 15:42:19", "updated_at": "2022-05-23 00:04:04"}, {"id": "746", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Geirus", "target": "_self", "parameters": null, "parent_id": "720", "order": "30", "enabled": "1", "created_at": "2022-05-19 15:46:39", "updated_at": "2022-05-23 00:04:04"}, {"id": "747", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Kiddush%20Hachodesh%20%2F%20Kiddush%20Levanah%20%2F%20Luach", "target": "_self", "parameters": null, "parent_id": "720", "order": "29", "enabled": "1", "created_at": "2022-05-19 15:46:41", "updated_at": "2022-05-23 00:04:04"}, {"id": "748", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Ribis", "target": "_self", "parameters": null, "parent_id": "720", "order": "28", "enabled": "1", "created_at": "2022-05-19 15:46:42", "updated_at": "2022-05-23 00:04:04"}, {"id": "749", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Choshen%20Mishpat%20%2F%20Dinei%20Mamonos%20%2F%20Dayanim", "target": "_self", "parameters": null, "parent_id": "720", "order": "27", "enabled": "1", "created_at": "2022-05-19 15:46:43", "updated_at": "2022-05-23 00:04:04"}, {"id": "750", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Refuah", "target": "_self", "parameters": null, "parent_id": "720", "order": "26", "enabled": "1", "created_at": "2022-05-19 15:46:43", "updated_at": "2022-05-23 00:04:04"}, {"id": "751", "menu_id": "1", "name": "Kibud Av V'Eim", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Kibud%20Av%20Vaem", "target": "_self", "parameters": null, "parent_id": "720", "order": "25", "enabled": "1", "created_at": "2022-05-19 15:46:44", "updated_at": "2022-05-23 00:04:04"}, {"id": "752", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Bikur%20Cholim", "target": "_self", "parameters": null, "parent_id": "720", "order": "32", "enabled": "1", "created_at": "2022-05-19 15:53:10", "updated_at": "2022-05-23 00:04:04"}, {"id": "753", "menu_id": "1", "name": "Avodah Zara & Kishuf", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Avodah%20Zara%20%2F%20Kishuf", "target": "_self", "parameters": null, "parent_id": "720", "order": "31", "enabled": "1", "created_at": "2022-05-19 15:53:13", "updated_at": "2022-05-23 00:04:04"}, {"id": "754", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Kilayim%20%2F%20Shemitah%20%2F%20Orlah%20%2F%20Eretz%20Yisroel", "target": "_self", "parameters": null, "parent_id": "720", "order": "33", "enabled": "1", "created_at": "2022-05-19 15:58:01", "updated_at": "2022-05-23 00:04:04"}, {"id": "755", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/general-halacha/227?filters=Halacha%20Topics_Kodashim%20%2F%20Beis%20Hamikdash%20%2F%20Kohanim%20%2F%20Matnos%20Kehuna", "target": "_self", "parameters": null, "parent_id": "720", "order": "34", "enabled": "1", "created_at": "2022-05-19 15:59:13", "updated_at": "2022-05-23 00:04:04"}, {"id": "756", "menu_id": "1", "name": "New Releases", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/a/categories/all-books/7?new=true", "target": "_self", "parameters": null, "parent_id": "485", "order": "1", "enabled": "1", "created_at": "2022-05-23 16:46:44", "updated_at": "2022-05-23 16:47:34"}, {"id": "758", "menu_id": "1", "name": "English Comics", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/comics/203?filters=Language_English", "target": "_self", "parameters": null, "parent_id": "658", "order": "1", "enabled": "1", "created_at": "2022-05-24 12:21:53", "updated_at": "2022-05-24 12:22:49"}, {"id": "759", "menu_id": "1", "name": "Yiddish Comics", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/comics/203?filters=Language_Yiddish", "target": "_self", "parameters": null, "parent_id": "658", "order": "2", "enabled": "1", "created_at": "2022-05-24 12:23:19", "updated_at": "2022-05-24 12:23:31"}, {"id": "760", "menu_id": "1", "name": "Hebrew Comics", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/comics/203?filters=Language_Hebrew", "target": "_self", "parameters": null, "parent_id": "658", "order": "3", "enabled": "1", "created_at": "2022-05-24 12:23:46", "updated_at": "2022-05-24 12:23:56"}, {"id": "761", "menu_id": "1", "name": "Series Books", "class": "App\\Classes\\Pages", "value": "91", "target": "_self", "parameters": null, "parent_id": "485", "order": "6", "enabled": "1", "created_at": "2022-05-24 12:27:29", "updated_at": "2022-05-24 12:28:07"}, {"id": "762", "menu_id": "1", "name": "Comic Series", "class": "App\\Classes\\AutomatedCategories", "value": "201", "target": "_self", "parameters": null, "parent_id": "761", "order": "1", "enabled": "1", "created_at": "2022-05-24 12:27:56", "updated_at": "2022-05-24 12:28:46"}, {"id": "763", "menu_id": "1", "name": "History And Biography Series", "class": "App\\Classes\\AutomatedCategories", "value": "208", "target": "_self", "parameters": null, "parent_id": "761", "order": "6", "enabled": "1", "created_at": "2022-05-24 12:28:50", "updated_at": "2022-05-24 12:48:08"}, {"id": "764", "menu_id": "1", "name": "Jewish Thought And Parenting Series", "class": "App\\Classes\\AutomatedCategories", "value": "207", "target": "_self", "parameters": null, "parent_id": "761", "order": "5", "enabled": "1", "created_at": "2022-05-24 12:28:52", "updated_at": "2022-05-24 12:47:39"}, {"id": "765", "menu_id": "1", "name": "Kids Series", "class": "App\\Classes\\AutomatedCategories", "value": "204", "target": "_self", "parameters": null, "parent_id": "761", "order": "4", "enabled": "1", "created_at": "2022-05-24 12:28:52", "updated_at": "2022-05-24 12:35:28"}, {"id": "766", "menu_id": "1", "name": "Short Story Series", "class": "App\\Classes\\AutomatedCategories", "value": "203", "target": "_self", "parameters": null, "parent_id": "761", "order": "3", "enabled": "1", "created_at": "2022-05-24 12:28:54", "updated_at": "2022-05-24 12:32:35"}, {"id": "767", "menu_id": "1", "name": "Young Reader Series", "class": "App\\Classes\\AutomatedCategories", "value": "202", "target": "_self", "parameters": null, "parent_id": "761", "order": "2", "enabled": "1", "created_at": "2022-05-24 12:28:55", "updated_at": "2022-05-24 12:31:54"}, {"id": "768", "menu_id": "1", "name": "Yom Tov Series", "class": "App\\Classes\\AutomatedCategories", "value": "212", "target": "_self", "parameters": null, "parent_id": "761", "order": "8", "enabled": "1", "created_at": "2022-05-24 12:35:30", "updated_at": "2022-05-24 12:49:18"}, {"id": "769", "menu_id": "1", "name": "Learning Series", "class": "App\\Classes\\AutomatedCategories", "value": "209", "target": "_self", "parameters": null, "parent_id": "761", "order": "7", "enabled": "1", "created_at": "2022-05-24 12:35:31", "updated_at": "2022-05-24 12:48:51"}, {"id": "778", "menu_id": "1", "name": "Books By Publisher", "class": "App\\Classes\\Categories", "value": "275", "target": "_self", "parameters": null, "parent_id": "485", "order": "35", "enabled": "1", "created_at": "2022-05-24 13:28:54", "updated_at": "2022-11-13 03:20:41"}, {"id": "779", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "76", "target": "_self", "parameters": null, "parent_id": "778", "order": "1", "enabled": "1", "created_at": "2022-05-24 13:29:53", "updated_at": "2022-05-24 13:30:27"}, {"id": "780", "menu_id": "1", "name": "Feldheim", "class": "App\\Classes\\Pages", "value": "97", "target": "_self", "parameters": null, "parent_id": "778", "order": "2", "enabled": "1", "created_at": "2022-05-24 13:30:30", "updated_at": "2022-05-24 13:31:00"}, {"id": "781", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "104", "target": "_self", "parameters": null, "parent_id": "778", "order": "7", "enabled": "1", "created_at": "2022-05-24 13:30:42", "updated_at": "2022-05-24 13:32:59"}, {"id": "782", "menu_id": "1", "name": "CIS Publishers", "class": "App\\Classes\\Pages", "value": "103", "target": "_self", "parameters": null, "parent_id": "778", "order": "6", "enabled": "1", "created_at": "2022-05-24 13:30:43", "updated_at": "2022-05-24 13:32:41"}, {"id": "783", "menu_id": "1", "name": "Judaica Press", "class": "App\\Classes\\Pages", "value": "102", "target": "_self", "parameters": null, "parent_id": "778", "order": "5", "enabled": "1", "created_at": "2022-05-24 13:30:43", "updated_at": "2022-05-24 13:32:16"}, {"id": "784", "menu_id": "1", "name": "Israel Bookshop Publications", "class": "App\\Classes\\Pages", "value": "99", "target": "_self", "parameters": null, "parent_id": "778", "order": "4", "enabled": "1", "created_at": "2022-05-24 13:30:45", "updated_at": "2022-05-24 13:31:56"}, {"id": "785", "menu_id": "1", "name": "<PERSON>ren & Maggid", "class": "App\\Classes\\Pages", "value": "105", "target": "_self", "parameters": null, "parent_id": "778", "order": "3", "enabled": "1", "created_at": "2022-05-24 13:30:45", "updated_at": "2022-05-24 13:31:26"}, {"id": "786", "menu_id": "1", "name": "Menucha Publishers", "class": "App\\Classes\\Pages", "value": "101", "target": "_self", "parameters": null, "parent_id": "778", "order": "8", "enabled": "1", "created_at": "2022-05-24 13:32:23", "updated_at": "2022-05-24 13:33:13"}, {"id": "787", "menu_id": "1", "name": "Gemara & Mishna", "class": "App\\Classes\\Pages", "value": "20", "target": "_self", "parameters": null, "parent_id": "663", "order": "2", "enabled": "1", "created_at": "2022-05-25 15:34:51", "updated_at": "2022-05-25 15:45:34"}, {"id": "788", "menu_id": "1", "name": "Breis<PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/chumash/131?filters=Sefer_Bereishis", "target": "_self", "parameters": null, "parent_id": "715", "order": "1", "enabled": "1", "created_at": "2022-05-25 15:44:47", "updated_at": "2024-09-18 12:22:10"}, {"id": "789", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/chumash/131?filters=Sefer_Devarim", "target": "_self", "parameters": null, "parent_id": "715", "order": "5", "enabled": "1", "created_at": "2022-05-25 15:45:31", "updated_at": "2024-09-18 12:22:58"}, {"id": "793", "menu_id": "1", "name": "Bamidbar", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/chumash/131?filters=Sefer_Bamidbar", "target": "_self", "parameters": null, "parent_id": "715", "order": "4", "enabled": "1", "created_at": "2022-05-25 15:48:05", "updated_at": "2024-09-18 12:22:45"}, {"id": "794", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/chumash/131?filters=Se<PERSON>_<PERSON><PERSON>ra", "target": "_self", "parameters": null, "parent_id": "715", "order": "3", "enabled": "1", "created_at": "2022-05-25 15:48:08", "updated_at": "2024-09-18 12:22:32"}, {"id": "795", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/chumash/131?filters=Se<PERSON>_Shemos", "target": "_self", "parameters": null, "parent_id": "715", "order": "2", "enabled": "1", "created_at": "2022-05-25 15:48:10", "updated_at": "2024-09-18 12:22:22"}, {"id": "796", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>Echad Targum", "class": "App\\Classes\\Categories", "value": "293", "target": "_self", "parameters": null, "parent_id": "663", "order": "34", "enabled": "1", "created_at": "2022-05-25 15:50:37", "updated_at": "2022-06-13 11:26:30"}, {"id": "797", "menu_id": "1", "name": "Yiddish Seforim", "class": "App\\Classes\\Pages", "value": "62", "target": "_self", "parameters": null, "parent_id": "663", "order": "37", "enabled": "1", "created_at": "2022-05-25 15:58:34", "updated_at": "2022-06-13 11:26:30"}, {"id": "798", "menu_id": "1", "name": "Pirkei Avos", "class": "App\\Classes\\AutomatedCategories", "value": "64", "target": "_self", "parameters": null, "parent_id": "663", "order": "35", "enabled": "1", "created_at": "2022-05-25 16:00:38", "updated_at": "2022-06-13 11:26:30"}, {"id": "799", "menu_id": "1", "name": "Shabbos", "class": "App\\Classes\\Categories", "value": "311", "target": "_self", "parameters": null, "parent_id": "663", "order": "36", "enabled": "1", "created_at": "2022-05-25 16:02:19", "updated_at": "2022-06-13 11:26:30"}, {"id": "800", "menu_id": "1", "name": "Tefillah Seforim", "class": "App\\Classes\\Categories", "value": "140", "target": "_self", "parameters": null, "parent_id": "663", "order": "26", "enabled": "1", "created_at": "2022-05-25 16:04:02", "updated_at": "2022-06-13 10:57:24"}, {"id": "801", "menu_id": "1", "name": "Klalim & Dikduk", "class": "App\\Classes\\Categories", "value": "308", "target": "_self", "parameters": null, "parent_id": "663", "order": "38", "enabled": "1", "created_at": "2022-05-25 16:08:55", "updated_at": "2022-06-13 11:26:31"}, {"id": "802", "menu_id": "1", "name": "Toldos & Sippurim", "class": "App\\Classes\\Categories", "value": "259", "target": "_self", "parameters": null, "parent_id": "663", "order": "39", "enabled": "1", "created_at": "2022-05-25 16:09:45", "updated_at": "2022-06-13 11:26:31"}, {"id": "805", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "87", "target": "_self", "parameters": null, "parent_id": "663", "order": "19", "enabled": "1", "created_at": "2022-05-25 16:13:35", "updated_at": "2022-06-13 10:57:24"}, {"id": "806", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yomim-tovim/246?filters=Holiday%20-%20Yom%20Tov_Rosh%20Hashanah", "target": "_self", "parameters": null, "parent_id": "805", "order": "3", "enabled": "1", "created_at": "2022-05-25 16:13:35", "updated_at": "2022-05-25 16:17:17"}, {"id": "807", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yomim-tovim/246?filters=Holiday%20-%20Yom%20Tov_Yom%20Kippur", "target": "_self", "parameters": null, "parent_id": "805", "order": "4", "enabled": "1", "created_at": "2022-05-25 16:13:35", "updated_at": "2022-05-25 16:17:46"}, {"id": "808", "menu_id": "1", "name": "Sukkos", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yomim-tovim/246?filters=Holiday%20-%20Yom%20Tov_Sukkos", "target": "_self", "parameters": null, "parent_id": "805", "order": "5", "enabled": "1", "created_at": "2022-05-25 16:13:35", "updated_at": "2022-05-25 16:18:18"}, {"id": "809", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "221", "target": "_self", "parameters": null, "parent_id": "805", "order": "6", "enabled": "1", "created_at": "2022-05-25 16:13:35", "updated_at": "2022-05-25 16:18:45"}, {"id": "810", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yomim-tovim/246?filters=Holiday%20-%20Yom%20Tov_Purim", "target": "_self", "parameters": null, "parent_id": "805", "order": "7", "enabled": "1", "created_at": "2022-05-25 16:13:35", "updated_at": "2022-05-25 16:19:29"}, {"id": "811", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yomim-tovim/246?filters=Holiday%20-%20Yom%20Tov_Pesach", "target": "_self", "parameters": null, "parent_id": "805", "order": "8", "enabled": "1", "created_at": "2022-05-25 16:13:35", "updated_at": "2022-05-25 16:23:46"}, {"id": "812", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yomim-tovim/246?filters=Holiday%20-%20Yom%20Tov_Sefirah", "target": "_self", "parameters": null, "parent_id": "805", "order": "9", "enabled": "1", "created_at": "2022-05-25 16:13:35", "updated_at": "2022-05-25 16:20:19"}, {"id": "814", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yomim-tovim/246?filters=Holiday%20-%20Yom%20Tov_Shavuos", "target": "_self", "parameters": null, "parent_id": "805", "order": "12", "enabled": "1", "created_at": "2022-05-25 16:13:35", "updated_at": "2022-05-25 16:23:37"}, {"id": "815", "menu_id": "1", "name": "3 Weeks & Tisha B'Av", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yomim-tovim/246?filters=Holiday%20-%20Yom%20Tov_Tisha%20Be%27av%20%2F%203%20Weeks", "target": "_self", "parameters": null, "parent_id": "805", "order": "13", "enabled": "1", "created_at": "2022-05-25 16:13:35", "updated_at": "2022-05-25 16:24:07"}, {"id": "816", "menu_id": "1", "name": "Shabbos", "class": "App\\Classes\\Categories", "value": "311", "target": "_self", "parameters": null, "parent_id": "805", "order": "1", "enabled": "1", "created_at": "2022-05-25 16:15:22", "updated_at": "2022-05-25 16:15:41"}, {"id": "817", "menu_id": "1", "name": "Pirkei Avos", "class": "App\\Classes\\AutomatedCategories", "value": "64", "target": "_self", "parameters": null, "parent_id": "805", "order": "10", "enabled": "1", "created_at": "2022-05-25 16:15:50", "updated_at": "2022-05-25 16:16:04"}, {"id": "818", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yomim-tovim/246?filters=Holiday%20-%20Yom%20Tov_Rosh%20Chodesh", "target": "_self", "parameters": null, "parent_id": "805", "order": "2", "enabled": "1", "created_at": "2022-05-25 16:16:37", "updated_at": "2022-05-25 16:17:26"}, {"id": "819", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yomim-tovim/246?filters=Holiday%20-%20Yom%20Tov_Lag%20Baomer", "target": "_self", "parameters": null, "parent_id": "805", "order": "11", "enabled": "1", "created_at": "2022-05-25 16:24:20", "updated_at": "2022-05-25 16:24:36"}, {"id": "820", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "13", "target": "_self", "parameters": null, "parent_id": "909", "order": "1", "enabled": "1", "created_at": "2022-05-25 16:29:46", "updated_at": "2022-06-13 11:28:29"}, {"id": "821", "menu_id": "1", "name": "New Releases", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/a/categories/all-media/21?new=true", "target": "_self", "parameters": null, "parent_id": "473", "order": "2", "enabled": "1", "created_at": "2022-05-25 16:34:00", "updated_at": "2022-06-10 12:46:19"}, {"id": "822", "menu_id": "1", "name": "Music CD's", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-music/260?filters=Music%20Type_Music%20CD&page=1", "target": "_self", "parameters": null, "parent_id": "473", "order": "3", "enabled": "1", "created_at": "2022-05-25 16:34:56", "updated_at": "2022-06-10 12:46:19"}, {"id": "823", "menu_id": "1", "name": "Music USB", "class": "App\\Classes\\AutomatedCategories", "value": "187", "target": "_self", "parameters": null, "parent_id": "473", "order": "4", "enabled": "1", "created_at": "2022-05-25 16:36:00", "updated_at": "2022-06-10 12:46:19"}, {"id": "824", "menu_id": "1", "name": "Digital Download", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-music/260?filters=Music%20Type_Digital%20Downloads&page=1", "target": "_self", "parameters": null, "parent_id": "473", "order": "5", "enabled": "1", "created_at": "2022-05-25 16:36:58", "updated_at": "2022-06-10 12:46:19"}, {"id": "825", "menu_id": "1", "name": "Children's Music", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-music/260?filters=Music%20Type_Children%27s", "target": "_self", "parameters": null, "parent_id": "473", "order": "7", "enabled": "1", "created_at": "2022-05-25 16:41:00", "updated_at": "2022-06-10 12:46:19"}, {"id": "826", "menu_id": "1", "name": "Follow Along CD's", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-music/260?filters=Music%20Type_Follow%20Along%20CD%27s&page=1", "target": "_self", "parameters": null, "parent_id": "473", "order": "6", "enabled": "1", "created_at": "2022-05-25 16:41:37", "updated_at": "2022-06-10 12:46:19"}, {"id": "827", "menu_id": "1", "name": "Chasiddish Music", "class": "App\\Classes\\AutomatedCategories", "value": "218", "target": "_self", "parameters": null, "parent_id": "473", "order": "8", "enabled": "1", "created_at": "2022-05-25 16:43:42", "updated_at": "2022-06-10 12:46:19"}, {"id": "828", "menu_id": "1", "name": "Choirs", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-music/260?filters=Music%20Type_Choirs", "target": "_self", "parameters": null, "parent_id": "473", "order": "12", "enabled": "1", "created_at": "2022-05-25 16:45:09", "updated_at": "2022-06-10 12:46:19"}, {"id": "829", "menu_id": "1", "name": "Yom Tov Music", "class": "App\\Classes\\AutomatedCategories", "value": "213", "target": "_self", "parameters": null, "parent_id": "473", "order": "10", "enabled": "1", "created_at": "2022-05-26 10:17:50", "updated_at": "2022-06-10 12:46:19"}, {"id": "830", "menu_id": "1", "name": "Shabbos Music", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-music/260?filters=Music%20Type_Shabbos", "target": "_self", "parameters": null, "parent_id": "473", "order": "11", "enabled": "1", "created_at": "2022-05-26 10:18:21", "updated_at": "2022-06-10 12:46:19"}, {"id": "831", "menu_id": "1", "name": "Concerts", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-music/260?filters=Music%20Type_Concerts", "target": "_self", "parameters": null, "parent_id": "473", "order": "14", "enabled": "1", "created_at": "2022-05-26 10:19:08", "updated_at": "2022-06-10 12:46:19"}, {"id": "832", "menu_id": "1", "name": "Sefira Music", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-music/260?filters=Music%20Type_Se<PERSON>rah", "target": "_self", "parameters": null, "parent_id": "473", "order": "9", "enabled": "1", "created_at": "2022-05-26 10:21:16", "updated_at": "2022-06-10 12:46:19"}, {"id": "833", "menu_id": "1", "name": "Children's Stories & Learning", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-music/260?filters=Music%20Type_Children%27s%20Stories%20%26%20Learning", "target": "_self", "parameters": null, "parent_id": "473", "order": "17", "enabled": "1", "created_at": "2022-05-26 10:22:03", "updated_at": "2022-06-10 12:46:19"}, {"id": "834", "menu_id": "1", "name": "Classes, Shiurim & Stories", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-music/260?filters=Music%20Type_Classes%20%26%20Shiurim", "target": "_self", "parameters": null, "parent_id": "473", "order": "18", "enabled": "1", "created_at": "2022-05-26 10:23:00", "updated_at": "2022-06-10 12:46:19"}, {"id": "835", "menu_id": "1", "name": "Educational Media", "class": "App\\Classes\\Categories", "value": "204", "target": "_self", "parameters": null, "parent_id": "866", "order": "3", "enabled": "1", "created_at": "2022-05-26 10:26:31", "updated_at": "2022-06-13 11:28:05"}, {"id": "836", "menu_id": "1", "name": "Classes, Shiurim & Stories", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-music/260?filters=Music%20Type_Classes%20%26%20Shiurim", "target": "_self", "parameters": null, "parent_id": "835", "order": "2", "enabled": "1", "created_at": "2022-05-26 10:27:05", "updated_at": "2022-05-26 10:27:33"}, {"id": "837", "menu_id": "1", "name": "Children's Stories & Learning", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-music/260?filters=Music%20Type_Children%27s%20Stories%20%26%20Learning", "target": "_self", "parameters": null, "parent_id": "835", "order": "1", "enabled": "1", "created_at": "2022-05-26 10:27:14", "updated_at": "2022-05-26 10:27:27"}, {"id": "838", "menu_id": "1", "name": "Educational Videos", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/videos-plays/221?filters=Video%20Type_Educational&page=1", "target": "_self", "parameters": null, "parent_id": "835", "order": "3", "enabled": "1", "created_at": "2022-05-26 10:28:03", "updated_at": "2022-05-26 10:28:19"}, {"id": "839", "menu_id": "1", "name": "Videos & Plays", "class": "App\\Classes\\Categories", "value": "221", "target": "_self", "parameters": null, "parent_id": "866", "order": "2", "enabled": "1", "created_at": "2022-05-26 10:29:07", "updated_at": "2022-06-13 11:27:33"}, {"id": "840", "menu_id": "1", "name": "English Videos", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/videos-plays/221?filters=Language_English", "target": "_self", "parameters": null, "parent_id": "839", "order": "1", "enabled": "1", "created_at": "2022-05-26 10:29:49", "updated_at": "2022-05-26 10:31:01"}, {"id": "841", "menu_id": "1", "name": "Children's Videos", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/videos-plays/221?filters=Video%20Type_Children%27s", "target": "_self", "parameters": null, "parent_id": "839", "order": "3", "enabled": "1", "created_at": "2022-05-26 10:30:03", "updated_at": "2022-05-26 10:33:44"}, {"id": "842", "menu_id": "1", "name": "Yiddish Videos", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/videos-plays/221?filters=Language_Yiddish", "target": "_self", "parameters": null, "parent_id": "839", "order": "2", "enabled": "1", "created_at": "2022-05-26 10:30:13", "updated_at": "2022-05-26 10:31:25"}, {"id": "843", "menu_id": "1", "name": "Plays", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/videos-plays/221?filters=Video%20Type_Plays", "target": "_self", "parameters": null, "parent_id": "839", "order": "6", "enabled": "1", "created_at": "2022-05-26 10:33:50", "updated_at": "2022-05-26 10:36:22"}, {"id": "844", "menu_id": "1", "name": "Interen Plays", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/videos-plays/221?filters=Video%20Type_Interen%20Plays", "target": "_self", "parameters": null, "parent_id": "839", "order": "7", "enabled": "1", "created_at": "2022-05-26 10:34:21", "updated_at": "2022-05-26 10:36:22"}, {"id": "845", "menu_id": "1", "name": "Women & Girls", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/videos-plays/221?filters=Video%20Type_Female%20Vocalists,Video%20Type_Plays%20For%20Women&page=1", "target": "_self", "parameters": null, "parent_id": "839", "order": "5", "enabled": "1", "created_at": "2022-05-26 10:35:02", "updated_at": "2022-05-26 10:38:46"}, {"id": "846", "menu_id": "1", "name": "Concerts", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/videos-plays/221?filters=Video%20Type_Concerts", "target": "_self", "parameters": null, "parent_id": "839", "order": "8", "enabled": "1", "created_at": "2022-05-26 10:36:49", "updated_at": "2022-05-26 10:37:07"}, {"id": "847", "menu_id": "1", "name": "Educational Videos", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/videos-plays/221?filters=Video%20Type_Educational&page=1", "target": "_self", "parameters": null, "parent_id": "839", "order": "10", "enabled": "1", "created_at": "2022-05-26 10:37:26", "updated_at": "2022-05-26 10:37:40"}, {"id": "848", "menu_id": "1", "name": "Women & Girls", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "https://www.shopeichlers.com/categories/all-music/260?filters=Music%20Type_Female%20Vocalist&page=1", "target": "_self", "parameters": null, "parent_id": "473", "order": "13", "enabled": "1", "created_at": "2022-05-26 10:39:34", "updated_at": "2022-06-10 12:46:19"}, {"id": "849", "menu_id": "1", "name": "Animated Videos", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/videos-plays/221?filters=Video%20Type_Animation&page=1", "target": "_self", "parameters": null, "parent_id": "839", "order": "4", "enabled": "1", "created_at": "2022-05-26 10:44:28", "updated_at": "2022-05-26 10:44:43"}, {"id": "850", "menu_id": "1", "name": "Israeli Music", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-music/260?filters=Music%20Type_Israeli&page=1", "target": "_self", "parameters": null, "parent_id": "473", "order": "15", "enabled": "1", "created_at": "2022-05-26 10:46:03", "updated_at": "2022-06-10 12:46:19"}, {"id": "851", "menu_id": "1", "name": "Instrumental Music", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-music/260?filters=Music%20Type_Instrumental&page=1", "target": "_self", "parameters": null, "parent_id": "473", "order": "16", "enabled": "1", "created_at": "2022-05-26 10:51:33", "updated_at": "2022-06-10 12:46:19"}, {"id": "852", "menu_id": "1", "name": "Comedy", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/videos-plays/221?filters=Video%20Type_Comedies&page=1", "target": "_self", "parameters": null, "parent_id": "839", "order": "9", "enabled": "1", "created_at": "2022-05-26 10:59:24", "updated_at": "2022-05-26 11:00:12"}, {"id": "853", "menu_id": "1", "name": "Yarmulkes", "class": "App\\Classes\\Pages", "value": "77", "target": "_self", "parameters": null, "parent_id": "909", "order": "2", "enabled": "1", "created_at": "2022-05-26 11:02:49", "updated_at": "2022-06-13 11:28:29"}, {"id": "854", "menu_id": "1", "name": "All Tzitzis", "class": "App\\Classes\\Categories", "value": "70", "target": "_self", "parameters": null, "parent_id": "820", "order": "1", "enabled": "1", "created_at": "2022-05-26 11:03:14", "updated_at": "2022-05-26 11:03:51"}, {"id": "855", "menu_id": "1", "name": "<PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/tzitzis/70?filters=Material_Cotton&page=1", "target": "_self", "parameters": null, "parent_id": "820", "order": "3", "enabled": "1", "created_at": "2022-05-26 11:03:45", "updated_at": "2022-05-26 11:05:39"}, {"id": "856", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/tzitzis/70?filters=Material_Wool&page=1", "target": "_self", "parameters": null, "parent_id": "820", "order": "4", "enabled": "1", "created_at": "2022-05-26 11:05:43", "updated_at": "2022-05-26 11:06:29"}, {"id": "857", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/tzitzis/70?filters=Material_Mesh&page=1", "target": "_self", "parameters": null, "parent_id": "820", "order": "5", "enabled": "1", "created_at": "2022-05-26 11:06:34", "updated_at": "2022-05-26 11:07:06"}, {"id": "858", "menu_id": "1", "name": "T-shirt <PERSON><PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/tzitzis/70?filters=Tzitzis%20Neck_T-Shirt", "target": "_self", "parameters": null, "parent_id": "820", "order": "6", "enabled": "1", "created_at": "2022-05-26 11:07:24", "updated_at": "2022-05-26 11:07:47"}, {"id": "859", "menu_id": "1", "name": "Ashkenaz", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/tzitzis/70?filters=Tzitzis%20Minhag_Ashkenaz", "target": "_self", "parameters": null, "parent_id": "820", "order": "7", "enabled": "1", "created_at": "2022-05-26 11:07:51", "updated_at": "2022-05-26 11:09:01"}, {"id": "860", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/tzitzis/70?filters=Tzitzis%20Minhag_Sephardi", "target": "_self", "parameters": null, "parent_id": "820", "order": "8", "enabled": "1", "created_at": "2022-05-26 11:09:04", "updated_at": "2022-05-26 11:09:29"}, {"id": "861", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/tzitzis/70?filters=Tzitzis%20Minhag_Chassidishe", "target": "_self", "parameters": null, "parent_id": "820", "order": "9", "enabled": "1", "created_at": "2022-05-26 11:09:32", "updated_at": "2022-05-26 11:10:03"}, {"id": "862", "menu_id": "1", "name": "Chabad", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/tzitzis/70?filters=Tzitzis%20Minhag_Chabad", "target": "_self", "parameters": null, "parent_id": "820", "order": "10", "enabled": "1", "created_at": "2022-05-26 11:10:10", "updated_at": "2022-05-26 11:10:29"}, {"id": "863", "menu_id": "1", "name": "Children's Tzitzis", "class": "App\\Classes\\AutomatedCategories", "value": "349", "target": "_self", "parameters": null, "parent_id": "820", "order": "2", "enabled": "1", "created_at": "2022-05-26 11:11:14", "updated_at": "2022-05-26 11:12:43"}, {"id": "864", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/garments-accessories/225?filters=Tzitzis%20Accessories_Tzitzis%20Strings", "target": "_self", "parameters": null, "parent_id": "820", "order": "11", "enabled": "1", "created_at": "2022-05-26 11:17:24", "updated_at": "2022-05-26 11:17:40"}, {"id": "865", "menu_id": "1", "name": "Tzitzis Accessories", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/garments-accessories/225?filters=Tzitzis%20Accessories_Tzitzis%20Acessories", "target": "_self", "parameters": null, "parent_id": "820", "order": "12", "enabled": "1", "created_at": "2022-05-26 11:17:43", "updated_at": "2022-05-26 11:18:10"}, {"id": "866", "menu_id": "1", "name": "Media", "class": "App\\Classes\\Pages", "value": "7", "target": "_self", "parameters": null, "parent_id": "472", "order": "6", "enabled": "1", "created_at": "2022-05-26 11:18:19", "updated_at": "2023-04-05 13:10:11"}, {"id": "867", "menu_id": "1", "name": "All Yarmulkes", "class": "App\\Classes\\Categories", "value": "16", "target": "_self", "parameters": null, "parent_id": "853", "order": "1", "enabled": "1", "created_at": "2022-05-26 11:21:03", "updated_at": "2022-05-26 11:44:31"}, {"id": "868", "menu_id": "1", "name": "Traditional Yarmulkes", "class": "App\\Classes\\AutomatedCategories", "value": "161", "target": "_self", "parameters": null, "parent_id": "853", "order": "2", "enabled": "1", "created_at": "2022-05-26 11:23:15", "updated_at": "2022-05-26 11:44:22"}, {"id": "869", "menu_id": "1", "name": "Colored Yarmulkes", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yarmulka/16?filters=Yarmulke%20Type_Colored", "target": "_self", "parameters": null, "parent_id": "853", "order": "3", "enabled": "1", "created_at": "2022-05-26 11:24:25", "updated_at": "2022-05-26 11:44:16"}, {"id": "870", "menu_id": "1", "name": "Designed Yarmulkes", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yarmulka/16?filters=Yarmulke%20Type_Designed", "target": "_self", "parameters": null, "parent_id": "853", "order": "4", "enabled": "1", "created_at": "2022-05-26 11:27:01", "updated_at": "2022-05-26 11:44:08"}, {"id": "871", "menu_id": "1", "name": "Cotton Yarmulkes", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yarmulka/16?filters=Material_Cotton&page=1", "target": "_self", "parameters": null, "parent_id": "853", "order": "8", "enabled": "1", "created_at": "2022-05-26 11:27:37", "updated_at": "2022-05-26 11:43:54"}, {"id": "872", "menu_id": "1", "name": "Terylene Yarmulkes", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yarmulka/16?filters=Material_Terylene&page=1", "target": "_self", "parameters": null, "parent_id": "853", "order": "15", "enabled": "1", "created_at": "2022-05-26 11:29:48", "updated_at": "2022-05-26 11:43:54"}, {"id": "873", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yarmulka/16?filters=Material_Suede&page=1", "target": "_self", "parameters": null, "parent_id": "853", "order": "13", "enabled": "1", "created_at": "2022-05-26 11:29:51", "updated_at": "2022-05-26 11:43:54"}, {"id": "874", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yarmulka/16?filters=Material_Denim&page=1", "target": "_self", "parameters": null, "parent_id": "853", "order": "12", "enabled": "1", "created_at": "2022-05-26 11:29:53", "updated_at": "2022-05-26 11:43:54"}, {"id": "875", "menu_id": "1", "name": "Leather <PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yarmulka/16?filters=Material_Leather&page=1", "target": "_self", "parameters": null, "parent_id": "853", "order": "11", "enabled": "1", "created_at": "2022-05-26 11:29:55", "updated_at": "2022-05-26 11:43:54"}, {"id": "876", "menu_id": "1", "name": "Linen <PERSON>ul<PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yarmulka/16?filters=Material_Linen&page=1", "target": "_self", "parameters": null, "parent_id": "853", "order": "10", "enabled": "1", "created_at": "2022-05-26 11:29:57", "updated_at": "2022-05-26 11:43:54"}, {"id": "877", "menu_id": "1", "name": "<PERSON> Yarmulkes", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yarmulka/16?filters=Material_Velvet&page=1", "target": "_self", "parameters": null, "parent_id": "853", "order": "9", "enabled": "1", "created_at": "2022-05-26 11:29:59", "updated_at": "2022-05-26 11:43:54"}, {"id": "878", "menu_id": "1", "name": "Wool Yarmulkes", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yarmulka/16?filters=Material_Wool&page=1", "target": "_self", "parameters": null, "parent_id": "853", "order": "14", "enabled": "1", "created_at": "2022-05-26 11:32:33", "updated_at": "2022-05-26 11:43:54"}, {"id": "880", "menu_id": "1", "name": "Corduroy  Yarmulkes", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yarmulka/16?filters=Material_Corduroy&page=1", "target": "_self", "parameters": null, "parent_id": "853", "order": "18", "enabled": "1", "created_at": "2022-05-26 11:33:53", "updated_at": "2022-05-26 11:43:54"}, {"id": "881", "menu_id": "1", "name": "Jersey Yarmulkes", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yarmulka/16?filters=Material_Jersey&page=1", "target": "_self", "parameters": null, "parent_id": "853", "order": "17", "enabled": "1", "created_at": "2022-05-26 11:33:54", "updated_at": "2022-05-26 11:43:54"}, {"id": "882", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yarmulka/16?filters=Material_Burlap&page=1", "target": "_self", "parameters": null, "parent_id": "853", "order": "16", "enabled": "1", "created_at": "2022-05-26 11:33:55", "updated_at": "2022-05-26 11:43:54"}, {"id": "883", "menu_id": "1", "name": "Suiting <PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "284", "target": "_self", "parameters": null, "parent_id": "853", "order": "5", "enabled": "1", "created_at": "2022-05-26 11:38:20", "updated_at": "2022-05-26 11:44:02"}, {"id": "884", "menu_id": "1", "name": "Knitted Yarmulkes", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yarmulka/16?filters=Yarmulke%20Type_Knitted&page=1", "target": "_self", "parameters": null, "parent_id": "853", "order": "6", "enabled": "1", "created_at": "2022-05-26 11:39:56", "updated_at": "2022-05-26 11:43:51"}, {"id": "885", "menu_id": "1", "name": "Sleeping Yarmulkes", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/yarmulka/16?filters=Yarmulke%20Type_Sleep", "target": "_self", "parameters": null, "parent_id": "853", "order": "7", "enabled": "1", "created_at": "2022-05-26 11:40:40", "updated_at": "2022-05-26 11:43:41"}, {"id": "886", "menu_id": "1", "name": "Yarmulke Accessories", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/garments-accessories/225?filters=Yarmulke%20Accessories_Yarmulke%20Accessories", "target": "_self", "parameters": null, "parent_id": "853", "order": "19", "enabled": "1", "created_at": "2022-05-26 11:41:45", "updated_at": "2022-05-26 11:43:54"}, {"id": "887", "menu_id": "1", "name": "iKippah", "class": "App\\Classes\\Pages", "value": "107", "target": "_self", "parameters": null, "parent_id": "853", "order": "20", "enabled": "1", "created_at": "2022-05-26 11:44:46", "updated_at": "2022-05-26 11:45:14"}, {"id": "909", "menu_id": "1", "name": "Garments", "class": "App\\Classes\\Pages", "value": "1", "target": "_self", "parameters": null, "parent_id": "472", "order": "7", "enabled": "1", "created_at": "2022-05-26 11:50:42", "updated_at": "2023-04-05 13:10:12"}, {"id": "910", "menu_id": "1", "name": "Atarahs", "class": "App\\Classes\\Categories", "value": "64", "target": "_self", "parameters": null, "parent_id": "909", "order": "4", "enabled": "1", "created_at": "2022-05-26 11:51:17", "updated_at": "2022-06-13 11:28:36"}, {"id": "911", "menu_id": "1", "name": "Tallis & Tefillin Bags", "class": "App\\Classes\\Pages", "value": "88", "target": "_self", "parameters": null, "parent_id": "472", "order": "8", "enabled": "1", "created_at": "2022-05-26 11:52:35", "updated_at": "2023-04-05 13:10:13"}, {"id": "912", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/tallis-and-tefillin-bags/74?filters=Material_Leather", "target": "_self", "parameters": null, "parent_id": "911", "order": "1", "enabled": "1", "created_at": "2022-05-26 11:58:35", "updated_at": "2022-05-26 12:05:41"}, {"id": "913", "menu_id": "1", "name": "Velvet Tallis Bags", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/tallis-and-tefillin-bags/74?filters=Material_Velvet", "target": "_blank", "parameters": null, "parent_id": "911", "order": "2", "enabled": "1", "created_at": "2022-05-26 11:59:16", "updated_at": "2022-05-26 12:05:41"}, {"id": "914", "menu_id": "1", "name": "Bar Mitzvah Te<PERSON> Bags", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/tallis-and-tefillin-bags/74?filters=Bag%20Type_Tefillin", "target": "_self", "parameters": null, "parent_id": "911", "order": "3", "enabled": "1", "created_at": "2022-05-26 12:03:42", "updated_at": "2022-05-26 12:04:32"}, {"id": "915", "menu_id": "1", "name": "Prestige Tefillin Bags", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/vendor/prestige-embroidery/457", "target": "_self", "parameters": null, "parent_id": "911", "order": "4", "enabled": "1", "created_at": "2022-05-26 12:04:39", "updated_at": "2022-05-26 12:05:10"}, {"id": "924", "menu_id": "1", "name": "Siddurim", "class": "App\\Classes\\Pages", "value": "22", "target": "_self", "parameters": null, "parent_id": "800", "order": "1", "enabled": "1", "created_at": "2022-05-26 12:12:50", "updated_at": "2022-06-13 11:25:37"}, {"id": "925", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/siddurim/141?filters=Nusach_Ashkenaz", "target": "_self", "parameters": null, "parent_id": "945", "order": "1", "enabled": "1", "created_at": "2022-05-26 12:12:50", "updated_at": "2022-05-26 12:20:09"}, {"id": "926", "menu_id": "1", "name": "All Siddurim", "class": "App\\Classes\\Categories", "value": "141", "target": "_self", "parameters": null, "parent_id": "924", "order": "1", "enabled": "1", "created_at": "2022-05-26 12:12:50", "updated_at": "2022-05-26 12:17:13"}, {"id": "927", "menu_id": "1", "name": "Siddur With Tehillim", "class": "App\\Classes\\Categories", "value": "376", "target": "_self", "parameters": null, "parent_id": "924", "order": "2", "enabled": "1", "created_at": "2022-05-26 12:12:50", "updated_at": "2022-05-26 12:17:45"}, {"id": "928", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/siddurim/141?filters=<PERSON><PERSON><PERSON>_<PERSON>d", "target": "_self", "parameters": null, "parent_id": "945", "order": "2", "enabled": "1", "created_at": "2022-05-26 12:12:50", "updated_at": "2022-05-26 12:20:15"}, {"id": "929", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/siddurim/141?filters=Nusach_Edus%20HaMizrach&page=1", "target": "_self", "parameters": null, "parent_id": "945", "order": "3", "enabled": "1", "created_at": "2022-05-26 12:12:50", "updated_at": "2022-05-26 12:20:57"}, {"id": "930", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/siddurim/141?filters=Nusach_Ari&page=1", "target": "_self", "parameters": null, "parent_id": "945", "order": "4", "enabled": "1", "created_at": "2022-05-26 12:12:50", "updated_at": "2022-05-26 12:21:25"}, {"id": "931", "menu_id": "1", "name": "Antique Leather Siddurim", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/siddurim/141?filters=Format_Leather", "target": "_self", "parameters": null, "parent_id": "924", "order": "7", "enabled": "1", "created_at": "2022-05-26 12:12:50", "updated_at": "2022-05-26 12:36:35"}, {"id": "932", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/siddurim/141?filters=Format_Leatherette&page=1", "target": "_self", "parameters": null, "parent_id": "924", "order": "8", "enabled": "1", "created_at": "2022-05-26 12:12:50", "updated_at": "2022-05-26 12:36:35"}, {"id": "933", "menu_id": "1", "name": "Weekday Siddurim", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/siddurim/141?filters=Siddur%20Type_Weekday,Siddur%20Type_L%27Chol&page=1", "target": "_self", "parameters": null, "parent_id": "924", "order": "9", "enabled": "1", "created_at": "2022-05-26 12:12:50", "updated_at": "2022-05-26 12:36:35"}, {"id": "934", "menu_id": "1", "name": "Shabbos & Yom Tov Siddurim", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/siddurim/141?filters=Siddur%20Type_Shabbos%20%26%20Yom%20Tov", "target": "_self", "parameters": null, "parent_id": "924", "order": "10", "enabled": "1", "created_at": "2022-05-26 12:12:50", "updated_at": "2022-05-26 12:36:35"}, {"id": "935", "menu_id": "1", "name": "Chumash With Siddur", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/siddurim/141?categories=Chumash&page=1", "target": "_self", "parameters": null, "parent_id": "924", "order": "11", "enabled": "1", "created_at": "2022-05-26 12:12:50", "updated_at": "2022-05-26 12:36:35"}, {"id": "936", "menu_id": "1", "name": "Pocket Size Siddur", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/siddurim/141?filters=Size_Pocket%20Size", "target": "_self", "parameters": null, "parent_id": "924", "order": "12", "enabled": "1", "created_at": "2022-05-26 12:12:50", "updated_at": "2022-05-26 12:36:35"}, {"id": "937", "menu_id": "1", "name": "Children's Siddurim", "class": "App\\Classes\\AutomatedCategories", "value": "50", "target": "_self", "parameters": null, "parent_id": "924", "order": "3", "enabled": "1", "created_at": "2022-05-26 12:12:50", "updated_at": "2022-05-26 12:33:19"}, {"id": "945", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "204", "target": "_self", "parameters": null, "parent_id": "924", "order": "6", "enabled": "1", "created_at": "2022-05-26 12:19:03", "updated_at": "2022-05-26 12:36:42"}, {"id": "949", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "142", "target": "_self", "parameters": null, "parent_id": "800", "order": "3", "enabled": "1", "created_at": "2022-05-26 12:22:52", "updated_at": "2022-06-13 11:25:37"}, {"id": "950", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/tehillim/142?categories=Antique%20Leather&page=1", "target": "_self", "parameters": null, "parent_id": "949", "order": "1", "enabled": "1", "created_at": "2022-05-26 12:34:03", "updated_at": "2022-05-26 12:35:24"}, {"id": "951", "menu_id": "1", "name": "Hebrew/English Siddurim", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/siddurim/141?filters=Language_English%2FHebrew,Language_English&page=1", "target": "_self", "parameters": null, "parent_id": "924", "order": "4", "enabled": "1", "created_at": "2022-05-26 12:35:24", "updated_at": "2022-05-26 12:36:18"}, {"id": "952", "menu_id": "1", "name": "Yiddish Siddurim", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/siddurim/141?filters=Language_Yiddish&page=1", "target": "_self", "parameters": null, "parent_id": "924", "order": "5", "enabled": "1", "created_at": "2022-05-26 12:36:24", "updated_at": "2022-05-26 12:37:10"}, {"id": "953", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/tehillim/142?filters=Format_Leatherette&page=1", "target": "_self", "parameters": null, "parent_id": "949", "order": "2", "enabled": "1", "created_at": "2022-05-26 12:37:39", "updated_at": "2022-05-26 12:38:31"}, {"id": "954", "menu_id": "1", "name": "Siddur With Tehillim", "class": "App\\Classes\\Categories", "value": "376", "target": "_self", "parameters": null, "parent_id": "949", "order": "3", "enabled": "1", "created_at": "2022-05-26 12:38:46", "updated_at": "2022-05-26 12:39:05"}, {"id": "955", "menu_id": "1", "name": "Hebrew/English Tehillim", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/tehillim/142?filters=Language_English%2FHebrew,Language_English&page=1", "target": "_self", "parameters": null, "parent_id": "949", "order": "4", "enabled": "1", "created_at": "2022-05-26 12:39:09", "updated_at": "2022-05-26 12:39:44"}, {"id": "956", "menu_id": "1", "name": "Yiddish Tehillim", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/tehillim/142?filters=Language_Yiddish&page=1", "target": "_self", "parameters": null, "parent_id": "949", "order": "5", "enabled": "1", "created_at": "2022-05-26 12:40:25", "updated_at": "2022-05-26 12:40:38"}, {"id": "957", "menu_id": "1", "name": "Machzorim", "class": "App\\Classes\\Pages", "value": "80", "target": "_self", "parameters": null, "parent_id": "800", "order": "2", "enabled": "1", "created_at": "2022-05-26 12:40:56", "updated_at": "2022-06-13 11:25:37"}, {"id": "958", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "179", "target": "_self", "parameters": null, "parent_id": "957", "order": "1", "enabled": "1", "created_at": "2022-05-26 12:40:56", "updated_at": "2022-05-26 12:45:00"}, {"id": "959", "menu_id": "1", "name": "Machzorim Sets", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-machzorim/143?filters=Machzor_Full%20Set", "target": "_self", "parameters": null, "parent_id": "957", "order": "2", "enabled": "1", "created_at": "2022-05-26 12:40:56", "updated_at": "2022-05-26 12:45:00"}, {"id": "960", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-machzorim/143?filters=Machzor_Rosh%20Hashana", "target": "_self", "parameters": null, "parent_id": "957", "order": "3", "enabled": "1", "created_at": "2022-05-26 12:40:56", "updated_at": "2022-05-26 12:45:50"}, {"id": "961", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-machzorim/143?filters=Machzor_Yom%20Kippur", "target": "_self", "parameters": null, "parent_id": "957", "order": "4", "enabled": "1", "created_at": "2022-05-26 12:40:56", "updated_at": "2022-05-26 12:46:08"}, {"id": "962", "menu_id": "1", "name": "Sukkos <PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-machzorim/143?filters=Machzor_Succos", "target": "_self", "parameters": null, "parent_id": "957", "order": "5", "enabled": "1", "created_at": "2022-05-26 12:40:56", "updated_at": "2022-05-26 12:46:39"}, {"id": "963", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-machzorim/143?filters=Machzor_Shavuos", "target": "_self", "parameters": null, "parent_id": "957", "order": "7", "enabled": "1", "created_at": "2022-05-26 12:46:12", "updated_at": "2022-05-26 12:47:30"}, {"id": "964", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-machzorim/143?filters=<PERSON>h<PERSON>_<PERSON>ach", "target": "_self", "parameters": null, "parent_id": "957", "order": "6", "enabled": "1", "created_at": "2022-05-26 12:46:13", "updated_at": "2022-05-26 12:47:12"}, {"id": "965", "menu_id": "1", "name": "Yiddish Machzorim", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/all-machzorim/143?filters=Language_Yiddish", "target": "_self", "parameters": null, "parent_id": "957", "order": "9", "enabled": "1", "created_at": "2022-05-26 12:47:34", "updated_at": "2022-05-26 12:48:40"}, {"id": "966", "menu_id": "1", "name": "Hebrew/English Machzorim", "class": "App\\Classes\\AutomatedCategories", "value": "175", "target": "_self", "parameters": null, "parent_id": "957", "order": "8", "enabled": "1", "created_at": "2022-05-26 12:47:36", "updated_at": "2022-05-26 12:48:18"}, {"id": "971", "menu_id": "1", "name": "Shop By Brand", "class": "App\\Classes\\Pages", "value": "12", "target": "_self", "parameters": null, "parent_id": "472", "order": "14", "enabled": "1", "created_at": "2022-06-10 12:29:21", "updated_at": "2024-05-28 21:10:30"}, {"id": "972", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "76", "target": "_self", "parameters": null, "parent_id": "971", "order": "1", "enabled": "1", "created_at": "2022-06-10 12:29:21", "updated_at": "2022-06-10 12:29:21"}, {"id": "973", "menu_id": "1", "name": "Feldheim", "class": "App\\Classes\\Pages", "value": "97", "target": "_self", "parameters": null, "parent_id": "971", "order": "2", "enabled": "1", "created_at": "2022-06-10 12:29:21", "updated_at": "2022-06-10 12:29:21"}, {"id": "974", "menu_id": "1", "name": "<PERSON>ren & Maggid", "class": "App\\Classes\\Pages", "value": "105", "target": "_self", "parameters": null, "parent_id": "971", "order": "3", "enabled": "1", "created_at": "2022-06-10 12:29:21", "updated_at": "2022-06-10 12:29:21"}, {"id": "975", "menu_id": "1", "name": "Israel Bookshop Publications", "class": "App\\Classes\\Pages", "value": "99", "target": "_self", "parameters": null, "parent_id": "971", "order": "4", "enabled": "1", "created_at": "2022-06-10 12:29:21", "updated_at": "2022-06-10 12:29:21"}, {"id": "976", "menu_id": "1", "name": "Judaica Press", "class": "App\\Classes\\Pages", "value": "102", "target": "_self", "parameters": null, "parent_id": "971", "order": "5", "enabled": "1", "created_at": "2022-06-10 12:29:21", "updated_at": "2022-06-10 12:29:21"}, {"id": "977", "menu_id": "1", "name": "CIS Publishers", "class": "App\\Classes\\Pages", "value": "103", "target": "_self", "parameters": null, "parent_id": "971", "order": "6", "enabled": "1", "created_at": "2022-06-10 12:29:21", "updated_at": "2022-06-10 12:29:21"}, {"id": "978", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "104", "target": "_self", "parameters": null, "parent_id": "971", "order": "7", "enabled": "1", "created_at": "2022-06-10 12:29:21", "updated_at": "2022-06-10 12:29:21"}, {"id": "979", "menu_id": "1", "name": "Menucha Publishers", "class": "App\\Classes\\Pages", "value": "101", "target": "_self", "parameters": null, "parent_id": "971", "order": "8", "enabled": "1", "created_at": "2022-06-10 12:29:21", "updated_at": "2022-06-10 12:29:21"}, {"id": "980", "menu_id": "1", "name": "Kittels", "class": "App\\Classes\\Categories", "value": "66", "target": "_self", "parameters": null, "parent_id": "909", "order": "6", "enabled": "1", "created_at": "2022-06-10 12:30:06", "updated_at": "2022-06-13 11:28:53"}, {"id": "982", "menu_id": "1", "name": "All Tefillos & Segulos", "class": "App\\Classes\\Categories", "value": "170", "target": "_self", "parameters": null, "parent_id": "481", "order": "1", "enabled": "1", "created_at": "2022-06-10 13:00:04", "updated_at": "2022-06-10 13:09:10"}, {"id": "983", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "145", "target": "_self", "parameters": null, "parent_id": "481", "order": "3", "enabled": "1", "created_at": "2022-06-10 13:01:44", "updated_at": "2022-06-10 13:02:01"}, {"id": "984", "menu_id": "1", "name": "Bencher Holders", "class": "App\\Classes\\Categories", "value": "160", "target": "_self", "parameters": null, "parent_id": "481", "order": "11", "enabled": "1", "created_at": "2022-06-10 13:02:07", "updated_at": "2022-06-10 13:02:24"}, {"id": "985", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/benchers-tefillos-and-segulos/170?filters=Tefillos_Hadlakas%20Neiros", "target": "_self", "parameters": null, "parent_id": "481", "order": "4", "enabled": "1", "created_at": "2022-06-10 13:02:28", "updated_at": "2022-06-10 13:03:19"}, {"id": "986", "menu_id": "1", "name": "Brachos", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/benchers-tefillos-and-segulos/170?filters=Tefillos_Berachos,Tefillos_Al%20Hamichya%20,Tefillos_Asher%20Yatzor,Tefillos_Al%20Netilas%20Yadayim&page=1", "target": "_self", "parameters": null, "parent_id": "481", "order": "5", "enabled": "1", "created_at": "2022-06-10 13:03:37", "updated_at": "2022-06-10 13:09:59"}, {"id": "987", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/benchers-tefillos-and-segulos/170?filters=Tefillos_Birchas%20HaBayis&page=1", "target": "_self", "parameters": null, "parent_id": "481", "order": "7", "enabled": "1", "created_at": "2022-06-10 13:05:21", "updated_at": "2022-06-10 13:13:55"}, {"id": "988", "menu_id": "1", "name": "Benchers", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/benchers-tefillos-and-segulos/170?filters=Tefillos_Benchers&page=1", "target": "_self", "parameters": null, "parent_id": "481", "order": "2", "enabled": "1", "created_at": "2022-06-10 13:08:46", "updated_at": "2022-06-10 13:08:46"}, {"id": "989", "menu_id": "1", "name": "Plaques", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/artwork/180?filters=Artwork_Plaques", "target": "_self", "parameters": null, "parent_id": "481", "order": "10", "enabled": "1", "created_at": "2022-06-10 13:10:17", "updated_at": "2022-06-10 13:10:59"}, {"id": "990", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/benchers-tefillos-and-segulos/170?filters=Tefillos_Krias%20Shema&page=1", "target": "_self", "parameters": null, "parent_id": "481", "order": "6", "enabled": "1", "created_at": "2022-06-10 13:11:10", "updated_at": "2022-06-10 13:13:55"}, {"id": "991", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/benchers-tefillos-and-segulos/170?filters=Tefillos_Birchas%20HaOsek&page=1", "target": "_self", "parameters": null, "parent_id": "481", "order": "8", "enabled": "1", "created_at": "2022-06-10 13:14:31", "updated_at": "2022-06-10 13:14:41"}, {"id": "992", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/benchers-tefillos-and-segulos/170?filters=Tefillos_Sefiras%20Haomer&page=1", "target": "_self", "parameters": null, "parent_id": "481", "order": "9", "enabled": "1", "created_at": "2022-06-10 13:15:32", "updated_at": "2022-06-10 13:15:46"}, {"id": "993", "menu_id": "1", "name": "Shabbos", "class": "App\\Classes\\Pages", "value": "72", "target": "_self", "parameters": null, "parent_id": "494", "order": "1", "enabled": "1", "created_at": "2022-06-13 09:59:11", "updated_at": "2022-06-13 10:06:14"}, {"id": "994", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "129", "target": "_self", "parameters": null, "parent_id": "494", "order": "3", "enabled": "1", "created_at": "2022-06-13 10:03:30", "updated_at": "2023-04-05 13:10:23"}, {"id": "995", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "26", "target": "_self", "parameters": null, "parent_id": "494", "order": "4", "enabled": "1", "created_at": "2022-06-13 10:03:55", "updated_at": "2023-04-05 13:10:23"}, {"id": "996", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "143", "target": "_self", "parameters": null, "parent_id": "494", "order": "5", "enabled": "1", "created_at": "2022-06-13 10:04:19", "updated_at": "2023-04-05 13:10:24"}, {"id": "997", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "68", "target": "_self", "parameters": null, "parent_id": "494", "order": "7", "enabled": "1", "created_at": "2022-06-13 10:04:47", "updated_at": "2023-04-05 13:10:24"}, {"id": "998", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "146", "target": "_self", "parameters": null, "parent_id": "494", "order": "6", "enabled": "1", "created_at": "2022-06-13 10:05:24", "updated_at": "2023-04-05 13:10:24"}, {"id": "999", "menu_id": "1", "name": "Tishrei", "class": "App\\Classes\\Pages", "value": "27", "target": "_self", "parameters": null, "parent_id": "494", "order": "2", "enabled": "1", "created_at": "2022-06-13 10:06:03", "updated_at": "2022-06-13 10:06:03"}, {"id": "1000", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "28", "target": "_self", "parameters": null, "parent_id": "494", "order": "8", "enabled": "1", "created_at": "2022-06-13 10:06:19", "updated_at": "2023-04-05 13:10:24"}, {"id": "1001", "menu_id": "1", "name": "3 Weeks / Tisha B'Av", "class": "App\\Classes\\Pages", "value": "119", "target": "_self", "parameters": null, "parent_id": "494", "order": "9", "enabled": "1", "created_at": "2022-06-13 10:06:39", "updated_at": "2023-04-05 13:10:24"}, {"id": "1002", "menu_id": "1", "name": "Shabbos Candles & Accessories", "class": "App\\Classes\\Pages", "value": "33", "target": "_self", "parameters": null, "parent_id": "478", "order": "1", "enabled": "1", "created_at": "2022-06-13 10:07:20", "updated_at": "2022-06-13 10:10:34"}, {"id": "1003", "menu_id": "1", "name": "Candlesticks & Candelabras", "class": "App\\Classes\\AutomatedCategories", "value": "345", "target": "_self", "parameters": null, "parent_id": "478", "order": "2", "enabled": "1", "created_at": "2022-06-13 10:09:11", "updated_at": "2022-06-15 19:02:56"}, {"id": "1004", "menu_id": "1", "name": "Paper Goods", "class": "App\\Classes\\Pages", "value": "111", "target": "_self", "parameters": null, "parent_id": "478", "order": "15", "enabled": "1", "created_at": "2022-06-13 10:10:04", "updated_at": "2022-06-15 19:02:57"}, {"id": "1005", "menu_id": "1", "name": "Kiddush Cups", "class": "App\\Classes\\Pages", "value": "144", "target": "_self", "parameters": null, "parent_id": "478", "order": "5", "enabled": "1", "created_at": "2022-06-13 10:15:39", "updated_at": "2022-06-15 19:02:56"}, {"id": "1006", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "179", "target": "_self", "parameters": null, "parent_id": "478", "order": "17", "enabled": "1", "created_at": "2022-06-13 10:24:53", "updated_at": "2022-06-15 19:02:57"}, {"id": "1007", "menu_id": "1", "name": "Shabbos Table", "class": "App\\Classes\\Categories", "value": "153", "target": "_self", "parameters": null, "parent_id": "478", "order": "14", "enabled": "1", "created_at": "2022-06-13 10:25:38", "updated_at": "2022-06-15 19:02:57"}, {"id": "1008", "menu_id": "1", "name": "Kiddush Wine Fountains", "class": "App\\Classes\\Categories", "value": "234", "target": "_self", "parameters": null, "parent_id": "478", "order": "6", "enabled": "1", "created_at": "2022-06-13 10:27:09", "updated_at": "2022-06-15 19:02:56"}, {"id": "1009", "menu_id": "1", "name": "Challah Boards", "class": "App\\Classes\\Categories", "value": "155", "target": "_self", "parameters": null, "parent_id": "478", "order": "7", "enabled": "1", "created_at": "2022-06-13 10:28:14", "updated_at": "2022-06-15 19:02:56"}, {"id": "1010", "menu_id": "1", "name": "<PERSON><PERSON> Covers", "class": "App\\Classes\\Categories", "value": "157", "target": "_self", "parameters": null, "parent_id": "478", "order": "8", "enabled": "1", "created_at": "2022-06-13 10:28:39", "updated_at": "2022-06-15 19:02:56"}, {"id": "1011", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "162", "target": "_self", "parameters": null, "parent_id": "478", "order": "10", "enabled": "1", "created_at": "2022-06-13 10:29:04", "updated_at": "2022-06-15 19:02:56"}, {"id": "1012", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "145", "target": "_self", "parameters": null, "parent_id": "478", "order": "12", "enabled": "1", "created_at": "2022-06-13 10:29:22", "updated_at": "2022-06-15 19:02:57"}, {"id": "1013", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "34", "target": "_self", "parameters": null, "parent_id": "478", "order": "16", "enabled": "1", "created_at": "2022-06-13 10:29:43", "updated_at": "2022-06-15 19:02:57"}, {"id": "1014", "menu_id": "1", "name": "Bencher Holders", "class": "App\\Classes\\Categories", "value": "160", "target": "_self", "parameters": null, "parent_id": "478", "order": "13", "enabled": "1", "created_at": "2022-06-13 10:33:30", "updated_at": "2022-06-15 19:02:57"}, {"id": "1015", "menu_id": "1", "name": "Washing Cups", "class": "App\\Classes\\Pages", "value": "110", "target": "_self", "parameters": null, "parent_id": "478", "order": "11", "enabled": "1", "created_at": "2022-06-13 10:35:49", "updated_at": "2022-06-15 19:02:56"}, {"id": "1016", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/knives/354?filters=Knife%20Type_Challa%20Knife", "target": "_self", "parameters": null, "parent_id": "478", "order": "9", "enabled": "1", "created_at": "2022-06-13 10:36:31", "updated_at": "2022-06-15 19:02:56"}, {"id": "1017", "menu_id": "1", "name": "Shabbos Seforim", "class": "App\\Classes\\Categories", "value": "311", "target": "_self", "parameters": null, "parent_id": "478", "order": "18", "enabled": "1", "created_at": "2022-06-13 10:37:14", "updated_at": "2022-06-15 19:02:57"}, {"id": "1018", "menu_id": "1", "name": "Card Games", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/toys-and-games/190?filters=Toys%20and%20Games_Card%20Games", "target": "_self", "parameters": null, "parent_id": "487", "order": "1", "enabled": "1", "created_at": "2022-06-13 10:42:47", "updated_at": "2022-06-13 10:45:01"}, {"id": "1019", "menu_id": "1", "name": "Puzzles", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/toys-and-games/190?filters=Toys%20and%20Games_Puzzles", "target": "_self", "parameters": null, "parent_id": "487", "order": "2", "enabled": "1", "created_at": "2022-06-13 10:45:05", "updated_at": "2022-06-13 10:45:30"}, {"id": "1020", "menu_id": "1", "name": "Educational Games", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/toys-and-games/190?filters=Toys%20and%20Games_Education", "target": "_self", "parameters": null, "parent_id": "487", "order": "3", "enabled": "1", "created_at": "2022-06-13 10:45:33", "updated_at": "2022-06-13 10:46:03"}, {"id": "1021", "menu_id": "1", "name": "Toys", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/toys-and-games/190?filters=Toys%20and%20Games_Toys", "target": "_self", "parameters": null, "parent_id": "487", "order": "4", "enabled": "1", "created_at": "2022-06-13 10:46:06", "updated_at": "2022-06-13 10:46:34"}, {"id": "1022", "menu_id": "1", "name": "Puppets", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/toys-and-games/190?filters=Toys%20and%20Games_Puppets", "target": "_self", "parameters": null, "parent_id": "487", "order": "5", "enabled": "1", "created_at": "2022-06-13 10:47:05", "updated_at": "2022-06-13 10:47:15"}, {"id": "1023", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/toys-and-games/190?filters=Toys%20and%20Games_Menchies", "target": "_self", "parameters": null, "parent_id": "487", "order": "6", "enabled": "1", "created_at": "2022-06-13 10:47:28", "updated_at": "2022-06-13 10:47:38"}, {"id": "1024", "menu_id": "1", "name": "Dress <PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/toys-and-games/190?filters=Toys%20and%20Games_Dress-ups", "target": "_self", "parameters": null, "parent_id": "487", "order": "7", "enabled": "1", "created_at": "2022-06-13 10:47:41", "updated_at": "2022-06-13 10:47:58"}, {"id": "1025", "menu_id": "1", "name": "Board Games", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/toys-and-games/190?filters=Toys%20and%20Games_Board%20Games", "target": "_self", "parameters": null, "parent_id": "487", "order": "8", "enabled": "1", "created_at": "2022-06-13 10:48:02", "updated_at": "2022-06-13 10:48:25"}, {"id": "1026", "menu_id": "1", "name": "Vehicles", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/toys-and-games/190?filters=Toys%20and%20Games_Vehicles", "target": "_self", "parameters": null, "parent_id": "487", "order": "9", "enabled": "1", "created_at": "2022-06-13 10:48:58", "updated_at": "2022-06-13 10:49:11"}, {"id": "1027", "menu_id": "1", "name": "Talking Toys", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/toys-and-games/190?filters=Toys%20and%20Games_Talking%20Toys", "target": "_self", "parameters": null, "parent_id": "487", "order": "10", "enabled": "1", "created_at": "2022-06-13 10:49:37", "updated_at": "2023-04-05 13:10:55"}, {"id": "1028", "menu_id": "1", "name": "Torah & Judaica Toys", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/toys-and-games/190?filters=Toys%20and%20Games_Torah%20Tefillin%20and%20Judaica", "target": "_self", "parameters": null, "parent_id": "487", "order": "11", "enabled": "1", "created_at": "2022-06-13 10:50:08", "updated_at": "2023-04-05 13:10:55"}, {"id": "1029", "menu_id": "1", "name": "Haggadahs", "class": "App\\Classes\\Pages", "value": "139", "target": "_self", "parameters": null, "parent_id": "663", "order": "40", "enabled": "1", "created_at": "2022-06-13 10:52:30", "updated_at": "2022-06-13 11:26:39"}, {"id": "1030", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/haggadahs/86?filters=Format_Leather", "target": "_self", "parameters": null, "parent_id": "1029", "order": "2", "enabled": "1", "created_at": "2022-06-13 10:52:30", "updated_at": "2022-06-13 10:55:03"}, {"id": "1031", "menu_id": "1", "name": "English Haggadahs", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/haggadahs/86?filters=Language_English", "target": "_self", "parameters": null, "parent_id": "1029", "order": "3", "enabled": "1", "created_at": "2022-06-13 10:52:30", "updated_at": "2022-06-13 10:55:03"}, {"id": "1032", "menu_id": "1", "name": "Hebrew Haggadahs", "class": "App\\Classes\\AutomatedCategories", "value": "394", "target": "_self", "parameters": null, "parent_id": "1029", "order": "4", "enabled": "1", "created_at": "2022-06-13 10:52:30", "updated_at": "2022-06-13 10:55:03"}, {"id": "1033", "menu_id": "1", "name": "All Haggadahs", "class": "App\\Classes\\Categories", "value": "86", "target": "_self", "parameters": null, "parent_id": "1029", "order": "1", "enabled": "1", "created_at": "2022-06-13 10:52:30", "updated_at": "2022-06-13 10:55:03"}, {"id": "1034", "menu_id": "1", "name": "Children's Haggadahs", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "categories/haggadahs/86?filters=Haggadah%20Type_Children’s%20Haggadas", "target": "_self", "parameters": null, "parent_id": "1029", "order": "6", "enabled": "1", "created_at": "2022-06-13 10:52:30", "updated_at": "2022-06-13 10:55:56"}, {"id": "1035", "menu_id": "1", "name": "Yiddish Haggadahs", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/haggadahs/86?filters=Language_Yiddish", "target": "_self", "parameters": null, "parent_id": "1029", "order": "5", "enabled": "1", "created_at": "2022-06-13 10:52:30", "updated_at": "2022-06-13 10:55:56"}, {"id": "1036", "menu_id": "1", "name": "Haggadahs Without Meforshim", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/haggadahs/86?filters=Haggadah%20Type_Text", "target": "_self", "parameters": null, "parent_id": "1029", "order": "7", "enabled": "1", "created_at": "2022-06-13 10:52:30", "updated_at": "2022-06-13 10:56:22"}, {"id": "1067", "menu_id": "1", "name": "Gifts", "class": "App\\Classes\\Pages", "value": "131", "target": "_self", "parameters": null, "parent_id": "472", "order": "11", "enabled": "1", "created_at": "2022-06-13 11:14:01", "updated_at": "2023-04-05 13:10:14"}, {"id": "1068", "menu_id": "1", "name": "Gift Cards", "class": "App\\Classes\\Pages", "value": "45", "target": "_self", "parameters": null, "parent_id": "1067", "order": "1", "enabled": "1", "created_at": "2022-06-13 11:14:01", "updated_at": "2022-06-13 11:14:01"}, {"id": "1069", "menu_id": "1", "name": "Judaica Gifts", "class": "App\\Classes\\Pages", "value": "83", "target": "_self", "parameters": null, "parent_id": "1067", "order": "2", "enabled": "1", "created_at": "2022-06-13 11:14:02", "updated_at": "2022-06-13 11:14:02"}, {"id": "1070", "menu_id": "1", "name": "Toys & games", "class": "App\\Classes\\Pages", "value": "70", "target": "_self", "parameters": null, "parent_id": "1067", "order": "3", "enabled": "1", "created_at": "2022-06-13 11:14:02", "updated_at": "2023-04-05 13:10:37"}, {"id": "1071", "menu_id": "1", "name": "Waterdale Collection", "class": "App\\Classes\\Pages", "value": "116", "target": "_self", "parameters": null, "parent_id": "1067", "order": "4", "enabled": "1", "created_at": "2022-06-13 11:14:02", "updated_at": "2023-04-05 13:10:37"}, {"id": "1072", "menu_id": "1", "name": "Upsherin Gifts", "class": "App\\Classes\\Pages", "value": "71", "target": "_self", "parameters": null, "parent_id": "1067", "order": "5", "enabled": "1", "created_at": "2022-06-13 11:14:02", "updated_at": "2023-04-05 13:10:37"}, {"id": "1073", "menu_id": "1", "name": "Bar Mitzvah Gifts", "class": "App\\Classes\\Categories", "value": "279", "target": "_self", "parameters": null, "parent_id": "1067", "order": "6", "enabled": "1", "created_at": "2022-06-13 11:14:02", "updated_at": "2023-04-05 13:10:37"}, {"id": "1074", "menu_id": "1", "name": "<PERSON> Gifts", "class": "App\\Classes\\Categories", "value": "287", "target": "_self", "parameters": null, "parent_id": "1067", "order": "7", "enabled": "1", "created_at": "2022-06-13 11:14:02", "updated_at": "2023-04-05 13:10:37"}, {"id": "1075", "menu_id": "1", "name": "Elegant Shabbos Table Gifts", "class": "App\\Classes\\Categories", "value": "280", "target": "_self", "parameters": null, "parent_id": "1067", "order": "8", "enabled": "1", "created_at": "2022-06-13 11:14:02", "updated_at": "2023-04-05 13:10:37"}, {"id": "1076", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "82", "target": "_self", "parameters": null, "parent_id": "1067", "order": "9", "enabled": "1", "created_at": "2022-06-13 11:14:02", "updated_at": "2023-04-05 13:10:37"}, {"id": "1077", "menu_id": "1", "name": "Wedding", "class": "App\\Classes\\Pages", "value": "36", "target": "_self", "parameters": null, "parent_id": "1067", "order": "10", "enabled": "1", "created_at": "2022-06-13 11:14:02", "updated_at": "2023-04-05 13:10:37"}, {"id": "1078", "menu_id": "1", "name": "Washing Cups", "class": "App\\Classes\\Pages", "value": "110", "target": "_self", "parameters": null, "parent_id": "1067", "order": "11", "enabled": "1", "created_at": "2022-06-13 11:14:02", "updated_at": "2023-04-05 13:10:37"}, {"id": "1079", "menu_id": "1", "name": "Shtenders", "class": "App\\Classes\\Pages", "value": "120", "target": "_self", "parameters": null, "parent_id": "1067", "order": "12", "enabled": "1", "created_at": "2022-06-13 11:14:02", "updated_at": "2023-04-05 13:10:37"}, {"id": "1080", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "31", "target": "_self", "parameters": null, "parent_id": "1067", "order": "13", "enabled": "1", "created_at": "2022-06-13 11:14:02", "updated_at": "2023-04-05 13:10:37"}, {"id": "1081", "menu_id": "1", "name": "Cookbooks", "class": "App\\Classes\\Pages", "value": "15", "target": "_self", "parameters": null, "parent_id": "1067", "order": "14", "enabled": "1", "created_at": "2022-06-13 11:14:02", "updated_at": "2023-04-05 13:10:37"}, {"id": "1082", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "179", "target": "_self", "parameters": null, "parent_id": "1067", "order": "15", "enabled": "1", "created_at": "2022-06-13 11:14:02", "updated_at": "2023-04-05 13:10:47"}, {"id": "1083", "menu_id": "1", "name": "Books", "class": "App\\Classes\\Pages", "value": "2", "target": "_self", "parameters": null, "parent_id": "1067", "order": "16", "enabled": "1", "created_at": "2022-06-13 11:14:02", "updated_at": "2023-04-05 13:10:47"}, {"id": "1084", "menu_id": "1", "name": "Talleisim", "class": "App\\Classes\\Categories", "value": "68", "target": "_self", "parameters": null, "parent_id": "909", "order": "3", "enabled": "1", "created_at": "2022-06-13 11:26:48", "updated_at": "2022-06-13 11:28:32"}, {"id": "1085", "menu_id": "1", "name": "Gartels", "class": "App\\Classes\\Categories", "value": "65", "target": "_self", "parameters": null, "parent_id": "909", "order": "5", "enabled": "1", "created_at": "2022-06-13 11:27:34", "updated_at": "2022-06-13 11:28:42"}, {"id": "1087", "menu_id": "1", "name": "Shabbos & Yom Tov Candles", "class": "App\\Classes\\Pages", "value": "33", "target": "_self", "parameters": null, "parent_id": "475", "order": "1", "enabled": "1", "created_at": "2022-06-13 11:31:42", "updated_at": "2022-06-14 10:38:55"}, {"id": "1088", "menu_id": "1", "name": "Arts & Crafts", "class": "App\\Classes\\Pages", "value": "123", "target": "_self", "parameters": null, "parent_id": "472", "order": "13", "enabled": "1", "created_at": "2022-06-13 11:33:08", "updated_at": "2023-04-05 13:10:14"}, {"id": "1089", "menu_id": "1", "name": "Stickers", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/arts-crafts/192?filters=Arts%20%26%20Crafts_Stickers", "target": "_self", "parameters": null, "parent_id": "1088", "order": "1", "enabled": "1", "created_at": "2022-06-13 11:33:08", "updated_at": "2022-06-13 11:36:20"}, {"id": "1090", "menu_id": "1", "name": "Crafts", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/arts-crafts/192?filters=Arts%20%26%20Crafts_Crafts", "target": "_self", "parameters": null, "parent_id": "1088", "order": "2", "enabled": "1", "created_at": "2022-06-13 11:33:08", "updated_at": "2022-06-13 11:36:42"}, {"id": "1091", "menu_id": "1", "name": "Writing", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/arts-crafts/192?filters=Arts%20%26%20Crafts_Writings", "target": "_self", "parameters": null, "parent_id": "1088", "order": "3", "enabled": "1", "created_at": "2022-06-13 11:33:08", "updated_at": "2022-06-13 11:37:11"}, {"id": "1092", "menu_id": "1", "name": "Coloring Books", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/arts-crafts/192?filters=Arts%20%26%20Crafts_Coloring%20Books", "target": "_self", "parameters": null, "parent_id": "1088", "order": "4", "enabled": "1", "created_at": "2022-06-13 11:33:08", "updated_at": "2022-06-13 11:37:27"}, {"id": "1093", "menu_id": "1", "name": "Foil Art", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/arts-crafts/192?filters=Arts%20%26%20Crafts_Foil%20and%20Art", "target": "_self", "parameters": null, "parent_id": "1088", "order": "5", "enabled": "1", "created_at": "2022-06-13 11:33:08", "updated_at": "2022-06-13 11:37:45"}, {"id": "1094", "menu_id": "1", "name": "Painting Crafts", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/arts-crafts/192?filters=Arts%20%26%20Crafts_Paint", "target": "_self", "parameters": null, "parent_id": "1088", "order": "6", "enabled": "1", "created_at": "2022-06-13 11:33:08", "updated_at": "2022-06-13 11:38:11"}, {"id": "1100", "menu_id": "1", "name": "Mezuza Cases", "class": "App\\Classes\\Categories", "value": "152", "target": "_self", "parameters": null, "parent_id": "475", "order": "5", "enabled": "1", "created_at": "2022-06-13 11:41:17", "updated_at": "2022-06-14 10:38:51"}, {"id": "1101", "menu_id": "1", "name": "Mezuzos", "class": "App\\Classes\\Pages", "value": "31", "target": "_self", "parameters": null, "parent_id": "475", "order": "6", "enabled": "1", "created_at": "2022-06-13 11:41:31", "updated_at": "2022-06-14 10:38:51"}, {"id": "1102", "menu_id": "1", "name": "Kiddush Cups", "class": "App\\Classes\\Pages", "value": "144", "target": "_self", "parameters": null, "parent_id": "475", "order": "7", "enabled": "1", "created_at": "2022-06-13 11:51:37", "updated_at": "2022-06-14 10:38:51"}, {"id": "1103", "menu_id": "1", "name": "Shtenders", "class": "App\\Classes\\Pages", "value": "120", "target": "_self", "parameters": null, "parent_id": "475", "order": "8", "enabled": "1", "created_at": "2022-06-13 11:53:35", "updated_at": "2022-06-14 10:38:51"}, {"id": "1104", "menu_id": "1", "name": "Cookware", "class": "App\\Classes\\Categories", "value": "245", "target": "_self", "parameters": null, "parent_id": "493", "order": "2", "enabled": "1", "created_at": "2022-06-13 12:12:51", "updated_at": "2022-06-13 15:04:57"}, {"id": "1106", "menu_id": "1", "name": "Washing Cups", "class": "App\\Classes\\Pages", "value": "110", "target": "_self", "parameters": null, "parent_id": "493", "order": "3", "enabled": "1", "created_at": "2022-06-13 15:05:01", "updated_at": "2022-06-13 15:11:10"}, {"id": "1107", "menu_id": "1", "name": "Cookbooks", "class": "App\\Classes\\Pages", "value": "15", "target": "_self", "parameters": null, "parent_id": "493", "order": "5", "enabled": "1", "created_at": "2022-06-13 15:06:19", "updated_at": "2022-06-13 15:06:19"}, {"id": "1108", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "332", "target": "_self", "parameters": null, "parent_id": "493", "order": "4", "enabled": "1", "created_at": "2022-06-13 15:11:16", "updated_at": "2023-01-10 14:31:17"}, {"id": "1109", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/benchers-tefillos-and-segulos/170?filters=Tefillos_Hadlakas%20Neiros", "target": "_self", "parameters": null, "parent_id": "478", "order": "4", "enabled": "1", "created_at": "2022-06-13 15:12:36", "updated_at": "2022-06-15 19:02:56"}, {"id": "1110", "menu_id": "1", "name": "Cookbooks", "class": "App\\Classes\\Pages", "value": "15", "target": "_self", "parameters": null, "parent_id": "493", "order": "6", "enabled": "1", "created_at": "2022-06-14 10:34:35", "updated_at": "2022-06-14 10:34:35"}, {"id": "1111", "menu_id": "1", "name": "Yahrtzeit Candles", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/candles-accessories/217?filters=Candle%20Type_Yahrzeit", "target": "_self", "parameters": null, "parent_id": "475", "order": "2", "enabled": "1", "created_at": "2022-06-14 10:36:04", "updated_at": "2022-06-14 10:39:38"}, {"id": "1112", "menu_id": "1", "name": "Elegant Shabbos Table Gifts", "class": "App\\Classes\\Categories", "value": "280", "target": "_self", "parameters": null, "parent_id": "478", "order": "19", "enabled": "1", "created_at": "2022-06-14 10:40:10", "updated_at": "2022-06-15 19:02:57"}, {"id": "1113", "menu_id": "1", "name": "Custom Seforim Stamps", "class": "App\\Classes\\Categories", "value": "378", "target": "_self", "parameters": null, "parent_id": "475", "order": "10", "enabled": "1", "created_at": "2022-06-14 10:49:45", "updated_at": "2022-06-14 10:50:09"}, {"id": "1114", "menu_id": "1", "name": "Custom Seforim Stamps", "class": "App\\Classes\\Categories", "value": "378", "target": "_self", "parameters": null, "parent_id": "1067", "order": "17", "enabled": "1", "created_at": "2022-06-14 10:50:20", "updated_at": "2023-04-05 13:10:47"}, {"id": "1115", "menu_id": "1", "name": "Artwork & Frames", "class": "App\\Classes\\Categories", "value": "180", "target": "_self", "parameters": null, "parent_id": "472", "order": "15", "enabled": "1", "created_at": "2022-06-14 11:00:54", "updated_at": "2024-09-18 12:24:25"}, {"id": "1119", "menu_id": "1", "name": "Picture Frames", "class": "App\\Classes\\Categories", "value": "353", "target": "_self", "parameters": null, "parent_id": "1115", "order": "1", "enabled": "1", "created_at": "2022-06-14 11:00:54", "updated_at": "2022-06-14 12:02:29"}, {"id": "1122", "menu_id": "1", "name": "Paintings", "class": "App\\Classes\\Categories", "value": "330", "target": "_self", "parameters": null, "parent_id": "1115", "order": "2", "enabled": "1", "created_at": "2022-06-14 11:00:54", "updated_at": "2022-06-14 12:02:29"}, {"id": "1123", "menu_id": "1", "name": "Plaques", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/artwork/180?filters=Artwork_Plaques&page=1", "target": "_self", "parameters": null, "parent_id": "1115", "order": "3", "enabled": "1", "created_at": "2022-06-14 11:00:54", "updated_at": "2022-06-14 12:02:29"}, {"id": "1124", "menu_id": "1", "name": "Tefillos", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/benchers-tefillos-and-segulos/170?filters=Artwork_Plaques&page=1", "target": "_self", "parameters": null, "parent_id": "1115", "order": "4", "enabled": "1", "created_at": "2022-06-14 11:00:54", "updated_at": "2022-06-14 12:02:30"}, {"id": "1126", "menu_id": "1", "name": "Trays", "class": "App\\Classes\\Categories", "value": "231", "target": "_self", "parameters": null, "parent_id": "478", "order": "3", "enabled": "1", "created_at": "2022-06-14 11:49:19", "updated_at": "2022-06-15 19:02:56"}, {"id": "1127", "menu_id": "1", "name": "Shtenders", "class": "App\\Classes\\Pages", "value": "120", "target": "_self", "parameters": null, "parent_id": "475", "order": "9", "enabled": "1", "created_at": "2022-06-14 11:59:52", "updated_at": "2022-06-14 11:59:52"}, {"id": "1128", "menu_id": "1", "name": "Set Your Table", "class": "App\\Classes\\Pages", "value": "111", "target": "_self", "parameters": null, "parent_id": "472", "order": "20", "enabled": "1", "created_at": "2022-06-14 12:02:33", "updated_at": "2023-04-05 13:10:15"}, {"id": "1129", "menu_id": "1", "name": "Paper Goods", "class": "App\\Classes\\Pages", "value": "115", "target": "_self", "parameters": null, "parent_id": "1128", "order": "1", "enabled": "1", "created_at": "2022-06-14 12:02:33", "updated_at": "2022-06-14 12:03:17"}, {"id": "1130", "menu_id": "1", "name": "Salt & Pepper Shakers", "class": "App\\Classes\\Categories", "value": "161", "target": "_self", "parameters": null, "parent_id": "1128", "order": "2", "enabled": "1", "created_at": "2022-06-14 12:02:33", "updated_at": "2022-06-14 12:03:35"}, {"id": "1131", "menu_id": "1", "name": "Pan Holders", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "https://www.shopeichlers.com/categories/table-settings/358?filters=Tableware%20Type_Pan%20Holders&page=1", "target": "_self", "parameters": null, "parent_id": "1128", "order": "3", "enabled": "1", "created_at": "2022-06-14 12:02:33", "updated_at": "2022-06-14 12:05:53"}, {"id": "1132", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>ers", "class": "App\\Classes\\Categories", "value": "159", "target": "_self", "parameters": null, "parent_id": "1128", "order": "4", "enabled": "1", "created_at": "2022-06-14 12:02:33", "updated_at": "2022-06-14 12:06:12"}, {"id": "1133", "menu_id": "1", "name": "Dip Bowls", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/table-settings/358?filters=Tableware%20Type_Dip%20Bowls&page=1", "target": "_self", "parameters": null, "parent_id": "1128", "order": "5", "enabled": "1", "created_at": "2022-06-14 12:02:33", "updated_at": "2022-06-14 12:06:55"}, {"id": "1134", "menu_id": "1", "name": "Cups", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/table-settings/358?filters=Tableware%20Type_Cups&page=1", "target": "_self", "parameters": null, "parent_id": "1128", "order": "6", "enabled": "1", "created_at": "2022-06-14 12:02:33", "updated_at": "2022-06-14 12:08:21"}, {"id": "1135", "menu_id": "1", "name": "Chargers", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/table-settings/358?filters=Tableware%20Type_Chargers&page=1", "target": "_self", "parameters": null, "parent_id": "1128", "order": "7", "enabled": "1", "created_at": "2022-06-14 12:08:25", "updated_at": "2022-06-14 12:09:59"}, {"id": "1136", "menu_id": "1", "name": "Bread Trays", "class": "OptimistDigital\\MenuBuilder\\Classes\\MenuItemStaticURL", "value": "/categories/table-settings/358?filters=Tableware%20Type_Bread%20Trays&page=1", "target": "_self", "parameters": null, "parent_id": "1128", "order": "8", "enabled": "1", "created_at": "2022-06-14 12:10:06", "updated_at": "2022-06-14 12:12:28"}, {"id": "1137", "menu_id": "1", "name": "Wine Decanters", "class": "App\\Classes\\Categories", "value": "320", "target": "_self", "parameters": null, "parent_id": "1128", "order": "9", "enabled": "1", "created_at": "2022-06-14 12:12:31", "updated_at": "2022-06-14 12:15:37"}, {"id": "1138", "menu_id": "1", "name": "Liquor Sets", "class": "App\\Classes\\Categories", "value": "313", "target": "_self", "parameters": null, "parent_id": "1128", "order": "10", "enabled": "1", "created_at": "2022-06-14 12:15:41", "updated_at": "2022-06-14 12:17:54"}, {"id": "1139", "menu_id": "1", "name": "Tzedaka Boxes", "class": "App\\Classes\\Categories", "value": "177", "target": "_self", "parameters": null, "parent_id": "475", "order": "11", "enabled": "1", "created_at": "2022-06-14 12:23:41", "updated_at": "2022-06-14 12:23:56"}, {"id": "1140", "menu_id": "1", "name": "Calendars", "class": "App\\Classes\\Categories", "value": "178", "target": "_self", "parameters": null, "parent_id": "475", "order": "12", "enabled": "1", "created_at": "2022-06-14 12:24:12", "updated_at": "2022-06-14 12:24:24"}, {"id": "1141", "menu_id": "1", "name": "Summer Shop", "class": "App\\Classes\\Pages", "value": "148", "target": "_self", "parameters": null, "parent_id": "472", "order": "1", "enabled": "1", "created_at": "2022-06-15 19:02:51", "updated_at": "2023-04-05 13:10:06"}, {"id": "1142", "menu_id": "1", "name": "Shop By Occasion", "class": "App\\Classes\\Categories", "value": "275", "target": "_self", "parameters": null, "parent_id": "472", "order": "16", "enabled": "1", "created_at": "2022-06-22 15:11:22", "updated_at": "2023-04-05 13:10:14"}, {"id": "1143", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "133", "target": "_self", "parameters": null, "parent_id": "1142", "order": "1", "enabled": "1", "created_at": "2022-06-22 15:11:22", "updated_at": "2022-06-23 10:06:58"}, {"id": "1144", "menu_id": "1", "name": "<PERSON><PERSON>in", "class": "App\\Classes\\Pages", "value": "71", "target": "_self", "parameters": null, "parent_id": "1142", "order": "2", "enabled": "1", "created_at": "2022-06-22 15:11:22", "updated_at": "2022-06-22 15:13:47"}, {"id": "1145", "menu_id": "1", "name": "Bar Mitzvah", "class": "App\\Classes\\Pages", "value": "121", "target": "_self", "parameters": null, "parent_id": "1142", "order": "3", "enabled": "1", "created_at": "2022-06-22 15:11:22", "updated_at": "2022-06-22 15:14:03"}, {"id": "1146", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "287", "target": "_self", "parameters": null, "parent_id": "1142", "order": "4", "enabled": "1", "created_at": "2022-06-22 15:11:22", "updated_at": "2022-06-23 10:07:44"}, {"id": "1147", "menu_id": "1", "name": "Wedding", "class": "App\\Classes\\Pages", "value": "36", "target": "_self", "parameters": null, "parent_id": "1142", "order": "5", "enabled": "1", "created_at": "2022-06-22 15:15:17", "updated_at": "2022-06-22 15:15:38"}, {"id": "1148", "menu_id": "1", "name": "Tefillah Seforim", "class": "App\\Classes\\Categories", "value": "140", "target": "_self", "parameters": null, "parent_id": "253", "order": "9", "enabled": "1", "created_at": "2022-06-23 12:00:26", "updated_at": "2022-06-23 12:00:43"}, {"id": "1149", "menu_id": "1", "name": "Married Life", "class": "App\\Classes\\Pages", "value": "63", "target": "_self", "parameters": null, "parent_id": "1142", "order": "6", "enabled": "1", "created_at": "2022-06-28 14:30:06", "updated_at": "2022-06-28 14:30:18"}, {"id": "1150", "menu_id": "1", "name": "Purim Books", "class": "App\\Classes\\Pages", "value": "158", "target": "_self", "parameters": null, "parent_id": "258", "order": "4", "enabled": "1", "created_at": "2023-02-09 14:25:45", "updated_at": "2023-12-17 16:56:52"}, {"id": "1151", "menu_id": "1", "name": "Purim Seforim & Books", "class": "App\\Classes\\Categories", "value": "198", "target": "_self", "parameters": null, "parent_id": "1150", "order": "1", "enabled": "0", "created_at": "2023-02-09 14:25:45", "updated_at": "2023-02-09 14:25:45"}, {"id": "1152", "menu_id": "1", "name": "Costumes", "class": "App\\Classes\\AutomatedCategories", "value": "484", "target": "_self", "parameters": null, "parent_id": "258", "order": "7", "enabled": "1", "created_at": "2023-02-09 14:27:08", "updated_at": "2023-12-17 16:56:52"}, {"id": "1162", "menu_id": "1", "name": "For The Pesach Table", "class": "App\\Classes\\AutomatedCategories", "value": "30", "target": "_self", "parameters": null, "parent_id": "1161", "order": "1", "enabled": "1", "created_at": "2023-03-06 14:28:21", "updated_at": "2023-03-06 14:28:21"}, {"id": "1163", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>s", "class": "App\\Classes\\Pages", "value": "142", "target": "_self", "parameters": null, "parent_id": "1161", "order": "2", "enabled": "1", "created_at": "2023-03-06 14:28:21", "updated_at": "2023-03-06 14:28:21"}, {"id": "1164", "menu_id": "1", "name": "Seder Plate", "class": "App\\Classes\\Categories", "value": "81", "target": "_self", "parameters": null, "parent_id": "1161", "order": "3", "enabled": "1", "created_at": "2023-03-06 14:28:21", "updated_at": "2023-03-06 14:28:21"}, {"id": "1165", "menu_id": "1", "name": "Kiddush Cups", "class": "App\\Classes\\Categories", "value": "158", "target": "_self", "parameters": null, "parent_id": "1161", "order": "4", "enabled": "1", "created_at": "2023-03-06 14:28:21", "updated_at": "2023-03-06 14:28:21"}, {"id": "1166", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>es", "class": "App\\Classes\\Categories", "value": "82", "target": "_self", "parameters": null, "parent_id": "1161", "order": "5", "enabled": "1", "created_at": "2023-03-06 14:28:21", "updated_at": "2023-03-06 14:28:21"}, {"id": "1167", "menu_id": "1", "name": "Washing Cups", "class": "App\\Classes\\Pages", "value": "110", "target": "_self", "parameters": null, "parent_id": "1161", "order": "6", "enabled": "1", "created_at": "2023-03-06 14:28:21", "updated_at": "2023-03-06 14:28:21"}, {"id": "1168", "menu_id": "1", "name": "<PERSON><PERSON><PERSON> Covers", "class": "App\\Classes\\Categories", "value": "323", "target": "_self", "parameters": null, "parent_id": "1161", "order": "7", "enabled": "1", "created_at": "2023-03-06 14:28:21", "updated_at": "2023-03-06 14:28:21"}, {"id": "1169", "menu_id": "1", "name": "Pillow Cases", "class": "App\\Classes\\Categories", "value": "83", "target": "_self", "parameters": null, "parent_id": "1161", "order": "8", "enabled": "1", "created_at": "2023-03-06 14:28:21", "updated_at": "2023-03-06 14:28:21"}, {"id": "1170", "menu_id": "1", "name": "Afikomen Bags", "class": "App\\Classes\\Categories", "value": "84", "target": "_self", "parameters": null, "parent_id": "1161", "order": "9", "enabled": "0", "created_at": "2023-03-06 14:28:21", "updated_at": "2023-03-06 14:28:21"}, {"id": "1171", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "85", "target": "_self", "parameters": null, "parent_id": "1161", "order": "10", "enabled": "0", "created_at": "2023-03-06 14:28:21", "updated_at": "2023-03-06 14:28:21"}, {"id": "1183", "menu_id": "1", "name": "Shabbos & Yom Tov Candles", "class": "App\\Classes\\Categories", "value": "275", "target": "_self", "parameters": null, "parent_id": "1182", "order": "1", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1184", "menu_id": "1", "name": "Oil & Paraffin", "class": "App\\Classes\\Categories", "value": "271", "target": "_self", "parameters": null, "parent_id": "1182", "order": "2", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1185", "menu_id": "1", "name": "Pre-Filled Oil & Paraffin", "class": "App\\Classes\\Categories", "value": "272", "target": "_self", "parameters": null, "parent_id": "1182", "order": "3", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1186", "menu_id": "1", "name": "Oil & Candle Glasses", "class": "App\\Classes\\Categories", "value": "273", "target": "_self", "parameters": null, "parent_id": "1182", "order": "4", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1187", "menu_id": "1", "name": "Wicks & Holders", "class": "App\\Classes\\Categories", "value": "274", "target": "_self", "parameters": null, "parent_id": "1182", "order": "5", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1188", "menu_id": "1", "name": "Lighters & Matches", "class": "App\\Classes\\Categories", "value": "277", "target": "_self", "parameters": null, "parent_id": "1182", "order": "6", "enabled": "0", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1189", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "168", "target": "_self", "parameters": null, "parent_id": "1182", "order": "7", "enabled": "0", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1190", "menu_id": "1", "name": "Yahrzeit Candles", "class": "App\\Classes\\Categories", "value": "278", "target": "_self", "parameters": null, "parent_id": "1182", "order": "8", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1191", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "276", "target": "_self", "parameters": null, "parent_id": "1182", "order": "9", "enabled": "0", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1193", "menu_id": "1", "name": "Candlesticks", "class": "App\\Classes\\Categories", "value": "164", "target": "_self", "parameters": null, "parent_id": "1192", "order": "1", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1194", "menu_id": "1", "name": "Candelabras", "class": "App\\Classes\\Categories", "value": "165", "target": "_self", "parameters": null, "parent_id": "1192", "order": "2", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1195", "menu_id": "1", "name": "Candles & Accessories", "class": "App\\Classes\\Pages", "value": "52", "target": "_self", "parameters": null, "parent_id": "1192", "order": "3", "enabled": "0", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1196", "menu_id": "1", "name": "Shabbos & Yom Tov Candles", "class": "App\\Classes\\Categories", "value": "275", "target": "_self", "parameters": null, "parent_id": "1195", "order": "1", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1197", "menu_id": "1", "name": "Oil & Paraffin", "class": "App\\Classes\\Categories", "value": "271", "target": "_self", "parameters": null, "parent_id": "1195", "order": "2", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1198", "menu_id": "1", "name": "Pre-Filled Oil & Paraffin", "class": "App\\Classes\\Categories", "value": "272", "target": "_self", "parameters": null, "parent_id": "1195", "order": "3", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1199", "menu_id": "1", "name": "Oil & Candle Glasses", "class": "App\\Classes\\Categories", "value": "273", "target": "_self", "parameters": null, "parent_id": "1195", "order": "4", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1200", "menu_id": "1", "name": "Wicks & Holders", "class": "App\\Classes\\Categories", "value": "274", "target": "_self", "parameters": null, "parent_id": "1195", "order": "5", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1201", "menu_id": "1", "name": "Lighters & Matches", "class": "App\\Classes\\Categories", "value": "277", "target": "_self", "parameters": null, "parent_id": "1195", "order": "6", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1202", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "168", "target": "_self", "parameters": null, "parent_id": "1195", "order": "7", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1203", "menu_id": "1", "name": "Yahrzeit Candles", "class": "App\\Classes\\Categories", "value": "278", "target": "_self", "parameters": null, "parent_id": "1195", "order": "8", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1204", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "276", "target": "_self", "parameters": null, "parent_id": "1195", "order": "9", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1205", "menu_id": "1", "name": "Trays", "class": "App\\Classes\\Categories", "value": "231", "target": "_self", "parameters": null, "parent_id": "1192", "order": "4", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1206", "menu_id": "1", "name": "Match Boxes", "class": "App\\Classes\\Categories", "value": "233", "target": "_self", "parameters": null, "parent_id": "1192", "order": "5", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1207", "menu_id": "1", "name": "Tzedakah Boxes", "class": "App\\Classes\\Categories", "value": "177", "target": "_self", "parameters": null, "parent_id": "1192", "order": "6", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1208", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>ers", "class": "App\\Classes\\Categories", "value": "159", "target": "_self", "parameters": null, "parent_id": "1192", "order": "7", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1209", "menu_id": "1", "name": "Salt and Pepper Shakers", "class": "App\\Classes\\Categories", "value": "161", "target": "_self", "parameters": null, "parent_id": "1192", "order": "8", "enabled": "1", "created_at": "2023-05-12 11:01:16", "updated_at": "2023-05-12 11:01:16"}, {"id": "1210", "menu_id": "1", "name": "Kiddush Cups", "class": "App\\Classes\\Categories", "value": "158", "target": "_self", "parameters": null, "parent_id": "1192", "order": "9", "enabled": "1", "created_at": "2023-05-12 11:01:17", "updated_at": "2023-05-12 11:01:17"}, {"id": "1211", "menu_id": "1", "name": "Kiddush Wine Fountain", "class": "App\\Classes\\Categories", "value": "234", "target": "_self", "parameters": null, "parent_id": "1192", "order": "10", "enabled": "1", "created_at": "2023-05-12 11:01:17", "updated_at": "2023-05-12 11:01:17"}, {"id": "1212", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "156", "target": "_self", "parameters": null, "parent_id": "1192", "order": "11", "enabled": "1", "created_at": "2023-05-12 11:01:17", "updated_at": "2023-05-12 11:01:17"}, {"id": "1213", "menu_id": "1", "name": "Challah Boards", "class": "App\\Classes\\Categories", "value": "155", "target": "_self", "parameters": null, "parent_id": "1192", "order": "12", "enabled": "1", "created_at": "2023-05-12 11:01:17", "updated_at": "2023-05-12 11:01:17"}, {"id": "1214", "menu_id": "1", "name": "<PERSON><PERSON> Covers", "class": "App\\Classes\\Categories", "value": "157", "target": "_self", "parameters": null, "parent_id": "1192", "order": "13", "enabled": "1", "created_at": "2023-05-12 11:01:17", "updated_at": "2023-05-12 11:01:17"}, {"id": "1215", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "162", "target": "_self", "parameters": null, "parent_id": "1192", "order": "14", "enabled": "1", "created_at": "2023-05-12 11:01:17", "updated_at": "2023-05-12 11:01:17"}, {"id": "1216", "menu_id": "1", "name": "Bencher Holders", "class": "App\\Classes\\Categories", "value": "160", "target": "_self", "parameters": null, "parent_id": "1192", "order": "15", "enabled": "1", "created_at": "2023-05-12 11:01:17", "updated_at": "2023-05-12 11:01:17"}, {"id": "1217", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON> Sets", "class": "App\\Classes\\Categories", "value": "167", "target": "_self", "parameters": null, "parent_id": "1192", "order": "16", "enabled": "1", "created_at": "2023-05-12 11:01:17", "updated_at": "2023-05-12 11:01:17"}, {"id": "1218", "menu_id": "1", "name": "Besomim", "class": "App\\Classes\\Categories", "value": "169", "target": "_self", "parameters": null, "parent_id": "1192", "order": "17", "enabled": "1", "created_at": "2023-05-12 11:01:17", "updated_at": "2023-05-12 11:01:17"}, {"id": "1219", "menu_id": "1", "name": "Honey & Apple Dishes", "class": "App\\Classes\\Categories", "value": "237", "target": "_self", "parameters": null, "parent_id": "1192", "order": "18", "enabled": "0", "created_at": "2023-05-12 11:01:17", "updated_at": "2023-05-12 11:01:17"}, {"id": "1220", "menu_id": "1", "name": "Tishrei", "class": "App\\Classes\\Pages", "value": "27", "target": "_self", "parameters": null, "parent_id": "11", "order": "1", "enabled": "1", "created_at": "2023-07-30 12:07:24", "updated_at": "2023-07-30 12:07:56"}, {"id": "1221", "menu_id": "1", "name": "<PERSON>hofar", "class": "App\\Classes\\Pages", "value": "92", "target": "_self", "parameters": null, "parent_id": "1220", "order": "2", "enabled": "1", "created_at": "2023-07-30 12:07:24", "updated_at": "2023-09-26 12:41:26"}, {"id": "1222", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "161", "target": "_self", "parameters": null, "parent_id": "1220", "order": "3", "enabled": "1", "created_at": "2023-07-30 12:07:24", "updated_at": "2024-09-18 11:58:21"}, {"id": "1223", "menu_id": "1", "name": "Seforim & Books", "class": "App\\Classes\\Pages", "value": "122", "target": "_self", "parameters": null, "parent_id": "1220", "order": "4", "enabled": "1", "created_at": "2023-07-30 12:07:24", "updated_at": "2023-09-26 12:41:26"}, {"id": "1224", "menu_id": "1", "name": "Machzorim", "class": "App\\Classes\\Pages", "value": "80", "target": "_self", "parameters": null, "parent_id": "1220", "order": "5", "enabled": "1", "created_at": "2023-07-30 12:07:24", "updated_at": "2023-09-26 12:41:26"}, {"id": "1225", "menu_id": "1", "name": "honey & Apple Dishes", "class": "App\\Classes\\Categories", "value": "237", "target": "_self", "parameters": null, "parent_id": "1220", "order": "6", "enabled": "1", "created_at": "2023-07-30 12:07:24", "updated_at": "2023-09-26 12:41:26"}, {"id": "1226", "menu_id": "1", "name": "Kittels", "class": "App\\Classes\\Categories", "value": "66", "target": "_self", "parameters": null, "parent_id": "1220", "order": "7", "enabled": "1", "created_at": "2023-07-30 12:07:24", "updated_at": "2023-09-26 12:41:26"}, {"id": "1227", "menu_id": "1", "name": "Succos", "class": "App\\Classes\\Pages", "value": "23", "target": "_self", "parameters": null, "parent_id": "11", "order": "2", "enabled": "1", "created_at": "2023-07-30 12:07:24", "updated_at": "2023-12-17 16:56:20"}, {"id": "1228", "menu_id": "1", "name": "Set Your Table", "class": "App\\Classes\\Pages", "value": "72", "target": "_self", "parameters": null, "parent_id": "1220", "order": "8", "enabled": "1", "created_at": "2023-07-30 12:07:24", "updated_at": "2023-09-26 12:41:26"}, {"id": "1229", "menu_id": "1", "name": "Succos Decorations", "class": "App\\Classes\\Pages", "value": "84", "target": "_self", "parameters": null, "parent_id": "1227", "order": "1", "enabled": "1", "created_at": "2023-09-22 12:57:38", "updated_at": "2023-09-22 13:13:19"}, {"id": "1230", "menu_id": "1", "name": "Succos Books", "class": "App\\Classes\\AutomatedCategories", "value": "312", "target": "_self", "parameters": null, "parent_id": "1227", "order": "2", "enabled": "1", "created_at": "2023-09-22 12:57:46", "updated_at": "2023-09-26 12:40:51"}, {"id": "1231", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "313", "target": "_self", "parameters": null, "parent_id": "1227", "order": "5", "enabled": "1", "created_at": "2023-09-22 13:04:54", "updated_at": "2023-10-09 01:20:49"}, {"id": "1232", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "52", "target": "_self", "parameters": null, "parent_id": "1227", "order": "6", "enabled": "1", "created_at": "2023-09-22 13:11:26", "updated_at": "2023-10-09 01:20:49"}, {"id": "1233", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "383", "target": "_self", "parameters": null, "parent_id": "1227", "order": "4", "enabled": "1", "created_at": "2023-09-22 13:12:36", "updated_at": "2024-09-11 13:19:45"}, {"id": "1234", "menu_id": "1", "name": "Esrog Holders", "class": "App\\Classes\\Categories", "value": "384", "target": "_self", "parameters": null, "parent_id": "1227", "order": "3", "enabled": "1", "created_at": "2023-09-22 13:12:41", "updated_at": "2024-09-11 13:19:31"}, {"id": "1235", "menu_id": "1", "name": "Candles", "class": "App\\Classes\\Pages", "value": "33", "target": "_self", "parameters": null, "parent_id": "1227", "order": "7", "enabled": "1", "created_at": "2023-09-22 13:14:27", "updated_at": "2023-10-09 01:20:49"}, {"id": "1236", "menu_id": "1", "name": "<PERSON><PERSON>v Table", "class": "App\\Classes\\Pages", "value": "32", "target": "_self", "parameters": null, "parent_id": "1227", "order": "8", "enabled": "1", "created_at": "2023-09-22 13:15:15", "updated_at": "2023-10-09 01:20:49"}, {"id": "1247", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "95", "target": "_self", "parameters": null, "parent_id": "1246", "order": "1", "enabled": "0", "created_at": "2024-01-29 17:39:05", "updated_at": "2024-01-29 17:39:05"}, {"id": "1249", "menu_id": "1", "name": "Purim Seforim & Books", "class": "App\\Classes\\Categories", "value": "198", "target": "_self", "parameters": null, "parent_id": "1248", "order": "1", "enabled": "0", "created_at": "2024-01-29 17:39:05", "updated_at": "2024-01-29 17:39:05"}, {"id": "1251", "menu_id": "1", "name": "Purim Seforim & Books", "class": "App\\Classes\\Categories", "value": "198", "target": "_self", "parameters": null, "parent_id": "1250", "order": "1", "enabled": "0", "created_at": "2024-01-29 17:39:05", "updated_at": "2024-01-29 17:39:05"}, {"id": "1256", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "24", "target": "_self", "parameters": null, "parent_id": "11", "order": "5", "enabled": "1", "created_at": "2024-03-25 17:03:30", "updated_at": "2024-03-25 17:03:44"}, {"id": "1257", "menu_id": "1", "name": "Haggadahs", "class": "App\\Classes\\Categories", "value": "86", "target": "_self", "parameters": null, "parent_id": "1256", "order": "1", "enabled": "0", "created_at": "2024-03-25 17:03:30", "updated_at": "2024-03-25 17:03:30"}, {"id": "1258", "menu_id": "1", "name": "Haggadahs", "class": "App\\Classes\\Pages", "value": "139", "target": "_self", "parameters": null, "parent_id": "1256", "order": "2", "enabled": "1", "created_at": "2024-03-25 17:03:30", "updated_at": "2024-03-25 17:03:30"}, {"id": "1259", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\AutomatedCategories", "value": "55", "target": "_self", "parameters": null, "parent_id": "1256", "order": "3", "enabled": "1", "created_at": "2024-03-25 17:03:30", "updated_at": "2024-03-25 17:03:30"}, {"id": "1260", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "141", "target": "_self", "parameters": null, "parent_id": "1256", "order": "4", "enabled": "1", "created_at": "2024-03-25 17:03:30", "updated_at": "2024-03-25 17:03:30"}, {"id": "1261", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "196", "target": "_self", "parameters": null, "parent_id": "1256", "order": "5", "enabled": "0", "created_at": "2024-03-25 17:03:30", "updated_at": "2024-03-25 17:03:30"}, {"id": "1262", "menu_id": "1", "name": "Pesach Books", "class": "App\\Classes\\Pages", "value": "138", "target": "_self", "parameters": null, "parent_id": "1256", "order": "6", "enabled": "1", "created_at": "2024-03-25 17:03:30", "updated_at": "2024-03-25 17:03:30"}, {"id": "1263", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Pages", "value": "80", "target": "_self", "parameters": null, "parent_id": "1256", "order": "7", "enabled": "1", "created_at": "2024-03-25 17:03:30", "updated_at": "2024-03-25 17:03:30"}, {"id": "1264", "menu_id": "1", "name": "Seder Table", "class": "App\\Classes\\Pages", "value": "66", "target": "_self", "parameters": null, "parent_id": "1256", "order": "8", "enabled": "1", "created_at": "2024-03-25 17:03:30", "updated_at": "2024-03-25 17:03:30"}, {"id": "1265", "menu_id": "1", "name": "For The Pesach Table", "class": "App\\Classes\\AutomatedCategories", "value": "30", "target": "_self", "parameters": null, "parent_id": "1264", "order": "1", "enabled": "1", "created_at": "2024-03-25 17:03:30", "updated_at": "2024-03-25 17:03:30"}, {"id": "1266", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>s", "class": "App\\Classes\\Pages", "value": "142", "target": "_self", "parameters": null, "parent_id": "1264", "order": "2", "enabled": "1", "created_at": "2024-03-25 17:03:30", "updated_at": "2024-03-25 17:03:30"}, {"id": "1267", "menu_id": "1", "name": "Seder Plate", "class": "App\\Classes\\Categories", "value": "81", "target": "_self", "parameters": null, "parent_id": "1264", "order": "3", "enabled": "1", "created_at": "2024-03-25 17:03:30", "updated_at": "2024-03-25 17:03:30"}, {"id": "1268", "menu_id": "1", "name": "Kiddush Cups", "class": "App\\Classes\\Pages", "value": "144", "target": "_self", "parameters": null, "parent_id": "1264", "order": "4", "enabled": "1", "created_at": "2024-03-25 17:03:31", "updated_at": "2024-03-25 17:03:31"}, {"id": "1269", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>es", "class": "App\\Classes\\Categories", "value": "82", "target": "_self", "parameters": null, "parent_id": "1264", "order": "5", "enabled": "1", "created_at": "2024-03-25 17:03:31", "updated_at": "2024-03-25 17:03:31"}, {"id": "1270", "menu_id": "1", "name": "Washing Cups", "class": "App\\Classes\\Pages", "value": "110", "target": "_self", "parameters": null, "parent_id": "1264", "order": "6", "enabled": "1", "created_at": "2024-03-25 17:03:31", "updated_at": "2024-03-25 17:03:31"}, {"id": "1271", "menu_id": "1", "name": "<PERSON><PERSON><PERSON> Covers", "class": "App\\Classes\\Categories", "value": "323", "target": "_self", "parameters": null, "parent_id": "1264", "order": "7", "enabled": "1", "created_at": "2024-03-25 17:03:31", "updated_at": "2024-03-25 17:03:31"}, {"id": "1272", "menu_id": "1", "name": "Pillow Cases", "class": "App\\Classes\\Categories", "value": "83", "target": "_self", "parameters": null, "parent_id": "1264", "order": "8", "enabled": "1", "created_at": "2024-03-25 17:03:31", "updated_at": "2024-03-25 17:03:31"}, {"id": "1273", "menu_id": "1", "name": "Afikomen Bags", "class": "App\\Classes\\Categories", "value": "84", "target": "_self", "parameters": null, "parent_id": "1264", "order": "9", "enabled": "0", "created_at": "2024-03-25 17:03:31", "updated_at": "2024-03-25 17:03:31"}, {"id": "1274", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "85", "target": "_self", "parameters": null, "parent_id": "1264", "order": "10", "enabled": "0", "created_at": "2024-03-25 17:03:31", "updated_at": "2024-03-25 17:03:31"}, {"id": "1275", "menu_id": "1", "name": "Kittels", "class": "App\\Classes\\Categories", "value": "66", "target": "_self", "parameters": null, "parent_id": "1256", "order": "9", "enabled": "1", "created_at": "2024-03-25 17:03:31", "updated_at": "2024-03-25 17:03:31"}, {"id": "1276", "menu_id": "1", "name": "Afikomen Presents", "class": "App\\Classes\\Pages", "value": "65", "target": "_self", "parameters": null, "parent_id": "1256", "order": "10", "enabled": "1", "created_at": "2024-03-25 17:03:31", "updated_at": "2024-03-25 17:03:31"}, {"id": "1285", "menu_id": "1", "name": "Shabbos & Yom Tov Candles", "class": "App\\Classes\\Categories", "value": "275", "target": "_self", "parameters": null, "parent_id": "1284", "order": "1", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1286", "menu_id": "1", "name": "Oil & Paraffin", "class": "App\\Classes\\Categories", "value": "271", "target": "_self", "parameters": null, "parent_id": "1284", "order": "2", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1287", "menu_id": "1", "name": "Pre-Filled Oil & Paraffin", "class": "App\\Classes\\Categories", "value": "272", "target": "_self", "parameters": null, "parent_id": "1284", "order": "3", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1288", "menu_id": "1", "name": "Oil & Candle Glasses", "class": "App\\Classes\\Categories", "value": "273", "target": "_self", "parameters": null, "parent_id": "1284", "order": "4", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1289", "menu_id": "1", "name": "Wicks & Holders", "class": "App\\Classes\\Categories", "value": "274", "target": "_self", "parameters": null, "parent_id": "1284", "order": "5", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1290", "menu_id": "1", "name": "Lighters & Matches", "class": "App\\Classes\\Categories", "value": "277", "target": "_self", "parameters": null, "parent_id": "1284", "order": "6", "enabled": "0", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1291", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "168", "target": "_self", "parameters": null, "parent_id": "1284", "order": "7", "enabled": "0", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1292", "menu_id": "1", "name": "Yahrzeit Candles", "class": "App\\Classes\\Categories", "value": "278", "target": "_self", "parameters": null, "parent_id": "1284", "order": "8", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1293", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "276", "target": "_self", "parameters": null, "parent_id": "1284", "order": "9", "enabled": "0", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1295", "menu_id": "1", "name": "Candlesticks", "class": "App\\Classes\\Categories", "value": "164", "target": "_self", "parameters": null, "parent_id": "1294", "order": "1", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1296", "menu_id": "1", "name": "Candelabras", "class": "App\\Classes\\Categories", "value": "165", "target": "_self", "parameters": null, "parent_id": "1294", "order": "2", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1297", "menu_id": "1", "name": "Candles & Accessories", "class": "App\\Classes\\Pages", "value": "52", "target": "_self", "parameters": null, "parent_id": "1294", "order": "3", "enabled": "0", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1298", "menu_id": "1", "name": "Shabbos & Yom Tov Candles", "class": "App\\Classes\\Categories", "value": "275", "target": "_self", "parameters": null, "parent_id": "1297", "order": "1", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1299", "menu_id": "1", "name": "Oil & Paraffin", "class": "App\\Classes\\Categories", "value": "271", "target": "_self", "parameters": null, "parent_id": "1297", "order": "2", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1300", "menu_id": "1", "name": "Pre-Filled Oil & Paraffin", "class": "App\\Classes\\Categories", "value": "272", "target": "_self", "parameters": null, "parent_id": "1297", "order": "3", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1301", "menu_id": "1", "name": "Oil & Candle Glasses", "class": "App\\Classes\\Categories", "value": "273", "target": "_self", "parameters": null, "parent_id": "1297", "order": "4", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1302", "menu_id": "1", "name": "Wicks & Holders", "class": "App\\Classes\\Categories", "value": "274", "target": "_self", "parameters": null, "parent_id": "1297", "order": "5", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1303", "menu_id": "1", "name": "Lighters & Matches", "class": "App\\Classes\\Categories", "value": "277", "target": "_self", "parameters": null, "parent_id": "1297", "order": "6", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1304", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "168", "target": "_self", "parameters": null, "parent_id": "1297", "order": "7", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1305", "menu_id": "1", "name": "Yahrzeit Candles", "class": "App\\Classes\\Categories", "value": "278", "target": "_self", "parameters": null, "parent_id": "1297", "order": "8", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1306", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "276", "target": "_self", "parameters": null, "parent_id": "1297", "order": "9", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1307", "menu_id": "1", "name": "Trays", "class": "App\\Classes\\Categories", "value": "231", "target": "_self", "parameters": null, "parent_id": "1294", "order": "4", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1308", "menu_id": "1", "name": "Match Boxes", "class": "App\\Classes\\Categories", "value": "233", "target": "_self", "parameters": null, "parent_id": "1294", "order": "5", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1309", "menu_id": "1", "name": "Tzedakah Boxes", "class": "App\\Classes\\Categories", "value": "177", "target": "_self", "parameters": null, "parent_id": "1294", "order": "6", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1310", "menu_id": "1", "name": "<PERSON><PERSON><PERSON>ers", "class": "App\\Classes\\Categories", "value": "159", "target": "_self", "parameters": null, "parent_id": "1294", "order": "7", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1311", "menu_id": "1", "name": "Salt and Pepper Shakers", "class": "App\\Classes\\Categories", "value": "161", "target": "_self", "parameters": null, "parent_id": "1294", "order": "8", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1312", "menu_id": "1", "name": "Kiddush Cups", "class": "App\\Classes\\Categories", "value": "158", "target": "_self", "parameters": null, "parent_id": "1294", "order": "9", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1313", "menu_id": "1", "name": "Kiddush Wine Fountain", "class": "App\\Classes\\Categories", "value": "234", "target": "_self", "parameters": null, "parent_id": "1294", "order": "10", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1314", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "156", "target": "_self", "parameters": null, "parent_id": "1294", "order": "11", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1315", "menu_id": "1", "name": "Challah Boards", "class": "App\\Classes\\Categories", "value": "155", "target": "_self", "parameters": null, "parent_id": "1294", "order": "12", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1316", "menu_id": "1", "name": "<PERSON><PERSON> Covers", "class": "App\\Classes\\Categories", "value": "157", "target": "_self", "parameters": null, "parent_id": "1294", "order": "13", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1317", "menu_id": "1", "name": "<PERSON><PERSON>", "class": "App\\Classes\\Categories", "value": "162", "target": "_self", "parameters": null, "parent_id": "1294", "order": "14", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1318", "menu_id": "1", "name": "Bencher Holders", "class": "App\\Classes\\Categories", "value": "160", "target": "_self", "parameters": null, "parent_id": "1294", "order": "15", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1319", "menu_id": "1", "name": "<PERSON><PERSON><PERSON><PERSON> Sets", "class": "App\\Classes\\Categories", "value": "167", "target": "_self", "parameters": null, "parent_id": "1294", "order": "16", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1320", "menu_id": "1", "name": "Besomim", "class": "App\\Classes\\Categories", "value": "169", "target": "_self", "parameters": null, "parent_id": "1294", "order": "17", "enabled": "1", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}, {"id": "1321", "menu_id": "1", "name": "Honey & Apple Dishes", "class": "App\\Classes\\Categories", "value": "237", "target": "_self", "parameters": null, "parent_id": "1294", "order": "18", "enabled": "0", "created_at": "2024-05-01 12:53:19", "updated_at": "2024-05-01 12:53:19"}]