[{"id": "1", "name": "Bangladesh", "code": "BD", "currency": "BDT", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "2", "name": "Belgium", "code": "BE", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "3", "name": "Burkina Faso", "code": "BF", "currency": "XOF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "4", "name": "Bulgaria", "code": "BG", "currency": "BGN", "symbol": "&#1083;&#1074;", "currency_name": "Bulgaria Lev", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "5", "name": "Bosnia and Herzegovina", "code": "BA", "currency": "BAM", "symbol": "&#75;&#77;", "currency_name": "Bosnia and Herzegovina Convertible Marka", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "6", "name": "Barbados", "code": "BB", "currency": "BBD", "symbol": "&#36;", "currency_name": "Barbados Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "7", "name": "Wallis and Futuna", "code": "WF", "currency": "XPF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "8", "name": "<PERSON>", "code": "BL", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "9", "name": "Bermuda", "code": "BM", "currency": "BMD", "symbol": "&#36;", "currency_name": "Bermuda Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "10", "name": "Brunei", "code": "BN", "currency": "BND", "symbol": "&#36;", "currency_name": "Brunei Darussalam Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "11", "name": "Bolivia", "code": "BO", "currency": "BOB", "symbol": "&#36;&#98;", "currency_name": "Bolivia Boliviano", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "12", "name": "Bahrain", "code": "BH", "currency": "BHD", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "13", "name": "Burundi", "code": "BI", "currency": "BIF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "14", "name": "Benin", "code": "BJ", "currency": "XOF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "15", "name": "Bhutan", "code": "BT", "currency": "BTN", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "16", "name": "Jamaica", "code": "JM", "currency": "JMD", "symbol": "&#74;&#36;", "currency_name": "Jamaica Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "17", "name": "Bouvet Island", "code": "BV", "currency": "NOK", "symbol": "&#107;&#114;", "currency_name": "Norway Krone", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "18", "name": "Botswana", "code": "BW", "currency": "BWP", "symbol": "&#80;", "currency_name": "Botswana Pula", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "19", "name": "Samoa", "code": "WS", "currency": "WST", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "20", "name": "Bonaire, Saint Eustati<PERSON> and Saba ", "code": "BQ", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "21", "name": "Brazil", "code": "BR", "currency": "BRL", "symbol": "&#82;&#36;", "currency_name": "Brazil Real", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "22", "name": "Bahamas", "code": "BS", "currency": "BSD", "symbol": "&#36;", "currency_name": "Bahamas Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "23", "name": "Jersey", "code": "JE", "currency": "GBP", "symbol": "&#163;", "currency_name": "United Kingdom Pound", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "24", "name": "Belarus", "code": "BY", "currency": "BYR", "symbol": "&#112;&#46;", "currency_name": "Belarus Ruble", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "25", "name": "Belize", "code": "BZ", "currency": "BZD", "symbol": "&#66;&#90;&#36;", "currency_name": "Belize Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "26", "name": "Russia", "code": "RU", "currency": "RUB", "symbol": "&#1088;&#1091;&#1073;", "currency_name": "Russia Ruble", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "27", "name": "Rwanda", "code": "RW", "currency": "RWF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "28", "name": "Serbia", "code": "RS", "currency": "RSD", "symbol": "&#1044;&#1080;&#1085;&#46;", "currency_name": "Serbia Dinar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "29", "name": "East Timor", "code": "TL", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "30", "name": "Reunion", "code": "RE", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "31", "name": "Turkmenistan", "code": "TM", "currency": "TMT", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "32", "name": "Tajikistan", "code": "TJ", "currency": "TJS", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "33", "name": "Romania", "code": "RO", "currency": "RON", "symbol": "&#108;&#101;&#105;", "currency_name": "Romania New Leu", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "34", "name": "Tokelau", "code": "TK", "currency": "NZD", "symbol": "&#36;", "currency_name": "New Zealand Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "35", "name": "Guinea-Bissau", "code": "GW", "currency": "XOF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "36", "name": "Guam", "code": "GU", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "37", "name": "Guatemala", "code": "GT", "currency": "GTQ", "symbol": "&#81;", "currency_name": "Guatemala Quetzal", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "38", "name": "South Georgia and the South Sandwich Islands", "code": "GS", "currency": "GBP", "symbol": "&#163;", "currency_name": "United Kingdom Pound", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "39", "name": "Greece", "code": "GR", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "40", "name": "Equatorial Guinea", "code": "GQ", "currency": "XAF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "41", "name": "Guadeloupe", "code": "GP", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "42", "name": "Japan", "code": "JP", "currency": "JPY", "symbol": "&#165;", "currency_name": "Japan Yen", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "43", "name": "Guyana", "code": "GY", "currency": "GYD", "symbol": "&#36;", "currency_name": "Guyana Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "44", "name": "Guernsey", "code": "GG", "currency": "GBP", "symbol": "&#163;", "currency_name": "United Kingdom Pound", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "45", "name": "French Guiana", "code": "GF", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "46", "name": "Georgia", "code": "GE", "currency": "GEL", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "47", "name": "Grenada", "code": "GD", "currency": "XCD", "symbol": "&#36;", "currency_name": "East Caribbean Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "48", "name": "United Kingdom", "code": "GB", "currency": "GBP", "symbol": "&#163;", "currency_name": "United Kingdom Pound", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "49", "name": "Gabon", "code": "GA", "currency": "XAF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "50", "name": "El Salvador", "code": "SV", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "51", "name": "Guinea", "code": "GN", "currency": "GNF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "52", "name": "Gambia", "code": "GM", "currency": "GMD", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "53", "name": "Greenland", "code": "GL", "currency": "DKK", "symbol": "&#107;&#114;", "currency_name": "Denmark Krone", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "54", "name": "Gibraltar", "code": "GI", "currency": "GIP", "symbol": "&#163;", "currency_name": "Gibraltar Pound", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "55", "name": "Ghana", "code": "GH", "currency": "GHS", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "56", "name": "Oman", "code": "OM", "currency": "OMR", "symbol": "&#65020;", "currency_name": "Oman Rial", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "57", "name": "Tunisia", "code": "TN", "currency": "TND", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "58", "name": "Jordan", "code": "JO", "currency": "JOD", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "59", "name": "Croatia", "code": "HR", "currency": "HRK", "symbol": "&#107;&#110;", "currency_name": "Croatia Kuna", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "60", "name": "Haiti", "code": "HT", "currency": "HTG", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:46"}, {"id": "61", "name": "Hungary", "code": "HU", "currency": "HUF", "symbol": "&#70;&#116;", "currency_name": "Hungary Forint", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "62", "name": "Hong Kong", "code": "HK", "currency": "HKD", "symbol": "&#36;", "currency_name": "Hong Kong Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "63", "name": "Honduras", "code": "HN", "currency": "HNL", "symbol": "&#76;", "currency_name": "Honduras Lempira", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "64", "name": "Heard Island and McDonald Islands", "code": "HM", "currency": "AUD", "symbol": "&#36;", "currency_name": "Australia Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "65", "name": "Venezuela", "code": "VE", "currency": "VEF", "symbol": "&#66;&#115;", "currency_name": "Venezuela Bolivar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "66", "name": "Puerto Rico", "code": "PR", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "67", "name": "Palestinian Territory", "code": "PS", "currency": "ILS", "symbol": "&#8362;", "currency_name": "Israel Shekel", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "68", "name": "<PERSON><PERSON>", "code": "PW", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "69", "name": "Portugal", "code": "PT", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "70", "name": "Svalbard and <PERSON>", "code": "SJ", "currency": "NOK", "symbol": "&#107;&#114;", "currency_name": "Norway Krone", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "71", "name": "Paraguay", "code": "PY", "currency": "PYG", "symbol": "&#71;&#115;", "currency_name": "Paraguay Guarani", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "72", "name": "Iraq", "code": "IQ", "currency": "IQD", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "73", "name": "Panama", "code": "PA", "currency": "PAB", "symbol": "&#66;&#47;&#46;", "currency_name": "Panama Balboa", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "74", "name": "French Polynesia", "code": "PF", "currency": "XPF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "75", "name": "Papua New Guinea", "code": "PG", "currency": "PGK", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "76", "name": "Peru", "code": "PE", "currency": "PEN", "symbol": "&#83;&#47;&#46;", "currency_name": "Peru Nuevo Sol", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "77", "name": "Pakistan", "code": "PK", "currency": "PKR", "symbol": "&#8360;", "currency_name": "Pakistan Rupee", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "78", "name": "Philippines", "code": "PH", "currency": "PHP", "symbol": "&#8369;", "currency_name": "Philippines Peso", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "79", "name": "Pitcairn", "code": "PN", "currency": "NZD", "symbol": "&#36;", "currency_name": "New Zealand Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "80", "name": "Poland", "code": "PL", "currency": "PLN", "symbol": "&#122;&#322;", "currency_name": "Poland Zloty", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "81", "name": "Saint Pierre and Miquelon", "code": "PM", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "82", "name": "Zambia", "code": "ZM", "currency": "ZMK", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "83", "name": "Western Sahara", "code": "EH", "currency": "MAD", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "84", "name": "Estonia", "code": "EE", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "85", "name": "Egypt", "code": "EG", "currency": "EGP", "symbol": "&#163;", "currency_name": "Egypt Pound", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "86", "name": "South Africa", "code": "ZA", "currency": "ZAR", "symbol": "&#82;", "currency_name": "South Africa Rand", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "87", "name": "Ecuador", "code": "EC", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "88", "name": "Italy", "code": "IT", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "89", "name": "Vietnam", "code": "VN", "currency": "VND", "symbol": "&#8363;", "currency_name": "Viet Nam Dong", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "90", "name": "Solomon Islands", "code": "SB", "currency": "SBD", "symbol": "&#36;", "currency_name": "Solomon Islands Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "91", "name": "Ethiopia", "code": "ET", "currency": "ETB", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "92", "name": "Somalia", "code": "SO", "currency": "SOS", "symbol": "&#83;", "currency_name": "Somalia Shilling", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "93", "name": "Zimbabwe", "code": "ZW", "currency": "ZWL", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "94", "name": "Saudi Arabia", "code": "SA", "currency": "SAR", "symbol": "&#65020;", "currency_name": "Saudi Arabia Riyal", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "95", "name": "Spain", "code": "ES", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "96", "name": "Eritrea", "code": "ER", "currency": "ERN", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "97", "name": "Montenegro", "code": "ME", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "98", "name": "Moldova", "code": "MD", "currency": "MDL", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "99", "name": "Madagascar", "code": "MG", "currency": "MGA", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "100", "name": "Saint <PERSON>", "code": "MF", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "101", "name": "Morocco", "code": "MA", "currency": "MAD", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "102", "name": "Monaco", "code": "MC", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "103", "name": "Uzbekistan", "code": "UZ", "currency": "UZS", "symbol": "&#1083;&#1074;", "currency_name": "Uzbekistan Som", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "104", "name": "Myanmar", "code": "MM", "currency": "MMK", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "105", "name": "Mali", "code": "ML", "currency": "XOF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "106", "name": "Macao", "code": "MO", "currency": "MOP", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "107", "name": "Mongolia", "code": "MN", "currency": "MNT", "symbol": "&#8366;", "currency_name": "Mongolia Tughrik", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "108", "name": "Marshall Islands", "code": "MH", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "109", "name": "Macedonia", "code": "MK", "currency": "MKD", "symbol": "&#1076;&#1077;&#1085;", "currency_name": "Macedonia Denar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "110", "name": "Mauritius", "code": "MU", "currency": "MUR", "symbol": "&#8360;", "currency_name": "Mauritius Rupee", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "111", "name": "Malta", "code": "MT", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "112", "name": "Malawi", "code": "MW", "currency": "MWK", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "113", "name": "Maldives", "code": "MV", "currency": "MVR", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "114", "name": "Martinique", "code": "MQ", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "115", "name": "Northern Mariana Islands", "code": "MP", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "116", "name": "Montserrat", "code": "MS", "currency": "XCD", "symbol": "&#36;", "currency_name": "East Caribbean Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "117", "name": "Mauritania", "code": "MR", "currency": "MRO", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "118", "name": "Isle of Man", "code": "IM", "currency": "GBP", "symbol": "&#163;", "currency_name": "United Kingdom Pound", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "119", "name": "Uganda", "code": "UG", "currency": "UGX", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "120", "name": "Tanzania", "code": "TZ", "currency": "TZS", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "121", "name": "Malaysia", "code": "MY", "currency": "MYR", "symbol": "&#82;&#77;", "currency_name": "Malaysia Ringgit", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "122", "name": "Mexico", "code": "MX", "currency": "MXN", "symbol": "&#36;", "currency_name": "Mexico Peso", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "123", "name": "Israel", "code": "IL", "currency": "ILS", "symbol": "&#8362;", "currency_name": "Israel Shekel", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "124", "name": "France", "code": "FR", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "125", "name": "British Indian Ocean Territory", "code": "IO", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "126", "name": "Saint Helena", "code": "SH", "currency": "SHP", "symbol": "&#163;", "currency_name": "<PERSON>", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "127", "name": "Finland", "code": "FI", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "128", "name": "Fiji", "code": "FJ", "currency": "FJD", "symbol": "&#36;", "currency_name": "Fiji Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "129", "name": "Falkland Islands", "code": "FK", "currency": "FKP", "symbol": "&#163;", "currency_name": "Falkland Islands (Malvinas) Pound", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "130", "name": "Micronesia", "code": "FM", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "131", "name": "Faroe Islands", "code": "FO", "currency": "DKK", "symbol": "&#107;&#114;", "currency_name": "Denmark Krone", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "132", "name": "Nicaragua", "code": "NI", "currency": "NIO", "symbol": "&#67;&#36;", "currency_name": "Nicaragua Cordoba", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "133", "name": "Netherlands", "code": "NL", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "134", "name": "Norway", "code": "NO", "currency": "NOK", "symbol": "&#107;&#114;", "currency_name": "Norway Krone", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "135", "name": "Namibia", "code": "NA", "currency": "NAD", "symbol": "&#36;", "currency_name": "Namibia Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "136", "name": "Vanuatu", "code": "VU", "currency": "VUV", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "137", "name": "New Caledonia", "code": "NC", "currency": "XPF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "138", "name": "Niger", "code": "NE", "currency": "XOF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "139", "name": "Norfolk Island", "code": "NF", "currency": "AUD", "symbol": "&#36;", "currency_name": "Australia Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "140", "name": "Nigeria", "code": "NG", "currency": "NGN", "symbol": "&#8358;", "currency_name": "Nigeria Naira", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "141", "name": "New Zealand", "code": "NZ", "currency": "NZD", "symbol": "&#36;", "currency_name": "New Zealand Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "142", "name": "Nepal", "code": "NP", "currency": "NPR", "symbol": "&#8360;", "currency_name": "Nepal Rupee", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "143", "name": "Nauru", "code": "NR", "currency": "AUD", "symbol": "&#36;", "currency_name": "Australia Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "144", "name": "Niue", "code": "NU", "currency": "NZD", "symbol": "&#36;", "currency_name": "New Zealand Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "145", "name": "Cook Islands", "code": "CK", "currency": "NZD", "symbol": "&#36;", "currency_name": "New Zealand Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "146", "name": "Kosovo", "code": "XK", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "147", "name": "Ivory Coast", "code": "CI", "currency": "XOF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "148", "name": "Switzerland", "code": "CH", "currency": "CHF", "symbol": "&#67;&#72;&#70;", "currency_name": "Switzerland Franc", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "149", "name": "Colombia", "code": "CO", "currency": "COP", "symbol": "&#36;", "currency_name": "Colombia Peso", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "150", "name": "China", "code": "CN", "currency": "CNY", "symbol": "&#165;", "currency_name": "China Yuan Renminbi", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "151", "name": "Cameroon", "code": "CM", "currency": "XAF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:47"}, {"id": "152", "name": "Chile", "code": "CL", "currency": "CLP", "symbol": "&#36;", "currency_name": "Chile Peso", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "153", "name": "Cocos Islands", "code": "CC", "currency": "AUD", "symbol": "&#36;", "currency_name": "Australia Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "154", "name": "Canada", "code": "CA", "currency": "CAD", "symbol": "&#36;", "currency_name": "Canada Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "155", "name": "Republic of the Congo", "code": "CG", "currency": "XAF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:48"}, {"id": "156", "name": "Central African Republic", "code": "CF", "currency": "XAF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:48"}, {"id": "157", "name": "Democratic Republic of the Congo", "code": "CD", "currency": "CDF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:48"}, {"id": "158", "name": "Czech Republic", "code": "CZ", "currency": "CZK", "symbol": "&#75;&#269;", "currency_name": "Czech Republic Koruna", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "159", "name": "Cyprus", "code": "CY", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "160", "name": "Christmas Island", "code": "CX", "currency": "AUD", "symbol": "&#36;", "currency_name": "Australia Dollar", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "161", "name": "Costa Rica", "code": "CR", "currency": "CRC", "symbol": "&#8353;", "currency_name": "Costa Rica Colon", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "162", "name": "Curacao", "code": "CW", "currency": "ANG", "symbol": "&#402;", "currency_name": "Netherlands Antilles Guilder", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 14:53:32"}, {"id": "163", "name": "Cape Verde", "code": "CV", "currency": "CVE", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:47", "updated_at": "2019-12-08 13:42:48"}, {"id": "164", "name": "Cuba", "code": "CU", "currency": "CUP", "symbol": "&#8369;", "currency_name": "Cuba Peso", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "165", "name": "Swaziland", "code": "SZ", "currency": "SZL", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "166", "name": "Syria", "code": "SY", "currency": "SYP", "symbol": "&#163;", "currency_name": "Syria Pound", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "167", "name": "Sint Maarten", "code": "SX", "currency": "ANG", "symbol": "&#402;", "currency_name": "Netherlands Antilles Guilder", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "168", "name": "Kyrgyzstan", "code": "KG", "currency": "KGS", "symbol": "&#1083;&#1074;", "currency_name": "Kyrgyzstan Som", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "169", "name": "Kenya", "code": "KE", "currency": "KES", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "170", "name": "South Sudan", "code": "SS", "currency": "SSP", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "171", "name": "Suriname", "code": "SR", "currency": "SRD", "symbol": "&#36;", "currency_name": "Suriname Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "172", "name": "Kiribati", "code": "KI", "currency": "AUD", "symbol": "&#36;", "currency_name": "Australia Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "173", "name": "Cambodia", "code": "KH", "currency": "KHR", "symbol": "&#6107;", "currency_name": "Cambodia Riel", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "174", "name": "Saint Kitts and Nevis", "code": "KN", "currency": "XCD", "symbol": "&#36;", "currency_name": "East Caribbean Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "175", "name": "Comoros", "code": "KM", "currency": "KMF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "176", "name": "Sao Tome and Principe", "code": "ST", "currency": "STD", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "177", "name": "Slovakia", "code": "SK", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "178", "name": "South Korea", "code": "KR", "currency": "KRW", "symbol": "&#8361;", "currency_name": "Korea (South) Won", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "179", "name": "Slovenia", "code": "SI", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "180", "name": "North Korea", "code": "KP", "currency": "KPW", "symbol": "&#8361;", "currency_name": "Korea (North) Won", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "181", "name": "Kuwait", "code": "KW", "currency": "KWD", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "182", "name": "Senegal", "code": "SN", "currency": "XOF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "183", "name": "San Marino", "code": "SM", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "184", "name": "Sierra Leone", "code": "SL", "currency": "SLL", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "185", "name": "Seychelles", "code": "SC", "currency": "SCR", "symbol": "&#8360;", "currency_name": "Seychelles Rupee", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "186", "name": "Kazakhstan", "code": "KZ", "currency": "KZT", "symbol": "&#1083;&#1074;", "currency_name": "Kazakhstan Tenge", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "187", "name": "Cayman Islands", "code": "KY", "currency": "KYD", "symbol": "&#36;", "currency_name": "Cayman Islands Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "188", "name": "Singapore", "code": "SG", "currency": "SGD", "symbol": "&#36;", "currency_name": "Singapore Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "189", "name": "Sweden", "code": "SE", "currency": "SEK", "symbol": "&#107;&#114;", "currency_name": "Sweden Krona", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "190", "name": "Sudan", "code": "SD", "currency": "SDG", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "191", "name": "Dominican Republic", "code": "DO", "currency": "DOP", "symbol": "&#82;&#68;&#36;", "currency_name": "Dominican Republic Peso", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "192", "name": "Dominica", "code": "DM", "currency": "XCD", "symbol": "&#36;", "currency_name": "East Caribbean Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "193", "name": "Djibouti", "code": "DJ", "currency": "DJF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "194", "name": "Denmark", "code": "DK", "currency": "DKK", "symbol": "&#107;&#114;", "currency_name": "Denmark Krone", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "195", "name": "British Virgin Islands", "code": "VG", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "196", "name": "Germany", "code": "DE", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "197", "name": "Yemen", "code": "YE", "currency": "YER", "symbol": "&#65020;", "currency_name": "Yemen Rial", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "198", "name": "Algeria", "code": "DZ", "currency": "DZD", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "199", "name": "United States", "code": "US", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "200", "name": "Uruguay", "code": "UY", "currency": "UYU", "symbol": "&#36;&#85;", "currency_name": "Uruguay Peso", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "201", "name": "Mayotte", "code": "YT", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "202", "name": "United States Minor Outlying Islands", "code": "UM", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "203", "name": "Lebanon", "code": "LB", "currency": "LBP", "symbol": "&#163;", "currency_name": "Lebanon Pound", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "204", "name": "Saint Lucia", "code": "LC", "currency": "XCD", "symbol": "&#36;", "currency_name": "East Caribbean Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "205", "name": "Laos", "code": "LA", "currency": "LAK", "symbol": "&#8365;", "currency_name": "Laos Kip", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "206", "name": "Tuvalu", "code": "TV", "currency": "AUD", "symbol": "&#36;", "currency_name": "Australia Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "207", "name": "Taiwan", "code": "TW", "currency": "TWD", "symbol": "&#78;&#84;&#36;", "currency_name": "Taiwan New Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "208", "name": "Trinidad and Tobago", "code": "TT", "currency": "TTD", "symbol": "&#84;&#84;&#36;", "currency_name": "Trinidad and Tobago Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "209", "name": "Turkey", "code": "TR", "currency": "TRY", "symbol": "", "currency_name": "Turkey Lira", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "210", "name": "Sri Lanka", "code": "LK", "currency": "LKR", "symbol": "&#8360;", "currency_name": "Sri Lanka Rupee", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "211", "name": "Liechtenstein", "code": "LI", "currency": "CHF", "symbol": "&#67;&#72;&#70;", "currency_name": "Switzerland Franc", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "212", "name": "Latvia", "code": "LV", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "213", "name": "Tonga", "code": "TO", "currency": "TOP", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "214", "name": "Lithuania", "code": "LT", "currency": "LTL", "symbol": "&#76;&#116;", "currency_name": "Lithuania Litas", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "215", "name": "Luxembourg", "code": "LU", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "216", "name": "Liberia", "code": "LR", "currency": "LRD", "symbol": "&#36;", "currency_name": "Liberia Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "217", "name": "Lesotho", "code": "LS", "currency": "LSL", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "218", "name": "Thailand", "code": "TH", "currency": "THB", "symbol": "&#3647;", "currency_name": "Thailand Baht", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "219", "name": "French Southern Territories", "code": "TF", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "220", "name": "Togo", "code": "TG", "currency": "XOF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "221", "name": "Chad", "code": "TD", "currency": "XAF", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "222", "name": "Turks and Caicos Islands", "code": "TC", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "223", "name": "Libya", "code": "LY", "currency": "LYD", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "224", "name": "Vatican", "code": "VA", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "225", "name": "Saint Vincent and the Grenadines", "code": "VC", "currency": "XCD", "symbol": "&#36;", "currency_name": "East Caribbean Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "226", "name": "United Arab Emirates", "code": "AE", "currency": "AED", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "227", "name": "Andorra", "code": "AD", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "228", "name": "Antigua and Barbuda", "code": "AG", "currency": "XCD", "symbol": "&#36;", "currency_name": "East Caribbean Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "229", "name": "Afghanistan", "code": "AF", "currency": "AFN", "symbol": "&#1547;", "currency_name": "Afghanistan Afghani", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "230", "name": "<PERSON><PERSON><PERSON>", "code": "AI", "currency": "XCD", "symbol": "&#36;", "currency_name": "East Caribbean Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "231", "name": "U.S. Virgin Islands", "code": "VI", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "232", "name": "Iceland", "code": "IS", "currency": "ISK", "symbol": "&#107;&#114;", "currency_name": "Iceland Krona", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "233", "name": "Iran", "code": "IR", "currency": "IRR", "symbol": "&#65020;", "currency_name": "Iran Rial", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "234", "name": "Armenia", "code": "AM", "currency": "AMD", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "235", "name": "Albania", "code": "AL", "currency": "ALL", "symbol": "&#76;&#101;&#107;", "currency_name": "Albania Lek", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "236", "name": "Angola", "code": "AO", "currency": "AOA", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "237", "name": "Antarctica", "code": "AQ", "currency": "", "symbol": "", "currency_name": "", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 13:42:48"}, {"id": "238", "name": "American Samoa", "code": "AS", "currency": "USD", "symbol": "&#36;", "currency_name": "United States Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "239", "name": "Argentina", "code": "AR", "currency": "ARS", "symbol": "&#36;", "currency_name": "Argentina Peso", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "240", "name": "Australia", "code": "AU", "currency": "AUD", "symbol": "&#36;", "currency_name": "Australia Dollar", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "241", "name": "Austria", "code": "AT", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "242", "name": "Aruba", "code": "AW", "currency": "AWG", "symbol": "&#402;", "currency_name": "Aruba Guilder", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "243", "name": "India", "code": "IN", "currency": "INR", "symbol": "", "currency_name": "India Rupee", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "244", "name": "Aland Islands", "code": "AX", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "245", "name": "Azerbaijan", "code": "AZ", "currency": "AZN", "symbol": "&#1084;&#1072;&#1085;", "currency_name": "Azerbaijan New Manat", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "246", "name": "Ireland", "code": "IE", "currency": "EUR", "symbol": "&#8364;", "currency_name": "Euro Member Countries", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "247", "name": "Indonesia", "code": "ID", "currency": "IDR", "symbol": "&#82;&#112;", "currency_name": "Indonesia Rupiah", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "248", "name": "Ukraine", "code": "UA", "currency": "UAH", "symbol": "&#8372;", "currency_name": "Ukraine Hryvna", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "249", "name": "Qatar", "code": "QA", "currency": "QAR", "symbol": "&#65020;", "currency_name": "Qatar Riyal", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}, {"id": "250", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": "MZ", "currency": "MZN", "symbol": "&#77;&#84;", "currency_name": "Mozambique Metical", "created_at": "2019-12-08 13:36:48", "updated_at": "2019-12-08 14:53:32"}]