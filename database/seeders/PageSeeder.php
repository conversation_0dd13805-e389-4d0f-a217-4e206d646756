<?php


namespace Database\Seeders;

use App\Page;

class PageSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $pages = $this->getSeedData('pages');

        $pages->each(function ($page) {
            Page::create([
                'id' => $page->id,
                'name' => $page->name,
                'title' => $page->title,
                'slug' => $page->slug,
                'description' => $page->description,
                'components' => json_decode($page->components),
                'created_at' => $page->created_at,
                'updated_at' => $page->updated_at,
                'redirect' => $page->redirect,
            ]);
        });
    }
}
