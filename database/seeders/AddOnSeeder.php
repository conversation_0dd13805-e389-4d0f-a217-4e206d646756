<?php

namespace Database\Seeders;

use App\AddOn;

class AddOnSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $addons = $this->getSeedData('addons');

        $addons->each(function ($addon) {
            AddOn::create([
                'id' => $addon->id,
                'product_id' => $addon->product_id,
                'model_type' => $addon->model_type,
                'model_id' => $addon->model_id,
                'created_at' => $addon->created_at,
                'updated_at' => $addon->updated_at,
            ]);
        });
    }
}
