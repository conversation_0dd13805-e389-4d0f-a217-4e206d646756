<?php

namespace Database\Seeders;

use App\Creator;

class CreatorSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $creators = $this->getSeedData('creators');
        $creators->each(function ($creator) {
            Creator::create([
                'id' => $creator->id,
                'name' => $creator->name,
                'heb_name' => $creator->heb_name,
                'type' => $creator->type,
                'description' => $creator->description,
                'created_at' => $creator->created_at,
                'updated_at' => $creator->updated_at,
                'redirect' => $creator->redirect,
            ]);
        });
    }
}
