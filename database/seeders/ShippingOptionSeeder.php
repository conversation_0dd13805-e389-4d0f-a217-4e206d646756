<?php


namespace Database\Seeders;

use App\ShippingOption;

class ShippingOptionSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $shippingOptions = $this->getSeedData('shipping_options');

        $shippingOptions->each(function ($shippingOption) {
            ShippingOption::create([
                'id' => $shippingOption->id,
                'internal_name' => $shippingOption->internal_name,
                'visible_name' => $shippingOption->visible_name,
                'delivery' => $shippingOption->delivery,
                'created_at' => $shippingOption->created_at,
                'updated_at' => $shippingOption->updated_at,
                'operational_days' => $shippingOption->operational_days
            ]);
        });
    }
}
