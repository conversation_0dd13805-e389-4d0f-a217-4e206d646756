<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call(CountrySeeder::class);
        $this->call(SettingSeeder::class);
        $this->call(ClosedDaySeeder::class);
        $this->call(FulfillmentSchematicSeeder::class);
        $this->call(PageSeeder::class);
        $this->call(ZipCodeSeeder::class);
        $this->call(GiftCardSettingSeeder::class);
        $this->call(GiftNotesSettingSeeder::class);
        $this->call(GiftOptionSeeder::class);
        $this->call(GroupSeeder::class);
        $this->call(InternationalShippingZoneSeeder::class);
        $this->call(InternationalShippingOptionShippingZoneSeeder::class);
        $this->call(MenuSeeder::class);
        $this->call(ProductTypeSeeder::class);
        $this->call(ShippingOptionSeeder::class);
        $this->call(ShippingZoneSeeder::class);
        $this->call(ShippingOptionShippingZoneSeeder::class);
        $this->call(CategorySeeder::class);
        $this->call(AutomatedCategorySeeder::class);
        $this->call(SubscriptionTypeSeeder::class);
        $this->call(SubscriptionGroupSeeder::class);

        $this->call(AddOnSeeder::class);
        $this->call(BundleCategorySeeder::class);
        $this->call(BundleItemSeeder::class);
        $this->call(BundleSeeder::class);
        $this->call(CategoryFilterSeeder::class);
        $this->call(CategoryProductSeeder::class);
        $this->call(CombinationSeeder::class);
        $this->call(CreatorProductSeeder::class);
        $this->call(CreatorSeeder::class);
        $this->call(FilterItemBundleSeeder::class);
        $this->call(FilterItemProductSeeder::class);
        $this->call(FilterItemSeeder::class);
        $this->call(FilterSeeder::class);
        $this->call(OnlinePriceSeeder::class);
        $this->call(ProductSeeder::class);

        $this->call(ProductRecurringSettingSeeder::class);
        $this->call(SubscriptionGroupItemSeeder::class);
        $this->call(SubscriptionTypeItemSeeder::class);

        $this->call(TagSeeder::class);
        $this->call(TaggableSeeder::class);

        $this->call(AdminSeeder::class);
    }
}
