<?php


namespace Database\Seeders;

use App\ShippingZone;

class ShippingZoneSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $shippingZones = $this->getSeedData('shipping_zones');

        $shippingZones->each(function ($shippingZone) {
           ShippingZone::create([
                'id' => $shippingZone->id,
                'name' => $shippingZone->name,
                'zip_codes_text' => $shippingZone->zip_codes_text,
                'created_at' => $shippingZone->created_at,
                'updated_at' => $shippingZone->updated_at,
            ]);
        });
    }
}
