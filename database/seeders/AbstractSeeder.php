<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;

abstract class AbstractSeeder extends Seeder
{
    protected function getSeedData($file): Collection
    {
        $data = collect(
            json_decode(
                File::get(
                    base_path("database/data/{$file}.json")
                )
            )
        );
        $this->command->info("Seeding {$file}: {$data->count()} records");
        return $data;
    }
}
