<?php

namespace Database\Seeders;

use App\Tag;

class TagSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $tags = $this->getSeedData('tags');
        $tags->each(function ($tag) {
            Tag::create([
                'id' => $tag->id,
                'name' => json_decode($tag->name),
                'slug' => json_decode($tag->slug),
                'type' => $tag->type,
                'order_column' => $tag->order_column,
                'created_at' => $tag->created_at,
                'updated_at' => $tag->updated_at,
            ]);
        });
    }
}
