<?php

namespace Database\Seeders;

use App\BundleItem;

class BundleItemSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $bundleItems = $this->getSeedData('bundle_items');

        $bundleItems->each(function ($bundleItem) {
            BundleItem::create([
                'id' => $bundleItem->id,
                'bundle_id' => $bundleItem->bundle_id,
                'quantity' => $bundleItem->quantity,
                'model_type' => $bundleItem->model_type,
                'model_id' => $bundleItem->model_id,
                'created_at' => $bundleItem->created_at,
                'updated_at' => $bundleItem->updated_at,
            ]);
        });
    }
}
