<?php


namespace Database\Seeders;

use App\FulfillmentSchematic;

class FulfillmentSchematicSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $fulfillment_schematics = $this->getSeedData('fulfillment_schematics');

        $fulfillment_schematics->each(function ($fulfillment_schematic) {
            FulfillmentSchematic::create([
                'id' => $fulfillment_schematic->id,
                'name' => $fulfillment_schematic->name,
                'delivery' => $fulfillment_schematic->delivery,
                'cutoff' => $fulfillment_schematic->cutoff,
                'cutoff_is_hourly' => $fulfillment_schematic->cutoff_is_hourly,
                'sunday_open' => $fulfillment_schematic->sunday_open,
                'monday_open' => $fulfillment_schematic->monday_open,
                'tuesday_open' => $fulfillment_schematic->tuesday_open,
                'wednesday_open' => $fulfillment_schematic->wednesday_open,
                'thursday_open' => $fulfillment_schematic->thursday_open,
                'friday_open' => $fulfillment_schematic->friday_open,
                'saturday_open' => $fulfillment_schematic->saturday_open,
                'sunday_close' => $fulfillment_schematic->sunday_close,
                'monday_close' => $fulfillment_schematic->monday_close,
                'tuesday_close' => $fulfillment_schematic->tuesday_close,
                'wednesday_close' => $fulfillment_schematic->wednesday_close,
                'thursday_close' => $fulfillment_schematic->thursday_close,
                'friday_close' => $fulfillment_schematic->friday_close,
                'saturday_close' => $fulfillment_schematic->saturday_close,
                'created_at' => $fulfillment_schematic->created_at,
                'updated_at' => $fulfillment_schematic->updated_at,
            ]);
        });
    }
}
