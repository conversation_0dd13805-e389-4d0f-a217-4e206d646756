<?php

namespace Database\Seeders;

use Illuminate\Support\Facades\DB;

class CategoryFilterSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $categoryFilters = $this->getSeedData('category_filter');
        $categoryFilters->each(function ($categoryFilter) {
            DB::table('category_filter')->insert([
                'id' => $categoryFilter->id,
                'category_id' => $categoryFilter->category_id,
                'filter_id' => $categoryFilter->filter_id,
                'created_at' => $categoryFilter->created_at,
                'updated_at' => $categoryFilter->updated_at,
            ]);
        });
    }
}
