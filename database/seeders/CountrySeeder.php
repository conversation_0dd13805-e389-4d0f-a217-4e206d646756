<?php

namespace Database\Seeders;

use App\Country;

class CountrySeeder extends AbstractSeeder
{
    public function run(): void
    {
        $countries = $this->getSeedData('countries');

        $countries->each(function ($country) {
            Country::create([
                'id' => $country->id,
                'name' => $country->name,
                'code' => $country->code,
                'currency' => $country->currency,
                'symbol' => $country->symbol,
                'currency_name' => $country->currency_name,
                'created_at' => $country->created_at,
                'updated_at' => $country->updated_at,
            ]);
        });
    }
}
