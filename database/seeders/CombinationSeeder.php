<?php

namespace Database\Seeders;

use App\Combination;

class CombinationSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $combinations = $this->getSeedData('combinations');
        $combinations->each(function ($combination) {
            Combination::create([
                'id' => $combination->id,
                'type_id' => $combination->type_id,
                'group_id' => $combination->group_id,
                'upcoming_products' => $combination->upcoming_products,
                'upcoming_date' => $combination->upcoming_date,
                'upcoming_string' => $combination->upcoming_string,
                'upcoming_filter_item_id' => $combination->upcoming_filter_item_id,
                'next_products' => $combination->next_products,
                'next_date' => $combination->next_date,
                'next_string' => $combination->next_string,
                'next_filter_item_id' => $combination->next_filter_item_id,
                'filter_name' => $combination->filter_name,
                'from_price' => $combination->from_price,
                'to_price' => $combination->to_price,
                'created_at' => $combination->created_at,
                'updated_at' => $combination->updated_at,
            ]);
        });
    }
}
