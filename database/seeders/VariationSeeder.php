<?php

namespace Database\Seeders;

use App\Variation;

class VariationSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $variations = $this->getSeedData('variations');
        $variations->each(function ($variation) {
            Variation::create([
                'id' => $variation->id,
                'model_type' => $variation->model_type,
                'model_id' => $variation->model_id,
                'name' => $variation->name,
                'info' => $variation->info,
                'values' => json_decode($variation->values),
                'description' => $variation->description,
                'filter' => $variation->filter,
                'assistance' => $variation->assistance,
                'order' => $variation->order,
                'created_at' => $variation->created_at,
                'updated_at' => $variation->updated_at,
                'images' => json_decode($variation->images),
            ]);
        });
    }
}
