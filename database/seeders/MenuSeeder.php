<?php

namespace Database\Seeders;

use Outl1ne\MenuBuilder\Models\Menu;

class MenuSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $menu = Menu::create([
            'name' => 'Departments',
            'slug' => 'departments',
        ]);

        $menu_items = $this->getSeedData('menu_items');

        $menu_items->each(function ($menu_item) use ($menu) {
            $menu->rootMenuItems()->create([
                'id' => $menu_item->id,
                'name' => $menu_item->name,
                'class' => $menu_item->class,
                'value' => $menu_item->value,
                'target' => $menu_item->target,
                'data' => $menu_item->parameters,
                'parent_id' => $menu_item->parent_id,
                'order' => $menu_item->order,
                'enabled' => $menu_item->enabled,
                'created_at' => $menu_item->created_at,
                'updated_at' => $menu_item->updated_at,
                'locale' => 'en_US',
            ]);
        });
    }
}
