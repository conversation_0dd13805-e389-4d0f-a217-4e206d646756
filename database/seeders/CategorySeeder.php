<?php

namespace Database\Seeders;

use App\Category;

class CategorySeeder extends AbstractSeeder
{
    public function run(): void
    {
        $categories = $this->getSeedData('categories');

        $categories->each(function ($category) {
            Category::create([
                'id' => $category->id,
                'name' => $category->name,
                'start' => $category->start,
                'end' => $category->end,
                "_lft" => $category->_lft,
                "_rgt" => $category->_rgt,
                "parent_id" => $category->parent_id,
                "created_at" => $category->created_at,
                "updated_at" => $category->updated_at,
                "redirect" => $category->redirect,
                "sort_by" => $category->sort_by,
                "seo_desc" => $category->seo_desc,
                "seo_title" => $category->seo_title,
            ]);
        });
    }
}
