<?php

namespace Database\Seeders;

use App\ZipCode;

class ZipCodeSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $zip_codes = $this->getSeedData('zip_codes');

        $zip_codes->each(function ($zip_code) {
            ZipCode::create([
                'id' => $zip_code->id,
                'zip_code' => $zip_code->zip_code,
                'state' => $zip_code->state,
                'tax_rate' => $zip_code->tax_rate,
                'shipping_zone_id' => $zip_code->shipping_zone_id,
                'created_at' => $zip_code->created_at,
                'updated_at' => $zip_code->updated_at,
            ]);
        });
    }
}
