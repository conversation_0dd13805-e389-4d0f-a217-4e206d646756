<?php

namespace Database\Seeders;

use App\SubscriptionGroupItem;

class SubscriptionGroupItemSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $subscription_group_items = $this->getSeedData('subscription_group_items');

        $subscription_group_items->each(function ($subscription_group_item) {
            SubscriptionGroupItem::create([
                'id' => $subscription_group_item->id,
                'model_type' => $subscription_group_item->model_type,
                'model_id' => $subscription_group_item->model_id,
                'product_id' => $subscription_group_item->product_id,
                'subscription_group_id' => $subscription_group_item->subscription_group_id,
                'created_at' => $subscription_group_item->created_at,
                'updated_at' => $subscription_group_item->updated_at,
            ]);
        });
    }
}
