<?php

namespace Database\Seeders;

use Illuminate\Support\Facades\DB;

class CreatorProductSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $creatorProducts = $this->getSeedData('creator_product');
        $creatorProducts->each(function ($creatorProduct) {
            DB::table('creator_product')->insert([
                'id' => $creatorProduct->id,
                'creator_id' => $creatorProduct->creator_id,
                'product_id' => $creatorProduct->product_id,
                'created_at' => $creatorProduct->created_at,
                'updated_at' => $creatorProduct->updated_at,
            ]);
        });
    }
}
