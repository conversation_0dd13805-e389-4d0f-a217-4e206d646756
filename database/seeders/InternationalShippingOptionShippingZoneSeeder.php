<?php


namespace Database\Seeders;

use App\InternationalShippingOptionShippingZone;

class InternationalShippingOptionShippingZoneSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $shipping_options = $this->getSeedData('international_shipping_option_shipping_zone');

        $shipping_options->each(function ($shipping_option) {
            InternationalShippingOptionShippingZone::create([
                'id' => $shipping_option->id,
                'shipping_option_id' => $shipping_option->shipping_option_id,
                'international_shipping_zone_id' => $shipping_option->international_shipping_zone_id,
                'base_rate' => $shipping_option->base_rate,
                'free_shipping_above' => $shipping_option->free_shipping_above,
                'active' => $shipping_option->active,
                'carrier' => $shipping_option->carrier,
                'carrier_info' => $shipping_option->carrier_info,
                'service' => $shipping_option->service,
                'service_info' => $shipping_option->service_info,
                'created_at' => $shipping_option->created_at,
                'updated_at' => $shipping_option->updated_at,
            ]);
        });
    }
}
