<?php

namespace Database\Seeders;

use App\FilterItem;

class FilterItemSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $filterItems = $this->getSeedData('filter_items');
        $filterItems->each(function ($filterItem) {
            FilterItem::create([
                'id' => $filterItem->id,
                'name' => $filterItem->name,
                'order' => $filterItem->order,
                'filter_id' => $filterItem->filter_id,
                'created_at' => $filterItem->created_at,
                'updated_at' => $filterItem->updated_at,
            ]);
        });
    }
}
