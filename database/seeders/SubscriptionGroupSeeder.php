<?php

namespace Database\Seeders;

use App\SubscriptionGroup;

class SubscriptionGroupSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $subscription_groups = $this->getSeedData('subscription_groups');

        $subscription_groups->each(function ($subscription_group) {
            SubscriptionGroup::create([
                'id' => $subscription_group->id,
                'name' => $subscription_group->name,
                'filter_id' => $subscription_group->filter_id,
                'created_at' => $subscription_group->created_at,
                'updated_at' => $subscription_group->updated_at,
            ]);
        });
    }
}
