<?php

namespace Database\Seeders;

use App\AutomatedCategory;

class AutomatedCategorySeeder extends AbstractSeeder
{
    public function run(): void
    {
        $automated_categories = $this->getSeedData('automated_categories');

        $automated_categories->each(function ($automated_category) {
            AutomatedCategory::create([
                'id' => $automated_category->id,
                'name' => $automated_category->name,
                'rule' => $automated_category->rule,
                'status' => $automated_category->status,
                'sort_by' => $automated_category->sort_by,
                'contains' => $automated_category->contains,
                'categories_and' => $automated_category->categories_and,
                'categories_or' => $automated_category->categories_or,
                'filters_and' => $automated_category->filters_and,
                'filters_or' => $automated_category->filters_or,
                'vendors_and' => $automated_category->vendors_and,
                'vendors_or' => $automated_category->vendors_or,
                'creators_and' => $automated_category->creators_and,
                'creators_or' => $automated_category->creators_or,
                'tags_and' => $automated_category->tags_and,
                'tags_or' => $automated_category->tags_or,
                'labels_and' => $automated_category->labels_and,
                'labels_or' => $automated_category->labels_or,
                'from_price' => $automated_category->from_price,
                'to_price' => $automated_category->to_price,
                'created_at' => $automated_category->created_at,
                'updated_at' => $automated_category->updated_at,
                'redirect' => $automated_category->redirect,
                'description' => $automated_category->description,
                'seo_title' => $automated_category->seo_title,
                'seo_desc' => $automated_category->seo_desc,
                'categories_exclude' => $automated_category->categories_exclude,
                'filters_exclude' => $automated_category->filters_exclude,
                'vendors_exclude' => $automated_category->vendors_exclude,
                'creators_exclude' => $automated_category->creators_exclude,
                'tags_exclude' => $automated_category->tags_exclude,
                'labels_exclude' => $automated_category->labels_exclude,
            ]);
        });
    }
}
