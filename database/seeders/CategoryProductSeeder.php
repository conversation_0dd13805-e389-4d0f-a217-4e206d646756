<?php

namespace Database\Seeders;

use Illuminate\Support\Facades\DB;

class CategoryProductSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $categoryProducts = $this->getSeedData('category_product');
        $categoryProducts->each(function ($categoryProduct) {
            DB::table('category_product')->insert([
                'id' => $categoryProduct->id,
                'category_id' => $categoryProduct->category_id,
                'product_id' => $categoryProduct->product_id,
                'created_at' => $categoryProduct->created_at,
                'updated_at' => $categoryProduct->updated_at,
            ]);
        });
    }
}
