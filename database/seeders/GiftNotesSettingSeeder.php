<?php


namespace Database\Seeders;

use App\GiftNotesSetting;

class GiftNotesSettingSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $giftNotesSettings = $this->getSeedData('gift_notes_settings');

        $giftNotesSettings->each(function ($giftNotesSetting) {
            GiftNotesSetting::create([
                'id' => $giftNotesSetting->id,
                'name' => $giftNotesSetting->name,
                'note' => $giftNotesSetting->note,
                'created_at' => $giftNotesSetting->created_at,
                'updated_at' => $giftNotesSetting->updated_at,
            ]);
        });
    }
}
