<?php

namespace Database\Seeders;

use Illuminate\Support\Facades\DB;

class TaggableSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $taggables = $this->getSeedData('taggables');
        $taggables->each(function ($taggable) {
            DB::table('taggables')->insert([
                'tag_id' => $taggable->tag_id,
                'taggable_id' => $taggable->taggable_id,
                'taggable_type' => $taggable->taggable_type,
            ]);
        });
    }
}
