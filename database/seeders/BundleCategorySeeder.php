<?php

namespace Database\Seeders;

use Illuminate\Support\Facades\DB;

class BundleCategorySeeder extends AbstractSeeder
{
    public function run(): void
    {
        $bundleCategories = $this->getSeedData('bundle_category');

        $bundleCategories->each(function ($bundleCategory) {
            DB::table('bundle_category')->insert([
                'id' => $bundleCategory->id,
                'bundle_id' => $bundleCategory->bundle_id,
                'category_id' => $bundleCategory->category_id,
                'created_at' => $bundleCategory->created_at,
                'updated_at' => $bundleCategory->updated_at,
            ]);
        });
    }
}
