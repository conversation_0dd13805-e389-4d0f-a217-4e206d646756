<?php

namespace Database\Seeders;

use App\SubscriptionType;

class SubscriptionTypeSeeder extends AbstractSeeder
{

    public function run(): void
    {
        $subscription_types = $this->getSeedData('subscription_types');

        $subscription_types->each(function ($subscription_type) {
            SubscriptionType::create([
                'id' => $subscription_type->id,
                'name' => $subscription_type->name,
                'filter_id' => $subscription_type->filter_id,
                'created_at' => $subscription_type->created_at,
                'updated_at' => $subscription_type->updated_at,
            ]);
        });
    }
}
