<?php

namespace Database\Seeders;

use Illuminate\Support\Facades\DB;

class ProductRecurringSettingSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $productRecurringSettings = $this->getSeedData('product_recurring_setting');
        $productRecurringSettings->each(function ($productRecurringSetting) {
            DB::table('product_recurring_setting')->insert([
                'id' => $productRecurringSetting->id,
                'product_id' => $productRecurringSetting->product_id,
                'recurring_setting_id' => $productRecurringSetting->recurring_setting_id,
            ]);
        });
    }
}
