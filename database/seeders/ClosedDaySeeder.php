<?php


namespace Database\Seeders;

use App\ClosedDay;

class ClosedDaySeeder extends AbstractSeeder
{
    public function run(): void
    {
        $closed_days = $this->getSeedData('closed_days');

        $closed_days->each(function ($closed_day) {
            ClosedDay::create([
                'id' => $closed_day->id,
                'name' => $closed_day->name,
                'date' => $closed_day->date,
                'delivery' => $closed_day->delivery,
                'pickup' => $closed_day->pickup,
                'open' => $closed_day->open,
                'close' => $closed_day->close,
                'source' => $closed_day->source,
                'created_at' => $closed_day->created_at,
                'updated_at' => $closed_day->updated_at,
            ]);
        });
    }
}
