<?php

namespace Database\Seeders;


use Illuminate\Support\Facades\DB;

class FilterItemProductSeeder extends AbstractSeeder
{
    public function run(): void
    {
        for ($fileIndex = 1; $fileIndex <= 5; $fileIndex++) {
            $filterItemProducts = $this->getSeedData("filter_item_product/filter_item_product_$fileIndex");
            $filterItemProducts->each(function ($filterItemProduct) {
                DB::table('filter_item_product')->insert([
                    'id' => $filterItemProduct->id,
                    'product_id' => $filterItemProduct->product_id,
                    'filter_item_id' => $filterItemProduct->filter_item_id,
                    'created_at' => $filterItemProduct->created_at,
                    'updated_at' => $filterItemProduct->updated_at,
                ]);
            });
        }
    }
}
