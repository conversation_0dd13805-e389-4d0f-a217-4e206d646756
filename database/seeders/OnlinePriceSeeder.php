<?php

namespace Database\Seeders;

use App\OnlinePrice;

class OnlinePriceSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $onlinePrices = $this->getSeedData('online_prices');
        $onlinePrices->each(function ($onlinePrice) {
            OnlinePrice::create([
                'id' => $onlinePrice->id,
                'model_type' => $onlinePrice->model_type,
                'model_id' => $onlinePrice->model_id,
                'based_on' => $onlinePrice->based_on,
                'percent' => $onlinePrice->percent,
                'created_at' => $onlinePrice->created_at,
                'updated_at' => $onlinePrice->updated_at,
            ]);
        });
    }
}
