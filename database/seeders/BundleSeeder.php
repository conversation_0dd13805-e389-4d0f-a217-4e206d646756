<?php

namespace Database\Seeders;

use App\Bundle;

class BundleSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $bundles = $this->getSeedData('bundles');

        $bundles->each(function ($bundle) {
            Bundle::create([
                'id' => $bundle->id,
                'title' => $bundle->title,
                'slug' => $bundle->slug,
                'description' => $bundle->description,
                'short_desc' => $bundle->short_desc,
                'heb_title' => $bundle->heb_title,
                'heb_description' => $bundle->heb_description,
                'heb_short_desc' => $bundle->heb_short_desc,
                'price' => $bundle->price,
                'barcode' => $bundle->barcode,
                'sku' => $bundle->sku,
                'meta' => $bundle->meta,
                'tax_code' => $bundle->tax_code,
                'visibility' => $bundle->visibility,
                'show_images' => $bundle->show_images,
                'show_products' => $bundle->show_products,
                'publish' => $bundle->publish,
                'search' => $bundle->search,
                'lists_id' => $bundle->lists_id,
                'created_at' => $bundle->created_at,
                'updated_at' => $bundle->updated_at,
            ]);
        });
    }
}
