<?php

namespace Database\Seeders;

use App\Filter;

class FilterSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $filters = $this->getSeedData('filters');
        $filters->each(function ($filter) {
            Filter::create([
                'id' => $filter->id,
                'name' => $filter->name,
                'info' => $filter->info,
                'options' => $filter->options,
                'show' => $filter->show,
                'assistant' => $filter->assistant,
                'created_at' => $filter->created_at,
                'updated_at' => $filter->updated_at,
                'sort_order' => $filter->sort_order,
            ]);
        });
    }
}
