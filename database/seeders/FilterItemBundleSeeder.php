<?php

namespace Database\Seeders;


use Illuminate\Support\Facades\DB;

class FilterItemBundleSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $filterItemBundles = $this->getSeedData('filter_item_bundle');
        $filterItemBundles->each(function ($filterItemBundle) {
            DB::table('filter_item_bundle')->insert([
                'id' => $filterItemBundle->id,
                'bundle_id' => $filterItemBundle->bundle_id,
                'filter_item_id' => $filterItemBundle->filter_item_id,
                'created_at' => $filterItemBundle->created_at,
                'updated_at' => $filterItemBundle->updated_at,
            ]);
        });
    }
}
