<?php

namespace Database\Seeders;

use App\ProductType;

class ProductTypeSeeder extends AbstractSeeder
{
    public function run()
    {
        $productTypes = $this->getSeedData('product_types');

        $productTypes->each(function ($productType) {
            ProductType::create([
                'id' => $productType->id,
                'name' => $productType->name,
                'creator_type' => $productType->creator_type,
                'fields' => json_decode($productType->fields),
                'variations' => json_decode($productType->variations),
                'created_at' => $productType->created_at,
                'updated_at' => $productType->updated_at,
            ]);
        });
    }
}
