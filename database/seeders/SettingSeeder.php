<?php


namespace Database\Seeders;

use App\Setting;

class SettingSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $settings = $this->getSeedData('settings');

        $settings->each(function ($setting) {
            Setting::create([
                'id' => $setting->id,
                'key' => $setting->key,
                'value' => $setting->value,
                'admin' => $setting->admin,
                'helper' => $setting->helper,
                'created_at' => $setting->created_at,
                'updated_at' => $setting->updated_at,
            ]);
        });
    }
}
