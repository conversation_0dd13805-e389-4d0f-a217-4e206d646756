<?php


namespace Database\Seeders;

use App\InternationalShippingZone;

class InternationalShippingZoneSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $shipping_zones = $this->getSeedData('international_shipping_zones');

        $shipping_zones->each(function ($shipping_zone) {
            InternationalShippingZone::create([
                'id' => $shipping_zone->id,
                'name' => $shipping_zone->name,
                'countries_text' => $shipping_zone->countries_text,
                'created_at' => $shipping_zone->created_at,
                'updated_at' => $shipping_zone->updated_at,
            ]);
        });
    }
}
