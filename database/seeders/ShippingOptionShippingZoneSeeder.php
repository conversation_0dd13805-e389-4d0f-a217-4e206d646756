<?php


namespace Database\Seeders;

use App\ShippingOptionShippingZone;

class ShippingOptionShippingZoneSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $shippingOptionShippingZones = $this->getSeedData('shipping_option_shipping_zone');

        $shippingOptionShippingZones->each(function ($shippingOptionShippingZone) {
            ShippingOptionShippingZone::create([
                'id' => $shippingOptionShippingZone->id,
                'shipping_option_id' => $shippingOptionShippingZone->shipping_option_id,
                'shipping_zone_id' => $shippingOptionShippingZone->shipping_zone_id,
                'price_per_pound' => $shippingOptionShippingZone->price_per_pound,
                'base_rate' => $shippingOptionShippingZone->base_rate,
                'duration' => $shippingOptionShippingZone->duration,
                'duration_is_hourly' => $shippingOptionShippingZone->duration_is_hourly,
                'free_shipping_above' => $shippingOptionShippingZone->free_shipping_above,
                'type' => $shippingOptionShippingZone->type,
                'active' => $shippingOptionShippingZone->active,
                'created_at' => $shippingOptionShippingZone->created_at,
                'updated_at' => $shippingOptionShippingZone->updated_at,
                'key_values' => json_decode($shippingOptionShippingZone->key_values, true),
            ]);
        });
    }
}
