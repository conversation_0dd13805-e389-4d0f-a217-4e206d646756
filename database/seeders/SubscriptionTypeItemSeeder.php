<?php

namespace Database\Seeders;

use App\SubscriptionTypeItem;

class SubscriptionTypeItemSeeder extends AbstractSeeder
{
    public function run(): void
    {
        $subscription_type_items = $this->getSeedData('subscription_type_items');
        $subscription_type_items->each(function ($subscription_type_item) {
            SubscriptionTypeItem::create([
                'id' => $subscription_type_item->id,
                'date' => $subscription_type_item->date,
                'subscription_type_id' => $subscription_type_item->subscription_type_id,
                'filter_item_id' => $subscription_type_item->filter_item_id,
            ]);
        });
    }
}
