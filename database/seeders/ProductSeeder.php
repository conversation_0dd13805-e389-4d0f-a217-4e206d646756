<?php

namespace Database\Seeders;

use Illuminate\Support\Facades\DB;

class ProductSeeder extends AbstractSeeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        for ($fileIndex = 1; $fileIndex <= 46; $fileIndex++) {
            $products = $this->getSeedData("products/products_$fileIndex");
            $products->each(function ($product) {
                DB::table('products')->insert([
                    'id' => $product->id,
                    'title' => $product->title,
                    'slug' => $product->slug,
                    'description' => isset($product->description) ? $product->description : null,
                    'short_desc' => isset($product->short_desc) ? $product->short_desc : null,
                    'heb_title' => isset($product->heb_title) ? $product->heb_title : null,
                    'heb_description' => isset($product->heb_description) ? $product->heb_description : null,
                    'heb_short_desc' => isset($product->heb_short_desc) ? $product->heb_short_desc : null,
                    'list_price' => isset($product->list_price) ? $product->list_price : null,
                    'store_price' => isset($product->store_price) ? $product->store_price : null,
                    'online_price' => isset($product->online_price) ? $product->online_price : null,
                    'sale_price' => isset($product->sale_price) ? $product->sale_price : null,
                    'cost_price' => isset($product->cost_price) ? $product->cost_price : null,
                    'barcode' => isset($product->barcode) ? $product->barcode : null,
                    'gtni' => isset($product->gtni) ? $product->gtni : null,
                    'sku' => isset($product->sku) ? $product->sku : null,
                    'item_type' => $product->item_type,
                    'width' => isset($product->width) ? $product->width : null,
                    'height' => isset($product->height) ? $product->height : null,
                    'length' => isset($product->length) ? $product->length : null,
                    'weight' => isset($product->weight) ? $product->weight : null,
                    'boxes' => isset($product->boxes) ? $product->boxes : null,
                    'origin' => isset($product->origin) ? $product->origin : null,
                    'system_code' => isset($product->system_code) ? $product->system_code : null,
                    'store_quantity' => isset($product->store_quantity) ? $product->store_quantity : null,
                    'website_quantity' => isset($product->website_quantity) ? $product->website_quantity : null,
                    'track_inventory' => isset($product->track_inventory) ? $product->track_inventory : null,
                    'meta' => isset($product->meta) ? $product->meta : null,
                    'pos_meta' => isset($product->pos_meta) ? $product->pos_meta : null,
                    'store_title' => isset($product->store_title) ? $product->store_title : null,
                    'product_type_id' => isset($product->product_type_id) ? $product->product_type_id : null,
                    'vendor_id' => isset($product->vendor_id) ? $product->vendor_id : null,
                    'label_id' => isset($product->label_id) ? $product->label_id : null,
                    'personalizes_id' => isset($product->personalizes_id) ? $product->personalizes_id : null,
                    'tax_code' => isset($product->tax_code) ? $product->tax_code : null,
                    'visibility' => isset($product->visibility) ? $product->visibility : null,
                    'publish' => isset($product->publish) ? $product->publish : null,
                    'expire' => isset($product->expire) ? $product->expire : null,
                    'release_date' => isset($product->release_date) ? $product->release_date : null,
                    'duration' => isset($product->duration) ? $product->duration : null,
                    'search' => isset($product->search) ? $product->search : null,
                    'file_format' => isset($product->file_format) ? $product->file_format : null,
                    'max_quantity' => isset($product->max_quantity) ? $product->max_quantity : null,
                    'exclude_free_shipping' => isset($product->exclude_free_shipping) ? $product->exclude_free_shipping : null,
                    'exclude_from_returns' => isset($product->exclude_from_returns) ? $product->exclude_from_returns : null,
                    'notification' => isset($product->notification) ? $product->notification : null,
                    'var_skus' => isset($product->var_skus) ? $product->var_skus : null,
                    'popularity_score' => isset($product->popularity_score) ? $product->popularity_score : null,
                    'seo_title' => isset($product->seo_title) ? $product->seo_title : null,
                    'seo_desc' => isset($product->seo_desc) ? $product->seo_desc : null,
                    'store_vendor' => isset($product->store_vendor) ? $product->store_vendor : null,
                    'store_category' => isset($product->store_category) ? $product->store_category : null,
                    'store_sub_category' => isset($product->store_sub_category) ? $product->store_sub_category : null,
                    'not_photographed' => isset($product->not_photographed) ? $product->not_photographed : null,
                    'created_at' => isset($product->created_at) ? $product->created_at : null,
                    'updated_at' => isset($product->updated_at) ? $product->updated_at : null,
                ]);
            });
        }
    }
}
