<?php

return [
    'searchableAttributes' => [
        'title',
        'heb_title',
        'item_type',
        'path',
        'price',
        'sale_price',
        'label',
        'from_price',
        'fake_price',
        'vendor',
        'short_desc',
        'description',
        'heb_short_desc',
        'heb_description',
        'release_date',
        'sku',
        'barcode',
        'meta',
        'creators',
        'filters',
        'questions',
        'side_variations',
        'variations',
    ],
    'customRanking' => null,
    'removeStopWords' => null,
    'disableTypoToleranceOnAttributes' => ['sku', 'barcode'],
    'attributesForFaceting' => [
        'categories',
        'creators',
        'filters',
        'questions',
        'variations',
        'vendors',
        'tags',
    ],
    'unretrievableAttributes' => null,
    'ignorePlurals' => null,
    'queryLanguages' => ['en'],
    'distinct' => null,
    'attributeForDistinct' => null,
];
