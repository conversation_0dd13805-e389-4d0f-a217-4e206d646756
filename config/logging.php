<?php

use Monolog\Handler\StreamHandler;
use Monolog\Handler\SyslogUdpHandler;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Log Channel
    |--------------------------------------------------------------------------
    |
    | This option defines the default log channel that gets used when writing
    | messages to the logs. The name specified in this option should match
    | one of the channels defined in the "channels" configuration array.
    |
    */

    'default' => env('LOG_CHANNEL', 'stack'),

    /*
    |--------------------------------------------------------------------------
    | Log Channels
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log channels for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Drivers: "single", "daily", "slack", "syslog",
    |                    "errorlog", "monolog",
    |                    "custom", "stack"
    |
    */

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['daily', 'bugsnag'],
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => 'debug',
        ],

        'tail' => [
            'driver' => 'single',
            'path' => storage_path('logs/tail.log'),
            'level' => 'debug',
        ],

        'sql' => [
            'driver' => 'single',
            'path' => storage_path('logs/sql.log'),
            'level' => 'info',
        ],

        'orders' => [
            'driver' => 'daily',
            'path' => storage_path('logs/orders.log'),
            'level' => 'info',
            'days' => 30,
        ],
        'backup' => [
            'driver' => 'single',
            'path' => storage_path('logs/backup.log'),
            'level' => 'info',
        ],

        'pos-inventory' => [
            'driver' => 'single',
            'path' => storage_path('logs/pos-inventory.log'),
            'level' => 'info',
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/laravel.log'),
            'level' => 'debug',
            'days' => 30,
        ],

        'slack' => [
            'driver' => 'slack',
            'url' => env('LOG_SLACK_WEBHOOK_URL'),
            'username' => 'Laravel Log',
            'emoji' => ':boom:',
            'level' => 'critical',
        ],

        'papertrail' => [
            'driver' => 'monolog',
            'level' => 'debug',
            'handler' => SyslogUdpHandler::class,
            'handler_with' => [
                'host' => env('PAPERTRAIL_URL'),
                'port' => env('PAPERTRAIL_PORT'),
            ],
        ],

        'stderr' => [
            'driver' => 'monolog',
            'handler' => StreamHandler::class,
            'formatter' => env('LOG_STDERR_FORMATTER'),
            'with' => [
                'stream' => 'php://stderr',
            ],
        ],

        'syslog' => [
            'driver' => 'syslog',
            'level' => 'debug',
        ],

        'errorlog' => [
            'driver' => 'errorlog',
            'level' => 'debug',
        ],

        'bugsnag' => [
            'driver' => 'bugsnag',
        ],

        'merchant' => [
            'driver' => 'single',
            'path' => storage_path('logs/merchant.csv'),
            'tap' => [App\Logging\CsvFormatter::class],
        ],

        'debug' => [
            'driver' => 'single',
            'path' => storage_path('logs/debug.csv'),
            'tap' => [App\Logging\CsvFormatter::class],
        ],
        'csv' => [
            'driver' => 'single',
            'path' => storage_path('logs/csv.csv'),
            'tap' => [App\Logging\CsvFormatter::class],
        ],
        'validation' => [
            'driver' => 'daily',
            'path' => storage_path('logs/validation.log'),
            'days' => 14,
        ],
    ],

];
