<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Variation extends Model
{
    protected $guarded = [];

    protected $casts = [
        'filter' => 'boolean',
        'assistance' => 'boolean',
        'values' => 'array',
        'images' => 'array',
    ];

    public function model()
    {
        return $this->morphTo();
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('order', 'asc');
    }
}
