<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class FulfillmentSchematic extends Model
{
    // protected $fillable = ['shipping_option_id', 'cutoff', 'cutoff_is_hourly',
    //     'sunday_open', 'sunday_close',
    //     'monday_open', 'monday_close',
    //     'tuesday_open', 'tuesday_close',
    //     'wednesday_open', 'wednesday_close',
    //     'thursday_open', 'thursday_close',
    //     'friday_open', 'friday_close',
    //     'saturday_open', 'saturday_close'];
    protected $guarded = [];

    public function shippingOptions()
    {
        // return $this->belongsTo('App\ShippingOption');
        return $this->hasMany('App\ShippingOption', 'delivery', 'delivery');
    }
}
