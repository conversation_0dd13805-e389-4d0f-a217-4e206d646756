<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ClosedDay extends Model
{
    protected $guarded = [];

    protected $casts = [
        'date' => 'date',
    ];

    public function getOptionAttribute()
    {
        if ($this->delivery && $this->pickup) {
            return 'both';
        }
        if ($this->delivery) {
            return 'delivery';
        }
        if ($this->pickup) {
            return 'pickup';
        }
    }

    public function setOptionAttribute($value)
    {
        if ($value == 'both') {
            $this->attributes['delivery'] = true;
            $this->attributes['pickup'] = true;
        }
        if ($value == 'delivery') {
            $this->attributes['delivery'] = true;
            $this->attributes['pickup'] = false;
        }
        if ($value == 'pickup') {
            $this->attributes['delivery'] = false;
            $this->attributes['pickup'] = true;
        }
    }
}
