<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ShippingOptionShippingZone extends Model
{
    protected $table = 'shipping_option_shipping_zone';

    public $casts = [
        'key_values' => 'array'
    ];

    protected $guarded = [];
    // protected $fillable = ['shipping_option_id', 'shipping_zone_id', 'price_per_pound', 'base_rate', 'duration',
    //     'duration_is_hourly', 'hourly', 'free_shipping_above'];

    public function shippingOption()
    {
        return $this->belongsTo(ShippingOption::class);
    }

    public function shippingZone()
    {
        return $this->belongsTo(ShippingZone::class);
    }

    public function scopeActive($query)
    {
        return $query->whereActive(true);
    }

    public function priceByPound($pounds = 0)
    {
        $kvp = collect($this->key_values)->where('key', '!=', '')->sortByDesc('key')->values()->filter(
            function ($kv) use ($pounds) {
                return data_get($kv, 'key') <= $pounds;
            }
        )->first();

        $kvp = $kvp ?? collect($this->key_values)->firstWhere('key', '');

        $value = (float)data_get($kvp, 'value');

        return (($value * $pounds) > 0) ? ($value * $pounds) : 0;
    }
}
