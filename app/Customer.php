<?php

namespace App;

use App\Emails\AddSubscriber;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Traits\CustomerRecurringTrait;
use Illuminate\Notifications\Notifiable;
use App\Http\Controllers\TransactionController;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use App\Traits\CustomerSubscriptionTrait;

class Customer extends Authenticatable implements HasMedia
{
    use InteractsWithMedia, CustomerSubscriptionTrait;
    use Notifiable;
    use CustomerRecurringTrait;

    protected $guarded = [];

    protected $with = [
        // 'addresses',
        // 'payments',
        // 'orders',
        // 'returns',
    ];

    protected $casts = [
        'favorites' => 'array',
        'recent' => 'array',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $appends = [
        'front_end_following',
        'subscription_status'
    ];

    public function utm()
    {
        return $this->morphOne('App\Utm', 'model');
    }

    public function bag()
    {
        return $this->hasMany(Bag::class);
    }

    public function following()
    {
        return $this->hasMany(Follow::class);
    }

    public function addresses()
    {
        return $this->morphMany(Address::class, 'model');
    }

    public function getFirstNameAttribute()
    {
        return Str::before($this->name, ' ');
    }

    public function GetAddressAttribute()
    {
        return $this->addresses->firstWhere('default', true) ?? $this->addresses->first();
    }

    public function GetPaymentAttribute()
    {
        return $this->payments->firstWhere('default', true) ?? $this->payments->first();
    }

    public function payments()
    {
        return $this->morphMany(Payment::class, 'model');
    }

    public static function discounts()
    {
        $discounts = Discount::user()
            ->active()
            // ->limit()
            ->automated()
            ->get();
        return $discounts->filter(function ($discount) {
            return $discount->checkCustomerLimit();
        });
    }

    public function membership()
    {
        return $this->hasOne('App\Membership');
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class)
            ->where(function ($query) {
                $query->where('subscriptions.status', '!=', 'canceled')
                    ->orWhere('subscriptions.status', null);
            });
    }

    public static function getCustomer($customer_array)
    {
        if (auth()->check()) {
            return auth()->user();
        }

        $user = self::firstOrCreate(
            ['email' => $customer_array['email']],
            [
                'name' => $customer_array['name'],
                'phone' => data_get($customer_array, 'shippingInfo.phone'),
            ]
        );

        if (isset($customer_array['loginInfo']) && !$user->password) {
            $password = decode(data_get($customer_array, 'loginInfo.password'));
            $user->update(['password' => Hash::make($password)]);

            auth()->login($user);

            try {
                $shipping = Arr::only(
                    $customer_array['shippingInfo'],
                    DB::getSchemaBuilder()->getColumnListing('addresses')
                );
                $shipping['default'] = true;
                $user->addresses()->create($shipping);
            } catch (Exception $e) {
            }

            try {
                $payment = Arr::only(
                    $customer_array['creditInfo'],
                    DB::getSchemaBuilder()->getColumnListing('payments')
                );
                $payment['default'] = true;
                $user->payments()->create($payment);
            } catch (Exception $e) {
            }
        }

        return $user->load(['payments', 'orders', 'addresses']);
    }

    public function nextSubscriptions()
    {
        return $this->subscriptions->map->getFristProducts();
    }

    public function group()
    {
        return $this->belongsTo('App\Group');
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function returns()
    {
        return $this->hasManyThrough(Returns::class, Order::class);
    }

    public function getOwnDiscounts($min)
    {
        return $this->eligible()->active()->automated()->above($min)->get();
    }

    public function getFrontEndFollowingAttribute()
    {
        return $this->following->map(function ($follow) {
            $class = explode('\\', $follow->model_type);
            return [
                'id' => $follow->model_id,
                'type' => Str::plural(strtolower($class[count($class) - 1])),
            ];
        })
            ->groupBy('type')
            ->map(function ($item) {
                return $item->map(function ($i) {
                    return $i['id'];
                });
            });
    }

    public function frontEndBag($later = false)
    {
        return makeForFronEnd($this->bag->where('later', $later));
    }

    public function abandonedBag()
    {
        return makeForFronEnd($this->bag->filter(function ($bag) {
            return !$bag->later && data_get($bag, 'meta.reminded') != true;
        }));
    }

    public function abandonedBagIds()
    {
        return $this->bag->filter(function ($bag) {
            return !$bag->later && data_get($bag, 'meta.reminded') != true;
        })->values()->pluck('id');
    }

    public function abandoned()
    {
        return $this->bag->sortBy('updated_at')->last()->updated_at->diffInHours() >= settings()->getValue(
                'hours_to_send_abandoned_cart_email'
            );
    }

    public function setBagsAsReminded()
    {
        $this->bag->each(function ($b) {
            $b->update(['meta->reminded' => true]);
        });
    }

    public function getBagTotal()
    {
        $total = 0;
        foreach ($this->bag as $item) {
            $total += $item->getPrice();
        }
        return $total;
    }
    // returns the total price of only applicable products.
    // public function getProductTotal($discount){
    //     $total=0;
    //     $ids=[];
    //     $productClass='App\Product';
    //     foreach($this->bag as $item){
    //         //checks if bag item is a product or a varient and gets the product.
    //         $product=($item->model instanceof $productClass) ? $item->model : $item->model->product;
    //         if ($product->applyDiscount($discount)){
    //             $total+=$item->getPrice();
    //             array_push($ids,$product->id);
    //         }
    //     }
    //     return [$total,$ids];
    // }
    public function getApplicableDiscounts()
    {
        $min = $this->getBagTotal();
        $groupDiscounts = $this->group->getDiscounts($min);
        $ownDiscounts = $this->getOwnDiscounts($min);
        if ($groupDiscounts && $ownDiscounts) {
            return $groupDiscounts->merge($ownDiscounts);
        } else {
            return $groupDiscounts ? $groupDiscounts : $ownDiscounts;
        }
    }
    // public function getSavings($discount){
    //     if(!$discount->automated){
    //         $discount->isActive();
    //     }
    //     $type = $discount->type;
    //     if($type == 'Free Shipping'){
    //         return $type;
    //     }
    //     $totalObject = $this->getProductTotal($discount);
    //     $total=$totalObject[0];
    //     $ids=$totalObject[1];
    //     if($type=='Fixed'){
    //         $savings= ($total >= $discount->amount) ? $discount->amount : $total;
    //         return ['savings'=>$savings,'products'=>$ids];
    //     }elseif ($type=='Percentage') {
    //         $percentage = $discount->amount/100;
    //         $savings= ($total <= $discount->max || !$discount->max ) ? number_format($total * $percentage ,2) : number_format($discount->max * $percentage ,2);
    //         return ['savings'=>$savings,'products'=>$ids];
    //     }else{
    //         return $type;
    //     }

    // }

    public function digitals()
    {
        return $this->hasMany(Digital::class);
    }

    public function createPayment($data)
    {
        //todo
        //parse date ex: 12/10
        $payment = $this->payments()->create($data);
        // return '//todo';
        return $payment;
    }

    public function calculatePoints()
    {
        $start = $this->membership->getStart();
        return [
            'start' => $start,
            'points' => (int)$this->orders->where('created_at', '>', $start)->where('status', '!=', 'canceled')
                ->map(function ($purchase) {
                    return $purchase['grand_total'] - $purchase['tax_amount'] - $purchase['shipping_amount'];
                })->sum()
        ];
    }

    public function calculateStatus()
    { //this function will is not to be called when the points actually change.
        $pointsInfo = $this->calculatePoints();
        $status = RewardGroup::where('amount', '<', $pointsInfo['points'])->orderBy('amount', 'desc')->first(
        )['name'];
        return array_merge($pointsInfo, ['status' => $status]);
    }

    public function getNewStatus()
    {
    }

    public function getPhoneNumberAttribute()
    {
        return optional($this->addresses->first())->phone;
    }

    public function hasActiveMembership()
    {
        return $this->membership && $this->membership->active;
    }


    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('file_upload')
            ->singleFile();
    }

    public $tax_exempt_options = [
        'government' => 'Government',
        'other' => 'Other',
        'wholesale' => 'Wholesale'
    ];

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($customer) {
            if (!$customer->getOriginal('tax_exempt') && $customer->tax_exempt) {
                TransactionController::AddCustomer($customer);
            }
            if ($customer->getOriginal('tax_exempt') && $customer->tax_exempt) {
                TransactionController::UpdateCustomer($customer);
            }
            if ($customer->getOriginal('tax_exempt') && !$customer->tax_exempt) {
                TransactionController::DeleteCustomer($customer);
            }
        });
    }


    public function getIsSubscribedAttribute($email)
    {
        return json_encode(
            data_get(
                forceArray(json_encode((new AddSubscriber)->get($email))),
                'response.State'
            ) == 'Active'
        );
    }
}
