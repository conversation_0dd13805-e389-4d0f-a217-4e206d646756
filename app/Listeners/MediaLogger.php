<?php

namespace App\Listeners;

use Illuminate\Support\Str;
use Spatie\MediaLibrary\Events\MediaHasBeenAdded;
use ZipArchive;

class MediaLogger
{
    public function handle(MediaHasBeenAdded $event)
    {
        $media = $event->media;
        if ($media->mime_type == 'application/zip') {
            $zip = new ZipArchive;

            $url = $media->getTemporaryUrl(now()->addMinutes(5));

            $str = (string)Str::uuid();

            file_put_contents(public_path("{$str}.zip"), fopen($url, 'r'));

            $zip->open(public_path("{$str}.zip"));

            $array = [];
            for ($i = 0; $i < $zip->numFiles; $i++) {
                $filename = $zip->getNameIndex($i);

                $ext = strtoupper((pathinfo($filename, PATHINFO_EXTENSION)));
                if ($ext && !collect($array)->contains($ext)) {
                    $array = array_merge($array, [$ext]);
                }
            }
            $zip->close();

            unlink(public_path("{$str}.zip"));

            $media->update(['custom_properties->extensions' => $array]);
        } else {
            $media->update(
                ['custom_properties->extensions' => [strtoupper((pathinfo($media->file_name, PATHINFO_EXTENSION)))]]
            );
        }
    }
}
