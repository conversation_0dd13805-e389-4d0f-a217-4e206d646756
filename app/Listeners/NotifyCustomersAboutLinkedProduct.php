<?php

namespace App\Listeners;

use App\Customer;
use App\Events\LinkedProductAdded;
use App\Jobs\SendEmail;
use App\Order;
use App\Product;

class NotifyCustomersAboutLinkedProduct
{
    public function handle(LinkedProductAdded $event): void
    {
        $linkItem = $event->linkItem;

        $product = $linkItem->model()->first();

        $link = $linkItem->link;

        $linkedProductIds = $link->linkItems()->where('model_type', Product::class)->pluck('model_id');

        $customerIds = Order::where(function ($query) use ($linkedProductIds) {
            foreach ($linkedProductIds as $productId) {
                $query->orWhereJsonContains('products_ids', $productId);
            }
        })->distinct()->pluck('customer_id');

        $customers = Customer::whereIn('id', $customerIds)->get();

        foreach ($customers as $customer) {
            $data = [
                'product' => $product,
                'customer' => $customer,
            ];
            SendEmail::dispatch(\App\Emails\LinkedProductNotification::class, $data, [
                'email' => data_get($customer, 'email'),
            ]);
        }

    }
}
