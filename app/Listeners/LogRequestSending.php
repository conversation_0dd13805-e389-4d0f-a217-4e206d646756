<?php

namespace App\Listeners;

use App\Services\ApiLogsService;
use Illuminate\Http\Client\Events\RequestSending;

class LogRequestSending
{
    public function handle(RequestSending $event): void
    {
        $uniqueId = $event->request->header('X-Request-ID');
        $tag = $event->request->header('X-API-TAG');
        $provider = $event->request->header('X-API-Provider');
        If(!empty($uniqueId) && !empty($tag) && !empty($provider)){
            $apiLogsService = new ApiLogsService();
            $apiLogsData = [
                'url' => $event->request->url(),
                'method' => $event->request->method(),
                'tag' => $tag[0],
                'request' => json_encode($event->request->data()),
                'unique_identifier' => $uniqueId[0],
                'provider' => $provider[0]
            ];
            $apiLogsService->add($apiLogsData);
        }
    }
}
