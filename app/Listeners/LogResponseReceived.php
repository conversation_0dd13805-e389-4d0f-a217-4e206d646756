<?php

namespace App\Listeners;

use App\Services\ApiLogsService;
use Illuminate\Http\Client\Events\ResponseReceived;
use Illuminate\Support\Facades\Log;

class LogResponseReceived
{
    public function handle(ResponseReceived $event): void
    {
        try {
            $uniqueId = $event->request->header('X-Request-ID');

            if (!empty($uniqueId)) {
                $responseData = $event->response->json();
                $apiLogsService = new ApiLogsService();
                $apiLogsData = [
                    'response' => json_encode($responseData),
                    'status_code' => $event->response->status(),
                    'updated_at' => now(),
                ];
                $apiLogsService->updateByUniqueIdentifier($uniqueId[0], $apiLogsData);
            }

        } catch (\Exception $e) {
            Log::error('LogResponseReceived Error processing response:', [
                'event' => [$event],
                'exception' => $e->getMessage()
            ]);
        }
    }
}
