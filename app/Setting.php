<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    public $guarded = [];

    public function getValue($key)
    {
        return optional($this->where('key', $key)->first())->value;
    }

    public function setValue($key, $value)
    {
        if ($item = $this->where('key', $key)->first()) {
            $item->update([
                'value' => $value
            ]);
        }
    }
}
