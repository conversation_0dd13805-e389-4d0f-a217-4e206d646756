<?php

namespace App;

use App\Jobs\Miscellaneous;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Creator extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $guarded = [];

    protected $fillable = [
        'name',
        'heb_name',
        'type',
        'description',
        'redirect',
        'button_link',
        'button_text',
    ];

    protected $with = [
        // 'media',
    ];

    protected $appends = [
        'path',
        'media_urls',
    ];

    public function followers()
    {
        return $this->morphMany(Follow::class, 'model');
    }

    public function customers()
    {
        return $this->morphToMany(Customer::class, 'model', 'follows');
    }

    public static $options = [
        'author' => 'Author',
        'singer' => 'Singer',
        'performer' => 'Performer',
        'artist' => 'Artist',
        'illustrator' => 'Illustrator',
    ];

    public function getOptionsAttribute()
    {
        return static::$options;
    }

    public function products()
    {
        return $this->belongsToMany(Product::class)
            ->withTimestamps();
    }

    public function apply()
    {
        return $this->morphMany(CodeApply::class, 'applicability');
    }

    public function getPathAttribute()
    {
        return "/creator/" . str_slug($this->name) . "/{$this->id}";
    }

    public function getMediaUrlsAttribute()
    {
        return $this->media->map(function ($item) {
            return $item->getUrl();
        });
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->background('#ffffff')
            ->width(130)
            ->height(130);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('media')
            ->singleFile();
    }

    public function refreshCache($creator)
    {
        $creator->products->each->refreshCache();
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($creator) {
            if ($creator->getOriginal('name') != $creator->name) {
                $creator->products->map->updateSearchFeild();
                Miscellaneous::dispatch(Creator::class, 'refreshCache', $creator);
            }
            refreshCache();
        });

        static::deleted(function ($creator) {
            refreshCache();
        });
    }
}
