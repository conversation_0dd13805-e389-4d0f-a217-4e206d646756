<?php

namespace App;

use Illuminate\Support\Str;

use Illuminate\Database\Eloquent\Model;

class Country extends Model
{
    protected $guarded = [];

    public function Active()
    {
        $zones = InternationalShippingZone::get();

        return $zones->map(function ($zone) {
            return Str::contains($zone->countries_text, $this->code) && $zone->active();
        })->contains(true);
    }
}
