<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class GiftOption extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $appends = ['path'];


    protected $guarded = [];

    public function getPathAttribute()
    {
        return "/gift_options/" . str_slug($this->name);
    }

    public function getPriceAttribute()
    {
        return $this->attributes['price'] ?? 0;
    }

    public function getMediaUrlsAttribute()
    {
        return $this->media->sortBy('order_column')->map(function ($item) {
            return $item->getUrl();
        });
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->background('#ffffff')
            ->width(130)
            ->height(130);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('media')
            ->singleFile();
    }
}
