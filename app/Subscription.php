<?php

namespace App;

use App\Http\Controllers\Api\BagController;
use App\Http\Controllers\Api\EmailsController;
use App\Jobs\CreateSubscriptionOrder;
use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    public $guarded = [];

    public $appends = [
        'to',
        'from',
        'string',
        'first_products',
        'combination'
    ];

    public $casts = [
        'shipping' => 'array',
        'payments' => 'array',
    ];

    public function getCombinationAttribute()
    {
        return Combination::where('group_id', $this->subscription_group_id)
            ->where('type_id', $this->subscription_type_id)
            ->first();
    }

    public function orders()
    {
        return $this->morphMany(Order::class, 'recurring');
    }

    public function setShippingAttribute($value)
    {
        if (!request()->is('nova-api/*')) {
            $this->attributes['shipping'] = json_encode($value);
        }
    }

    public function setPaymentsAttribute($value)
    {
        if (!request()->is('nova-api/*')) {
            $this->attributes['payments'] = json_encode($value);
        }
    }

    public function getToAttribute()
    {
        return $this->combination ? $this->combination->to_price : 0;
    }

    public function getFromAttribute()
    {
        return $this->combination ? $this->combination->from_price : 0;
    }

    public function getStringAttribute()
    {
        return $this->combination ? $this->combination->string : '';
    }

    public function getActiveAttribute()
    {
        return $this->status == 'active';
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function payment()
    {
        return $this->belongsTo(Payment::class);
    }

    public function address()
    {
        return $this->belongsTo(Address::class);
    }

    public function subscriptiontype()
    {
        return $this->belongsTo(SubscriptionType::class, 'subscription_type_id');
    }

    public function subscriptionGroup()
    {
        return $this->belongsTo(SubscriptionGroup::class);
    }

    public function productsForToday()
    {
        return data_get(
            $this->nextProducts()['next_products'],
            today()->toDateString()
        );
    }

    public function dateProducts($date)
    {
        return data_get(
            $this->nextProducts()['next_products'],
            $date->toDateString()
        );
    }

    public function nextProducts()
    {
        $combination = $this->combination;

        $type = $this->subscriptiontype;
        $group = $this->subscriptionGroup;

        return [
            'name' => $type ? $type->name : null,
            'subscription_group_id' => $group ? $group->id : null,
            'subscription_group_name' => $group ? $group->name : null,
            'subscription_id' => $combination ? $combination->type_id : null,
            'filter_name' => $combination ? $combination->filter_name : null,
            'next_products' => $combination ? $combination->upcoming_products : [],
            'product' => collect($combination ? $combination->upcoming_products : [])->first(),
        ];
    }

    public function getFirstProductsAttribute()
    {
        return [
                'date' => $this->combination ? $this->combination->upcoming_date : null,
                'filter_name' => $this->combination ? $this->combination->filter_name : null,
            ] + $this->nextProducts();

        return $this->subscriptiontype->getFirstProducts($this->subscriptionGroup);
    }

    public function getAnyProductAttribute()
    {
        return $this->subscriptiontype->getAnyProduct($this->subscriptionGroup);
    }


    public function sendEmail($date = null)
    {
        if ($this->status == 'canceled') {
            return;
        }
        if ($this->combination && !$products = $this->combination->upcoming_products) {
            return;
        }

        if (!!collect($this->orders->where('status', '!=', 'cancelled')->map->product_ids)->flatten(1)->values(
        )->intersect(collect($products)->pluck('id'))->count()) {
            return;
        }

        $products = collect($products)
            ->map(function ($product) {
                return ['quantity' => $this->quantity] + $product;
            });

        return EmailsController::SendSubscriptionReview($this, $products);
    }

    public function createOrder($date)
    {
        if (!$this->active || $this->orderWasCreated($date)) {
            return;
        }
        if ($this->combination && !$products = $this->combination->upcoming_products) {
            return;
        }
        $products = collect($products)->map(function ($product) {
            return ['quantity' => $this->quantity] + $product;
        });

        if (!!collect($this->orders->where('status', '!=', 'cancelled')->map->product_ids)->flatten(1)->values(
        )->intersect(collect($products)->pluck('id'))->count()) {
            return;
        }

        $address = $this->address ?? $this->customer->address;
        $payment = $this->payment ?? $this->customer->payment;

        $totals = (new BagController)->GetBreakDown(request()->merge([
            'products' => $products,
            'address' => $address,
        ]));

        $bag = collect($totals['bag'])->map(function ($item) {
            if (collect(explode(',', settings()->getValue('skus_that_are_free_in_subscriptions')))->contains(
                data_get($item, 'sku')
            )) {
                $item['price'] = 0;
                $item['total'] = 0;
                $item['total_amount'] = 0;
                $item['grand_total'] = 0;
                $item['tax_amount'] = 0;
            }
            return $item;
        });
        $totals = [
            'sub_total' => $bag->sum('total'),
            'tax' => $bag->sum('tax_amount'),
            'shipping' => $totals['shipping'],
            'total' => $bag->sum('total') + $bag->sum('tax_amount'),
            'bag' => $bag
        ];

        $local = optional(
            optional(
                optional(
                    optional(
                        optional(
                            optional(
                                optional(
                                    optional(ZipCode::getCode(data_get($address, 'postal_code')))->shippingZone
                                )->options
                            )->where('active', true)
                        )->map
                    )->shippingOption
                )->values()
            )->pluck('visible_name')
        )->contains('Local Delivery');

        return $this->customer->orders()->create([
            'sub_total' => $totals['sub_total'],
            'tax_amount' => $totals['tax'],
            'shipping_amount' => $totals['shipping'],
            'meta->subscription' => [
                'id' => $this->id,
                'type_name' => $this->subscriptiontype->name,
                'name' => $this->subscriptiontype->name,
                'type_id' => $this->subscriptiontype->id,
                'group' => $this->subscriptionGroup->name,
                'group_id' => $this->subscriptionGroup->id,
                'date' => $date->toDateString(),
            ],
            'subscription' => true,
            'grand_total' => $totals['total'],
            'products' => $totals['bag'],
            'shipping' => [
                'shippingInfo' => $this->shipping, //$address,
                'shippingType' => $local ? [
                    'name' => 'Local Delivery',
                    'estimated_arrival' => now()->parse($date)->copy()->subWeek()->toDateTimeString()
                ] :
                    ['estimated_arrival' => now()->parse($date)->copy()->subWeek()->toDateTimeString()],
                'tracking' => null
            ],
            'payments' => [
                'giftCard' => [],
                'creditInfo' => $this->payments, //$payment,
            ],
            'payments->creditInfo->payment_type' => 'creditCard',
            'payment_status' => 'unpaid',
            'status' => 'unpaid',
            'product_ids' => $totals['bag']->pluck('product_id')->filter(),
            'recurring_type' => 'App\Subscription',
            'recurring_id' => $this->id,
        ]);
    }

    public function orderWasCreated($next_date)
    {
        return !!$this->customer->orders
            ->where('meta.subscription.id', $this->id)
            ->where('meta.subscription.date', $next_date->toDateString())
            ->where('payment_status', '!=', 'declined')
            ->first();
    }

    public function isAfterCutOff()
    {
        $next_date = $this->combination ? $this->combination->upcoming_date : null;

        return !!$next_date->lte(today()->addWeek(2)) && today()->lte($next_date);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($subscription) {
            $subscription->status = 'active';
        });

        static::created(function ($subscription) {
            EmailsController::SendSubscriptionConfirmation($subscription);

            if ($subscription->isAfterCutOff()) {
                $next_date = $subscription->combination ? $subscription->combination->upcoming_date : null;
                CreateSubscriptionOrder::dispatch($subscription->id, $next_date);
            }
        });

        static::saving(function ($subscription) {
            if (request()->is('nova-api/*')) {
                $array = [];
                if (request()->shipping) {
                    collect(json_decode(request()->shipping))->map(function ($value, $key) use (&$array) {
                        $array = array_merge(["shipping->$key" => $value], $array);
                    });
                }
                $payment = Payment::find(request()->payment_id);
                $payments = $payment ? collect($payment->toArray())->only(
                    'id',
                    'address_line_1',
                    'address_line_2',
                    'city',
                    'country',
                    'created_at',
                    'expDate',
                    'last_four',
                    'name',
                    'postal_code',
                    'securityCode',
                    'state',
                    'type'
                )->toArray() : null;
                if ($payments) {
                    collect($payments)->map(function ($value, $key) use (&$array) {
                        $array = array_merge(["payments->$key" => $value], $array);
                    });
                }
                $subscription->withoutEvents(function () use ($array, $subscription) {
                    $subscription->update($array);
                });
            }
        });

        static::updated(function ($subscription) {
            if ($subscription->active && $subscription->isAfterCutOff()) {
                $next_date = $subscription->combination ? $subscription->combination->upcoming_date : null;

                if (!$subscription->orderWasCreated($next_date)) {
                    CreateSubscriptionOrder::dispatch($subscription->id, $next_date);
                }
            }
        });
    }

    public function getLastPaymentAttribute()
    {
        return optional($this->orders->sortByDesc('created_at')->first())->payment_status;
    }

    public function setLastPaymentAttribute()
    {
    }
}
