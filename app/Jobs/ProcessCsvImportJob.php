<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Capitalc\CustomCsvImport\CustomImporter;
use Laravel\Nova\Resource;
use Maatwebsite\Excel\Facades\Excel;
use Exception;

class ProcessCsvImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 3600; // 1 hour timeout for large imports

    protected $filePath;
    protected $resourceClass;
    protected $modelClass;
    protected $attributeMap;
    protected $rules;
    protected $meta;
    protected $randomStringSettings;
    protected $customValues;
    protected $combinedValues;
    protected $resultsPath;

    /**
     * Create a new job instance.
     */
    public function __construct(
        string $filePath,
        string $resourceClass,
        string $modelClass,
        array $attributeMap = [],
        array $rules = [],
        array $meta = [],
        array $randomStringSettings = [],
        array $customValues = [],
        array $combinedValues = []
    ) {
        $this->filePath = $filePath;
        $this->resourceClass = $resourceClass;
        $this->modelClass = $modelClass;
        $this->attributeMap = $attributeMap;
        $this->rules = $rules;
        $this->meta = $meta;
        $this->randomStringSettings = $randomStringSettings;
        $this->customValues = $customValues;
        $this->combinedValues = $combinedValues;
        
        // Generate results file path
        // Extract hash from the file path (remove directory and extension)
        $filename = basename($filePath);
        $hash = pathinfo($filename, PATHINFO_FILENAME);
        $this->resultsPath = "csv-import/{$hash}.csv.results.json";

        // Use default queue for background processing
        $this->onQueue('default');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Starting CSV import job', [
                'file' => $this->filePath,
                'resource' => $this->resourceClass,
                'model' => $this->modelClass
            ]);

            // Initialize results structure
            $results = [
                'status' => 'processing',
                'started_at' => now()->toISOString(),
                'completed_at' => null,
                'total_rows' => 0,
                'processed_rows' => 0,
                'successful_rows' => 0,
                'failed_rows' => 0,
                'failures' => [],
                'errors' => []
            ];

            // Save initial status
            $this->saveResults($results);

            // Set up the CustomImporter with all configuration
            $importer = new CustomImporter();
            $resource = new $this->resourceClass(new $this->modelClass());
            
            $importer->setResource($resource);
            $importer->setModelClass($this->modelClass);
            $importer->setAttributeMap($this->attributeMap);
            $importer->setRules($this->rules);
            $importer->setMeta($this->meta);
            $importer->setRandomStringSettings($this->randomStringSettings);
            $importer->setCustomValues($this->customValues);
            $importer->setCombinedValues($this->combinedValues);

            // Get full file path
            $fullFilePath = Storage::path($this->filePath);
            
            if (!file_exists($fullFilePath)) {
                throw new Exception("CSV file not found: {$fullFilePath}");
            }

            // Count total rows for progress tracking
            $totalRows = $this->countCsvRows($fullFilePath);
            $results['total_rows'] = $totalRows;
            $this->saveResults($results);

            Log::info('CSV import processing started', [
                'total_rows' => $totalRows,
                'file_path' => $fullFilePath
            ]);

            // Set up progress tracking callback for chunked processing
            $importer->setProgressCallback(function($stats) use ($totalRows) {
                $this->updateProgressFromStats($totalRows, $stats);
            });

            // Process the import using Laravel Excel's chunking mechanism
            Excel::import($importer, $fullFilePath);

            // Get failures from the importer
            $failures = $importer->failures();
            $failureData = [];

            foreach ($failures as $failure) {
                $failureData[] = [
                    'row' => $failure->row(),
                    'attribute' => $failure->attribute(),
                    'errors' => $failure->errors(),
                    'values' => $failure->values()
                ];
            }

            // Get final processing stats from the importer
            $finalStats = $importer->getProcessingStats();

            // Update final results (include 'imported' key for base controller compatibility)
            $results = [
                'status' => 'completed',
                'started_at' => $this->getStartTime(),
                'completed_at' => now()->toISOString(),
                'total_rows' => $totalRows,
                'processed_rows' => $finalStats['processed_rows'],
                'successful_rows' => $finalStats['successful_rows'],
                'failed_rows' => $finalStats['failed_rows'],
                'imported' => $finalStats['successful_rows'], // ✅ Add imported key for base controller
                'failures' => $failureData,
                'errors' => []
            ];

            $this->saveResults($results);

            Log::info('CSV import job completed successfully', [
                'total_rows' => $totalRows,
                'successful_rows' => $results['successful_rows'],
                'failed_rows' => $results['failed_rows']
            ]);

        } catch (Exception $e) {
            Log::error('CSV import job failed', [
                'error' => $e->getMessage(),
                'file' => $this->filePath,
                'trace' => $e->getTraceAsString()
            ]);

            // Save error results (include 'imported' key for base controller compatibility)
            $errorResults = [
                'status' => 'failed',
                'started_at' => $results['started_at'] ?? now()->toISOString(),
                'completed_at' => now()->toISOString(),
                'total_rows' => $results['total_rows'] ?? 0,
                'processed_rows' => $results['processed_rows'] ?? 0,
                'successful_rows' => 0,
                'failed_rows' => $results['total_rows'] ?? 0,
                'imported' => 0, // ✅ Add imported key for base controller (0 for failed jobs)
                'failures' => [],
                'errors' => [
                    [
                        'message' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine()
                    ]
                ]
            ];

            $this->saveResults($errorResults);
            
            // Re-throw to mark job as failed
            throw $e;
        }
    }

    /**
     * Count the number of rows in the CSV file (excluding header)
     */
    private function countCsvRows(string $filePath): int
    {
        $count = 0;
        $handle = fopen($filePath, 'r');
        
        if ($handle) {
            // Skip header row
            fgetcsv($handle);
            
            while (fgetcsv($handle) !== false) {
                $count++;
            }
            
            fclose($handle);
        }
        
        return $count;
    }

    /**
     * Update progress from importer stats (used with chunked processing)
     */
    private function updateProgressFromStats(int $totalRows, array $stats): void
    {
        $results = [
            'status' => 'processing',
            'started_at' => $this->getStartTime(),
            'completed_at' => null,
            'total_rows' => $totalRows,
            'processed_rows' => $stats['processed_rows'],
            'successful_rows' => $stats['successful_rows'],
            'failed_rows' => $stats['failed_rows'],
            'imported' => $stats['successful_rows'],
            'failures' => [], // Will be populated at the end
            'errors' => []
        ];

        $this->saveResults($results);

        // Log progress every 50 rows (chunk size) for monitoring
        if ($stats['processed_rows'] % 50 === 0) {
            Log::info('CSV import progress update', [
                'processed_rows' => $stats['processed_rows'],
                'successful_rows' => $stats['successful_rows'],
                'failed_rows' => $stats['failed_rows'],
                'total_rows' => $totalRows,
                'progress_percentage' => $totalRows > 0 ? round(($stats['processed_rows'] / $totalRows) * 100, 2) : 0
            ]);
        }
    }



    /**
     * Get the start time from existing results or current time
     */
    private function getStartTime(): string
    {
        if (Storage::exists($this->resultsPath)) {
            $existingResults = json_decode(Storage::get($this->resultsPath), true);
            return $existingResults['started_at'] ?? now()->toISOString();
        }

        return now()->toISOString();
    }

    /**
     * Save results to the JSON file
     */
    private function saveResults(array $results): void
    {
        Storage::put($this->resultsPath, json_encode($results, JSON_PRETTY_PRINT));
    }

    /**
     * Handle job failure
     */
    public function failed(Exception $exception): void
    {
        Log::error('CSV import job failed permanently', [
            'error' => $exception->getMessage(),
            'file' => $this->filePath,
            'trace' => $exception->getTraceAsString()
        ]);

        // Ensure error results are saved (include 'imported' key for base controller compatibility)
        $errorResults = [
            'status' => 'failed',
            'started_at' => now()->toISOString(),
            'completed_at' => now()->toISOString(),
            'total_rows' => 0,
            'processed_rows' => 0,
            'successful_rows' => 0,
            'failed_rows' => 0,
            'imported' => 0, // ✅ Add imported key for base controller (0 for failed jobs)
            'failures' => [],
            'errors' => [
                [
                    'message' => $exception->getMessage(),
                    'file' => $exception->getFile(),
                    'line' => $exception->getLine()
                ]
            ]
        ];

        $this->saveResults($errorResults);
    }
}
