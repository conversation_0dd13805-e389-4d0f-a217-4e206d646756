<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $class;

    protected $data;

    protected $customer;

    // protected $queue = 'high';

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($class, $data, $customer)
    {
        $this->class = $class;
        $this->data = $data;
        $this->customer = $customer;
        $this->queue = 'mail';
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        (new $this->class)->withData(['data' => $this->data])->sendTo($this->customer);
    }
}
