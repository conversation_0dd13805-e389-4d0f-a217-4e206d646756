<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class OrderCreate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $class;

    protected $method;

    protected $object;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($class, $method, $object)
    {
        $this->class = $class;
        $this->method = $method;
        $this->object = $object;
        $this->queue = 'order';
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        (new $this->class)->{$this->method}($this->object);
    }
}
