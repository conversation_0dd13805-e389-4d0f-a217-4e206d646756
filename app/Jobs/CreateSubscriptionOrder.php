<?php

namespace App\Jobs;

use App\Subscription;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CreateSubscriptionOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $date;
    public $subscription;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($subscription, $date)
    {
        $this->subscription = $subscription;
        $this->date = $date;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        return Subscription::find($this->subscription)->createOrder($this->date);
    }
}
