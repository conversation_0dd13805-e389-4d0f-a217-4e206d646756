<?php

namespace App\Jobs;

use App\Product;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ProcessAddSale implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 300;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $backoff = 60;

    /**
     * The sale data and product IDs to process
     */
    protected $saleData;
    protected $productIds;

    /**
     * Create a new job instance.
     */
    public function __construct(array $saleData, array $productIds)
    {
        $this->saleData = $saleData;
        $this->productIds = $productIds;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('ProcessAddSale job started', [
            'product_count' => count($this->productIds),
            'add_sale' => $this->saleData['add_sale']
        ]);

        try {
            // Get the products to process
            $products = Product::with('variationInfos')->whereIn('id', $this->productIds)->get();

            $processedCount = 0;
            foreach ($products as $model) {
                $this->processSaleForProduct($model);
                $processedCount++;

                // Log progress every 10 products
                if ($processedCount % 10 === 0) {
                    Log::info('ProcessAddSale progress', [
                        'processed' => $processedCount,
                        'total' => count($this->productIds)
                    ]);
                }
            }

            // Update search fields for searchable products
            $this->updateSearchFields($products);

            Log::info('ProcessAddSale job completed successfully', [
                'product_count' => count($this->productIds),
                'processed_count' => $processedCount
            ]);

        } catch (\Exception $e) {
            Log::error('ProcessAddSale job failed', [
                'error' => $e->getMessage(),
                'product_ids' => $this->productIds
            ]);
            throw $e;
        }
    }

    /**
     * Process sale for a single product and its variations
     */
    private function processSaleForProduct(Product $model): void
    {
        if (!$this->saleData['add_sale']) {
            // Remove sale
            $model->sale()->delete();
        } else {
            // Add/update sale
            $model->sale()->updateOrCreate(
                ['model_type' => 'App\Product', 'model_id' => $model->id],
                [
                    'type' => $this->saleData['sale_type'],
                    'amount' => $this->saleData['sale_amount'],
                    'from' => $this->saleData['sale_from'],
                    'start' => $this->saleData['start_sale'],
                    'end' => $this->saleData['end_sale'],
                ]
            );
        }

        // Process variations
        foreach ($model->variationInfos as $variationInfo) {
            if (!$this->saleData['add_sale']) {
                $variationInfo->sale()->delete();
            } else {
                $variationInfo->sale()->updateOrCreate(
                    ['model_type' => 'App\VariationInfo', 'model_id' => $variationInfo->id],
                    [
                        'type' => $this->saleData['sale_type'],
                        'amount' => $this->saleData['sale_amount'],
                        'from' => $this->saleData['sale_from'],
                        'start' => $this->saleData['start_sale'],
                        'end' => $this->saleData['end_sale'],
                    ]
                );
            }
        }

        // Clear product cache
        Cache::forget("product_{$model->id}");
    }

    /**
     * Update search fields for searchable products
     */
    private function updateSearchFields(Collection $products): void
    {
        $updateIds = Product::setEagerLoads([])
            ->isSearchable()
            ->whereIn('id', $products->pluck('id'))
            ->get(['id'])
            ->map->id;

        if ($updateIds->isNotEmpty()) {
            Product::find($updateIds)->each->updateSearchFeild();
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('ProcessAddSale job failed permanently', [
            'error' => $exception->getMessage(),
            'product_ids' => $this->productIds,
            'sale_data' => $this->saleData
        ]);
    }
}
