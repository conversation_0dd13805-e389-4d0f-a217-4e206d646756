<?php

namespace App\Jobs;

use App\Product;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class UpdateProductSearch implements ShouldQueue
{
    use Queueable;

    public int $timeout = 300;

    protected array $productIds;

    public function __construct(array $productIds)
    {
        $this->productIds = $productIds;
    }

    public function handle(): void
    {
        $products = Product::whereIn('id', $this->productIds)->get();
        foreach ($products as $product) {
            $product->updateSearchFeild();
            cache()->forget("product_{$product->id}");
        }
        $products->searchable();
    }
}
