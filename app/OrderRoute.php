<?php

namespace App;

use App\Http\Controllers\Api\EmailsController;
use Illuminate\Database\Eloquent\Model;
use Route4Me\Route;
use Route4Me\Route4Me;

class OrderRoute extends Model
{
    public $guarded = [];

    public $casts = [
        'meta' => 'array',
        'web_hooks' => 'array',
    ];

    public $table = 'order_route';

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function route()
    {
        return $this->belongsTo(\App\Route::class);
    }

    public function sendShippedEmail()
    {
        EmailsController::SendOrderShipped($this->order);
    }


    public function insertWebhook($data = [])
    {
        $web_hooks = $this->web_hooks ?? [];

        array_push(
            $web_hooks,
            $data
        );

        $this->update([
            'web_hooks' => $web_hooks,
            'visited' => (!$this->visited && data_get(
                        $data,
                        'activity_type'
                    ) == 'mark-destination-visited') || $this->visited
        ]);

        if (data_get($data, 'activity_type') == 'mark-destination-departed') {
            $this->order->markAsDelivered();
        }
    }
}
