<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class FilterItem extends Model implements HasMedia
{
    use InteractsWithMedia;

    public $guarded = [];

    public function toArray()
    {
        return array_merge(
            $this->attributes,
            ['parent' => optional($this->filter)->name]
        );
    }

    public function products()
    {
        return $this->belongsToMany(Product::class);
    }

    public function active_products()
    {
        return $this->belongsToMany(Product::class)
            ->active();
    }

    public function filter()
    {
        return $this->belongsTo(Filter::class);
    }


    // Images
    // filter items should not return an array
    // to filter so it can have images
}
