<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Membership extends Model
{
    public $casts = [
        'status_start' => 'date'
    ];
    protected $attributes = ['status' => 'Blue',];
    protected $guarded = [];

    public function customer()
    {
        return $this->belongsTo('App\Customer');
    }

    public function getStart()
    {
        $start = $this->created_at;
        $diff = $start->diffInYears(now());
        return $start->addYears($diff);
    }

    public function getStatus()
    {
        $exp = null;
        if ($this->status_start) {
            $exp = $this->status_start;
            $exp->addYears(1);
            if (now() > $exp) {
                return $this->updateLatestStatus();
            }
        }
        return ['status' => $this->status, 'expiration' => $exp];
    }

    public function updateLatestStatus()
    {
        if ($this->status_start->diffInYears(now()) > 1) {
            $this->status = 'Blue';
            $this->status_start = null;
            $this->save();
            return ['status' => 'Blue', 'expiration' => null];
        }
        $start = $this->created_at;
        $end = $this->status_start->addYears(1);
        $diff = $start->diffInYears($this->end);
        $start->addYears($diff);
        $points = (int)$this->customer->orders->whereBetween('created_at', [$start, $end])->where(
            'status',
            '!=',
            'canceled'
        )
            ->map(function ($purchase) {
                return $purchase['grand_total'] - $purchase['tax_amount'] - $purchase['shipping_amount'];
            })->sum();
        $status = RewardGroup::where('amount', '<', $points)->orderBy('amount', 'desc')->first()['name'] ?? 'Blue';
        $this->status = $status;
        $this->status_start = ($status != 'Blue') ? $end : null;
        $this->save();
        return ['status' => $status, 'expiration' => $end->addYears(1)];
    }
    // public function updateStatus($newStatus){
    //     $this->status=$newStatus;
    //     if($newStatus=='Blue'){
    //         $this->status_start=null;
    //     }else{
    //         $this->status_start=now();
    //     }
    //     $this->save();
    // }
    public static function ForPlaceOrder()
    {
        $user = auth()->user();
        if (!(optional($user)->hasActiveMembership())) {
            return;
        }
        $currentStatus = $user->membership->getStatus()['status'];
        return RewardGroup::getReward($currentStatus);
    }

    public static function removePoints($order)
    {
        self::updateMembershipPoints(
            ($order->grand_total - $order->tax_amount - $order->shipping_amount) * -1
        );
    }

    public static function updatePoints($order)
    {
        self::updateMembershipPoints(
            $order->grand_total - $order->tax_amount - $order->shipping_amount
        );
    }

    public static function updateMembershipPoints($payment)
    {
        $user = auth()->user();
        if (!$user || !$user->hasActiveMembership()) {
            return;
        }
        $pointsInfo = $user->calculatePoints();
        $points = $pointsInfo['points'];
        $user->membership->total_points += $payment;
        $currentStatus = $user->membership->status;
        $newStatus = RewardGroup::getHighestStatus($points, $currentStatus);
        if (!($newStatus == $currentStatus)) {
            $user->membership->status = $newStatus;
            $user->membership->status_start = now();
        }
        $user->membership->save();
        return array_merge($pointsInfo, ['status' => $user->membership->status]);
    }


}
