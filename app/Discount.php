<?php

namespace App;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class Discount extends Model
{
    public $guarded = [];

    protected $casts = [
        'end' => 'datetime',
        'start' => 'datetime',
        'model_id' => 'array',
        'eligibility_id' => 'array',
    ];

    public static $options = [
        'fixed' => 'Fixed',
        'percentage' => 'Percentage',
        'freeShipping' => 'Free Shipping'
    ];
    // will match model type column with a string that is both user friendly and matches the appropiate class.

    public static $models = [
        '' => 'Entire Order',
        'products' => 'Products',
        'categories' => 'Categories',
        'creators' => 'Creators',
        'vendors' => 'Vendor',
        'giftCards' => 'Only Gift Cards',
    ];

    public static $boolean = [
        '1' => 'Automated Discount',
        '0' => 'Coupon Code'
    ];

    public function getModelsAttribute()
    {
        return static::$models;
    }

    public function getOptionsAttribute()
    {
        return static::$options;
    }

    public function getBooleanAttribute()
    {
        return static::$boolean;
    }
    // public function eligibility()
    // {
    //     return $this->morphTo();
    // }
    public function applies()
    {
        $class = 'App\\' . static::$models[$this->model_type];
        return $class::find($this->model_id);
    }

    public function shipping_options()
    {
        return $this->belongsToMany(ShippingOption::class);
    }

    public function getShippingOptionIdsAttribute()
    {
        return $this->shipping_options->pluck('id');
    }

    public function setShippingOptionIdsAttribute($value)
    {
        return;
    }

    public function applyToProduct($bag)
    {
        if (class_basename($bag) == 'Bag') {
            $item = $bag->model;
        }
        if ($item && $item->sale_price && !$this->apply_to_sale) {
            return null;
        }
        if (class_basename($item) == 'Product') {
            $product = $item;
            $price = $product->price;
        } elseif (class_basename($item) == 'VariationInfo') {
            $variation = $item;
            $price = $variation->getPrice();
            $product = $variation->product;
        } elseif ($bag->model_type == 'App\GiftCard' && $this->apply_to_gc) {
            $product = $bag;
            $product->tax_code = 22;
            $price = data_get($bag, 'meta.price');
        } else {
            return null;
        }

        if ($product && $product->tax_code == 22 && !$this->apply_to_gc) {
            return null;
        }

        if ($this->model_type) {
            $model = $this->model_type;
            if ($model == 'products') {
                if (in_array($product->id, $this->model_id)) {
                    return $price;
                }
            } elseif ($model == 'vendors') {
                if (in_array($product->vendor_id, $this->model_id)) {
                    return $price;
                }
            } elseif ($model == 'giftCards') {
                if(
                    $bag->model_type == 'App\GiftCard'
                    || $product->tax_code == 22
                ) {
                    return $price;
                }
            } else {
                $id = $product->$model;
                if ($id->count() > 0) {
                    if ($id instanceof Collection) {
                        if ($id->pluck('id')->intersect($this->model_id)->count()) {
                            return $price;
                        }
                    } else {
                        if (in_array($id->id, $this->model_id)) {
                            return $price;
                        }
                    }
                }
            }
            return null;
        }
        return $price;
    }

    public function getProductTotal($products)
    {
        $total = 0;
        $ids = [];
        foreach ($products as $product) {
            $price = $this->applyToProduct($product);
            if ($price) {
                $total += ($price * $product['quantity']);
                array_push($ids, ['id' => $product['id'], 'type' => $product['type']]);
            }
        }
        return [$total, $ids];
    }

    public function getSavings($products, $location)
    {
        $type = $this->type;
        $savings = 0;
        $ids = [];
        $percent = 0;
        $shippingOptions = [];
        if ($this->free_shipping_location && $this->free_shipping_location != 'BOTH') {
            if (($this->free_shipping_location == 'US' && data_get(
                        $location,
                        'country'
                    ) != 'US') || ($this->free_shipping_location != 'US' && data_get($location, 'country') == 'US')) {
                return 'wrong_location';
            }
        }
        if ($type != "freeShipping") {
            $totalObject = $this->getProductTotal($products);
            $total = $totalObject[0];
            $ids = $totalObject[1];
            // if no products are covered by the discount returns null.
            if ($total == 0) {
                return null;
            }
            if ($type == 'fixed') {
                $savings = ($total >= $this->amount) ? $this->amount : $total;
            } else {
                $percent = $this->amount;
                $percentage = $percent / 100;
                $savings = ($total <= $this->max || !$this->max) ? number_format(
                    $total * $percentage,
                    2,
                    '.',
                    ''
                ) : number_format($this->max * $percentage, 2, '.', '');
            }
        } else {
            $shippingOptions = $this->shipping_options->map(function ($option) {
                return ['name' => $option->visible_name, 'id' => $option->id];
            });
        }
        return [
            'id' => $this->id,
            'type' => $type,
            'savings' => $savings,
            'name' => $this->name,
            'automated' => $this->automated,
            'percent' => $percent,
            'shipping_options' => $shippingOptions
        ];
    }

    public function scopeActive($query)
    {
        return $query
            ->where(function ($query) {
                $query->where('start', '<=', now())
                    ->orWhereNull('start');
            })
            ->where(function ($query) {
                $query->where('end', '>=', now())
                    ->orWhereNull('end');
            });
    }

    public function scopeAutomated($query)
    {
        return $query->where('automated', '=', true);
    }

    public function scopeAbove($query, $min)
    {
        return $query->where('min', '<=', $min)->orWhereNull('min');
    }

    public function scopeUser($query)
    {
        $query = $query
            ->orWhere('eligibility_type', "=", '')
            ->orWhereNull('eligibility_type');

        if ($user = auth()->user()) {
            $query = $query
                ->orWhere(function ($query) use ($user) {
                    $query->where('eligibility_type', '=', 'App\Group')
                        ->whereJsonContains('eligibility_id', $user->group_id);
                })
                ->orWhere(function ($query) use ($user) {
                    $query->where('eligibility_type', '=', 'App\Customer')
                        ->whereJsonContains('eligibility_id', $user->id);
                });
        }
        return $query;
    }

    public function isActive($min, $location)
    {
        $now = now();
        if (!($now >= $this->start || $this->start == null)) {
            abort(400, "This discount is not active yet.");
        }
        if ((!($now <= $this->end || $this->end == null))) {
            abort(400, "The promo code " . $this->name . " has expired.");
        }
        if (!$this->forUser()) {
            abort(400, "Discount is unavailable to customer.");
        }
        if (!$this->aboveMin($min)) {
            abort(400, "Discount requires a minimum of $" . $this->min . " purchase");
        }
        if (!$this->aboveLimit()) {
            abort(400, "This discount has reached the limit.");
        }
        if (!$this->checkLocation($location)) {
            abort(400, "This discount does not cover this shipping option.");
        }
    }

    public function confirmValidity($min, $location)
    {
        $now = now();

        return ($now >= $this->start || $this->start == null)
            && ($now <= $this->end || $this->end == null)
            && $this->forUser() && $this->aboveMin($min) && $this->aboveLimit() && $this->checkLocation($location);
    }

    public function aboveMin($min)
    {
        return $min >= $this->min || $this->min == null;
    }

    public function forUser()
    {
        if (auth()->user()) {
            $user = auth()->user();
            $customer = "App\Customer";
            $group = "App\Group";
            if ($this->eligibility_type == $group) {
                if (collect($this->eligibility_id)->contains($user->group_id)) {
                    if (!$this->checkCustomerLimit($user->id)) {
                        abort(400, "You have reached the limit for this discount.");
                    }
                } else {
                    return false;
                }
            } elseif ($this->eligibility_type == $customer) {
                if (collect($this->eligibility_id)->contains($user->id)) {
                    if (!$this->checkCustomerLimit($user->id)) {
                        abort(400, "You have reached the limit for this discount.");
                    }
                } else {
                    return false;
                }
            } else {
                if ($this->limit_customer) {
                    if (!$this->checkCustomerLimit($user->id)) {
                        abort(400, "You have reached the limit for this discount.");
                    } else {
                        return true;
                    }
                }
            }
            return $this->eligibility == null;
        } else {
            return $this->eligibility == null;
        }
    }

    public function setProductModelIdsAttribute($value)
    {
        $this->attributes['model_id'] = collect(json_decode($value, true))->pluck('id')->values();
    }

    public function setCategoryModelIdsAttribute($value)
    {
        $this->attributes['model_id'] = collect(json_decode($value, true))->pluck('id')->values();
    }

    public function setCreatorModelIdsAttribute($value)
    {
        $this->attributes['model_id'] = collect(json_decode($value, true))->pluck('id')->values();
    }

    public function setVendorModelIdsAttribute($value)
    {
        $this->attributes['model_id'] = collect(json_decode($value, true))->pluck('id')->values();
    }

    public function getCategoryModelIdsAttribute()
    {
        if ($this->model_type == 'categories') {
            return optional(Category::find($this->model_id))->map(function ($item) {
                return [
                    'id' => $item->id,
                    'title' => $item->name,
                    'media' => $item->getFirstMediaUrl('media', 'thumb'),
                ];
            });
        }
    }

    public function getCreatorModelIdsAttribute()
    {
        if ($this->model_type == 'creators') {
            return optional(Creator::find($this->model_id))->map(function ($item) {
                return [
                    'id' => $item->id,
                    'title' => $item->name,
                    'media' => $item->getFirstMediaUrl('media', 'thumb'),
                ];
            });
        }
    }

    public function getVendorModelIdsAttribute()
    {
        if ($this->model_type == 'vendors') {
            return optional(Vendor::find($this->model_id))->map(function ($item) {
                return [
                    'id' => $item->id,
                    'title' => $item->name,
                    'media' => $item->getFirstMediaUrl('media', 'thumb'),
                ];
            });
        }
    }

    public function getProductModelIdsAttribute()
    {
        if ($this->model_type == 'products') {
            return optional(Product::find($this->model_id))->map(function ($item) {
                return [
                    'id' => $item->id,
                    'title' => $item->title,
                    'price' => $item->price,
                    'media' => $item->search['image'],
                    'sku' => $item->sku,
                ];
            });
        }
    }

    public function setCustomerEligibilityIdAttribute($value)
    {
        $this->attributes['eligibility_id'] = collect(json_decode($value, true))->pluck('id')->values();
    }

    public function getCustomerEligibilityIdAttribute()
    {
        if ($this->eligibility_type == 'App\Customer') {
            return optional(DB::table('customers')->whereIn('id', $this->eligibility_id))
                ->get()->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'title' => $item->name,
                    ];
                });
        }
    }

    public function setGroupEligibilityIdAttribute($value)
    {
        $this->attributes['eligibility_id'] = collect(json_decode($value, true))->pluck('id')->values();
    }

    public function getGroupEligibilityIdAttribute()
    {
        if ($this->eligibility_type == 'App\Group') {
            return optional(Group::find($this->eligibility_id))
                ->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'title' => $item->name,
                    ];
                });
        }
    }

    public function _scopeLimit($query)
    {
        return $query
            ->orWhere('limitable', "=", false)
            ->orWhere('limit', '>', 0);
    }

    public function adjustLimit()
    {
        if ($this->limit) {
            $this->limit--;
            $this->save();
        }
    }

    public function checkCustomerLimit($customer_id)
    {
        if ($this->limit_customer) {
            return $this->limit_customer > Order::where(
                    [['status', '!=', 'cancelled'], ['customer_id', '=', $customer_id], ['discount_id', '=', $this->id]]
                )->count();
        }
        return true;
    }

    public function aboveLimit()
    {
        return $this->limitable && $this->limit ? DB::table('orders')->where('discount_id', $this->id)->where(
                'status',
                '!=',
                'cancelled'
            )->count() < $this->limit : true;
        // return $this->limitable ? $this->limit > 0 : true;
    }

    public function getCustomerIdsAttribute($ids)
    {
        // $this->attribute['model_ids'] = $ids;
    }

    public function checkLocation($location)
    {
        if ($this->type != 'freeShipping') {
            return true;
        }
        if (data_get($location, 'country') == 'US') {
            $zipCode = data_get(ZipCode::getCode($location['postal_code']), 'zip_code');
            return $this->getShippingZipCodes()->contains($zipCode);
        }
        return false;
    }

    public function getShippingZipCodes()
    {
        return $this->shipping_options->map(function ($shipping) {
            return $shipping->getZipCodes();
        })->flatten();
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($discount) {
            $request = request();
            if ($request->has('shipping_option_ids')) {
                $discount->shipping_options()->sync(explode(',', $request->shipping_option_ids));
            } else {
                return $discount->shipping_options()->sync([]);
            }
        });
    }
}
