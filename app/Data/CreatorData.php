<?php

namespace App\Data;

use App\Creator;
use App\Enums\CreatorType;
use Carbon\CarbonImmutable;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\Validation\Date;
use Spatie\LaravelData\Data;

class CreatorData extends Data
{
    public function __construct(
        public int             $id,
        public string          $name,
        public ?string         $heb_name,
        public CreatorType     $type,
        public ?string         $description,
        public ?bool           $redirect,
        public ?string         $button_link,
        public ?string         $button_text,
        #[Date]
        public CarbonImmutable $created_at,
        #[Date]
        public CarbonImmutable $updated_at,
        public ?string $image,
        public Collection           $products)
    {
    }

    public static function fromModel(Creator $creator): CreatorData
    {
        return new self(
            $creator->id,
            $creator->name,
            $creator->heb_name,
            CreatorType::from($creator->type),
            $creator->description,
            $creator->redirect,
            $creator->button_link,
            $creator->button_text,
            CarbonImmutable::make($creator->created_at),
            CarbonImmutable::make($creator->updated_at),
            $creator->getFirstMediaUrl('images'),
            ProductData::collect($creator->products()->limit(10)->get())
        );
    }
}
