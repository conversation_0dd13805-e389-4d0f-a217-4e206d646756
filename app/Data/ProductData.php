<?php

namespace App\Data;

use Spa<PERSON>\LaravelData\Data;
use Illuminate\Support\Collection;

class ProductData extends Data
{
    public function __construct(
        public int             $id,
        public string          $title,
        public ?string         $heb_title,
        public string                          $item_type,
        public string                          $path,
        public float                           $price,
        public ?float                          $sale_price,
        public ?array                          $label,
        public ?float                          $from_price,
        public ?float                          $fake_price,
        public ?string                         $vendor,
        public ?string                         $short_desc,
        public ?string                         $description,
        public ?Collection                     $media_urls,
        public ?string                         $heb_short_desc,
        public ?string                         $heb_description,
        public ?int                            $release_date,
        public ?string                         $sku,
        public ?string                         $barcode,
    )
    {
    }

    public static function fromModel($product): ProductData
    {
        return new self(
            $product->id,
            $product->title,
            $product->heb_title,
            $product->item_type,
            $product->path,
            $product->price,
            $product->sale_price,
            $product->getLabel(),
            $product->from_price,
            $product->fake_price,
            optional($product->vendor)->is_visible ? $product->vendor->name : null,
            strip_tags($product->short_desc),
            substr(strip_tags($product->description), 0, 1000),
            $product->media_urls,
            strip_tags($product->heb_short_desc),
            strip_tags($product->heb_description),
            optional($product->release_date)->timestamp,
            $product->sku,
            $product->barcode
        );
    }

}
