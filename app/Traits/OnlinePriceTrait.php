<?php

namespace App\Traits;

use App\OnlinePrice;

trait OnlinePriceTrait
{
    public function onlinePrice()
    {
        return $this->morphOne(OnlinePrice::class, 'model');
    }

    public function getOnlineFinalPriceAttribute()
    {
        if ($onlinePrice = $this->onlinePrice) {
            $price = $this->{$onlinePrice->based_on} * ($onlinePrice->percent / 100);

            // round it to the nearest .49 of .99
            return $price ? (0.5 * round($price * 2)) -.01 : null;

            return $price = (0.5 * round($price * 2)) -.01;
        }
        return $this->online_price ?? null; //?? optional($this->product)->sale_price ?? null;
    }

    public function getAddOnlinePriceAttribute()
    {
        return !!$this->onlinePrice ? 'rules' : 'set';
    }

    public function setAddOnlinePriceAttribute($value)
    {
        if ($value == 'set') {
            $this->onlinePrice()->delete();
        }
    }

    public function getOnlinePriceBasedOnAttribute()
    {
        return $this->onlinePrice ? $this->onlinePrice->based_on : null;
    }

    public function setOnlinePriceBasedOnAttribute()
    {
    }

    public function getOnlinePricePercentAttribute()
    {
        return $this->onlinePrice ? $this->onlinePrice->percent : null;
    }

    public function setOnlinePricePercentAttribute()
    {
    }
}
