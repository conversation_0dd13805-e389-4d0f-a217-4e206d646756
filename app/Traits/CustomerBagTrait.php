<?php

namespace App\Traits;

use App\Bundle;
use App\Product;
use App\VariationInfo;

trait CustomerBagTrait
{
    public $user;

    public function addToBag()
    {
        $user = customer();
        collect(request()->bag)->each(function ($item) use ($user) {
            $class = $this->getClassFromItem($item);
            if ($class == 'App\\GiftCard') {
                $user->bag()->updateOrCreate(
                    [
                        'model_id' => $item['id'],
                        'model_type' => $class,
                        'later' => false,
                        'item_key' => data_get($item, 'item_key')
                    ],
                    [
                        'quantity' => $item['quantity'],
                        'meta' => $item,
                    ]
                );
            } else {
                if ($item['quantity'] == 0) {
                    $user->bag()->where(
                        [
                            'model_id' => $item['id'],
                            'model_type' => $class,
                            'later' => false,
                            'item_key' => data_get($item, 'item_key')
                        ]
                    )->delete();
                } else {
                    $user->bag()->updateOrCreate(
                        [
                            'model_id' => $item['id'],
                            'model_type' => $class,
                            'later' => false,
                            'item_key' => data_get($item, 'item_key')
                        ],
                        [
                            'quantity' => $item['quantity'],
                            'meta->media' => $item['media'] ?? null,
                            'meta->amount' => $item['amount'] ?? null,
                            'meta->gift' => data_get($item, 'gift') ?? null,
                            'meta->gift_options' => $item['gift_options'] ?? null,
                            'meta->personalization' => data_get($item, 'personalization'),
                            'meta->is_personalization' => data_get($item, 'is_personalization') ?? false,
                        ]
                    );
                }
            }
        })->filter();
        return $user->frontEndBag();
    }

    public function removeFromBag()
    {
        $user = customer();

        collect(request()->bag)->each(function ($item) use ($user) {
            $class = $this->getClassFromItem($item);

            $user->bag()
                ->where('model_id', $item['id'])
                ->where('model_type', $class)
                ->where('later', false)
                ->where('item_key', data_get($item, 'item_key'))
                ->delete();
        });

        return $user->frontEndBag();
    }

    public function bag()
    {
        return customer()->frontEndBag();
    }

    public function later()
    {
        return customer()->frontEndBag(true);
    }


    public function addToLater()
    {
        $user = customer();

        collect(request()->later)->each(function ($item) use ($user) {
            $class = $this->getClassFromItem($item);
            $new = $user->bag()->updateOrCreate(
                [
                    'model_id' => $item['id'],
                    'model_type' => $class,
                    'later' => true,
                    'item_key' => data_get($item, 'item_key')
                ],
                [
                    'quantity' => $item['quantity'],
                    'meta->media' => $item['media'] ?? null,
                    'meta->amount' => $item['amount'] ?? null,
                    'meta->gift' => data_get($item, 'gift') ?? null,
                    'meta->gift_options' => $item['gift_options'] ?? null,
                    'meta->personalization' => data_get($item, 'personalization'),

                    'meta->path' => data_get($item, 'path') ?? null,
                    'meta->type' => data_get($item, 'type') ?? null,
                    'meta->media' => data_get($item, 'media') ?? null,
                    'meta->price' => data_get($item, 'price') ?? null,
                    'meta->title' => data_get($item, 'title') ?? null,
                    'meta->vendor' => data_get($item, 'vendor') ?? null,
                    'meta->message' => data_get($item, 'message') ?? null,
                    'meta->to_name' => data_get($item, 'to_name') ?? null,
                    'meta->item_key' => data_get($item, 'item_key') ?? null,
                    'meta->quantity' => data_get($item, 'quantity') ?? null,
                    'meta->to_email' => data_get($item, 'to_email') ?? null,
                    'meta->from_name' => data_get($item, 'from_name') ?? null,
                    'meta->item_type' => data_get($item, 'item_type') ?? null,

                ]
            );
        });

        return $user->frontEndBag(true);
    }

    public function removeFromLater()
    {
        $user = customer();

        collect(request()->later)->each(function ($item) use ($user) {
            $class = $this->getClassFromItem($item);
            $user->bag()
                ->where('model_id', $item['id'])
                ->where('model_type', $class)
                ->where('later', true)
                ->where('item_key', data_get($item, 'item_key'))
                ->delete();
        });

        return $user->frontEndBag(true);
    }

    public function again()
    {
        $user = customer();
        $orders = $user->orders->where('guest', false)->reverse();

        $products = Product::find(
            $orders->map(function ($order) {
                $collection = collect($order->products)->filter(function ($product) {
                    return $product['type'] == 'product';
                });
                return !$collection->isEmpty()
                    ? $collection->mapWithKeys(function ($a) {
                        return $a;
                    })
                    : null;
            })->filter()->pluck('id')
        );
        $bundles = Bundle::find(
            $orders->map(function ($order) {
                $collection = collect($order->products)->filter(function ($product) {
                    return $product['type'] == 'bundles';
                });
                return !$collection->isEmpty()
                    ? $collection->mapWithKeys(function ($a) {
                        return $a;
                    })
                    : null;
            })->filter()->pluck('id')
        );
        $variations = VariationInfo::find(
            $orders->map(function ($order) {
                $collection = collect($order->products)->filter(function ($product) {
                    return $product['type'] == 'variation';
                });
                return !$collection->isEmpty()
                    ? $collection->mapWithKeys(function ($a) {
                        return $a;
                    })
                    : null;
            })->filter()->pluck('id')
        );

        $giftCards = collect(); //just for the dynamic model in the if statement

        return $orders->map(function ($order) use ($products, $bundles, $variations, $giftCards) {
            return collect($order->products)->map(
                function ($product) use ($order, $products, $bundles, $variations, $giftCards) {
                    if ($model = ${$product['type'] . 's'}->where('id', $product['id'])->first()) {
                        // return $model->front_end + [
                        //     'id_type' => $product['id'] . $product['type']
                        // ];
                        return [
                            'id' => $product['id'],
                            'path' => $product['path'],
                            'media' => $product['media'],
                            'title' => $product['title'],
                            'vendor' => $product['vendor'],
                            'type' => $product['type'],
                            'meta' => isset($product['meta']) ? $product['meta'] : null,
                            'date' => $order->created_at->toDateString(),
                            'price' => $model->toArray()['price'],
                            'id_type' => $product['id'] . $product['type']
                        ];
                    }
                }
            )->filter();
        })
            ->collapse()
            ->groupBy('id_type')
            ->values()
            ->map(function ($item) {
                return array_merge($item[0], ['count' => count($item)]);
            });
    }

    public function getClassFromItem($item)
    {
        switch ($item['type']) {
            case 'product':
                return 'App\\Product';
                break;
            case 'bundle':
                return 'App\\Bundle';
                break;
            case 'variation':
                return 'App\\VariationInfo';
                break;
            case 'giftCard':
                return 'App\\GiftCard';
                break;
        }
    }
}
