<?php

namespace App\Traits;

use App\RecurringSetting;
use Illuminate\Support\Str;

trait RecurringTrait
{
    public function recurringSettings()
    {
        return $this->belongsToMany(RecurringSetting::class);
    }

    public function getRecurringArrayAttribute()
    {
        return $this->recurringSettings->map(function ($recurring) {
            return [
                'id' => $recurring->id,
                'name' => $recurring->name,
                'cycle' => $recurring->cycle,
                'amount' => $recurring->amount,
                'display' =>
                    Str::title(
                        $recurring->amount == 1
                            ? "Every $recurring->cycle"
                            : "Every $recurring->amount "
                            . Str::plural($recurring->cycle, $recurring->amount)
                    ),
            ];
        });
    }

    public function getRecurringsAttribute()
    {
        return $this->recurringSettings->pluck('id');
    }

    public function setRecurringsAttribute($value)
    {
        return;
    }
}
