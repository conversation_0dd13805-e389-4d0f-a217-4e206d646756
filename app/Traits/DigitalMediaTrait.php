<?php

namespace App\Traits;

trait DigitalMediaTrait
{
    public function setDigitalAttribute($value)
    {
        return;
    }

    public function getDigitalAttribute($value)
    {
        return optional($this->getMedia('digital')->first())->file_name;
    }
    // public function updateDigital($product)
    // {
    //     $request = request();
    //     if ($request->has('digital_url')) {
    //         $product->addMediaFromUrl($request->digital_url);
    //     }
    // }
}
