<?php

namespace App\Traits;

use App\Product;

trait CustomerFavoriteTrait
{
    public function favorites()
    {
        return Product::whereIn('id', optional(auth()->guard('api')->user())->favorites ?? [])
            ->get()
            ->map(function ($product) {
                return $product->front_end;
            });
    }

    public function removeFavorites()
    {
        $user = auth()->guard('api')->user();

        $user->update([
            'favorites' => collect($user->favorites)->diff(collect(request()->ids))->values()->toArray(),
        ]);

        return $user->favorites;
    }

    public function addFavorites()
    {
        $user = auth()->guard('api')->user();

        $user->update([
            'favorites' => collect($user->favorites)->merge(collect(request()->ids))->values()->toArray(),
        ]);

        return $user->favorites;
    }
}
