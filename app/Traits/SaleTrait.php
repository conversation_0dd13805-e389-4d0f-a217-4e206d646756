<?php

namespace App\Traits;

use App\Sale;
use Illuminate\Support\Facades\Log;

trait SaleTrait
{
    public function sale()
    {
        return $this->morphOne(Sale::class, 'model');
    }

    public $saleOptions = [
        'fixed' => 'Fixed',
        'percent' => 'Percent'
    ];

    public function getSalePriceAttribute()
    {
        if ($sale = $this->sale) {
            $now = now();
            if (($sale->start ? $now->isAfter($sale->start) : true) && ($sale->end ? $now->isBefore($sale->end) : true)) {
                if ($sale->type == 'fixed') {
                    return $sale->amount;
                } elseif ($sale->type == 'percent') {
                    $price = $this->{$sale->from} ?: optional($this->product)->{$sale->from};
                    // ?? $this->list_price
                    // ?? $this->store_price
                    // ?? $this->online_price;
                    $total = $price - (($sale->amount / 100) * $price);
                    return +number_format($total, 2, '.', '');
                }
            }
        }
        return $this->attributes['sale_price'] ?? null; //?? optional($this->product)->sale_price ?? null;
    }

    public function getStoreSalePriceAttribute()
    {
        return $this->attributes['sale_price'] ?? null;
    }

    public function setStoreSalePriceAttribute()
    {
        return;
    }

    public function getAddSaleAttribute()
    {
        return $this->sale ? true : false;
    }

    public function setAddSaleAttribute($value)
    {
        if (!$value) {
            $this->sale()->delete();
        }
    }

    public function getSaleTypeAttribute()
    {
        return $this->sale ? $this->sale->type : null;
    }

    public function setSaleTypeAttribute()
    {
    }

    public function getSaleFromAttribute()
    {
        return $this->sale ? $this->sale->from : null;
    }

    public function setSaleFromAttribute()
    {
    }

    public function getSaleAmountAttribute()
    {
        return $this->sale ? $this->sale->amount : null;
    }

    public function setSaleAmountAttribute()
    {
    }

    public function getStartSaleAttribute()
    {
        return $this->sale ? $this->sale->start : null;
    }

    public function setStartSaleAttribute()
    {
    }

    public function getEndSaleAttribute()
    {
        return $this->sale ? $this->sale->end : null;
    }

    public function setEndSaleAttribute()
    {
    }
}
