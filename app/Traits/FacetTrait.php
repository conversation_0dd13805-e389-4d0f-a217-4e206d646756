<?php

namespace App\Traits;

use App\Filter;
use App\Product;

trait FacetTrait
{
    public function ProductWithFacets($products)
    {
        $request = request();
        $products = $products->active()->with([
            // 'variations',
            // 'variationInfos.product',
            // 'vendor',
            // 'filters.filter',
            // 'categories'
        ]);

        $products = $this->queryFilters($products, $request);
        $products = $this->queryCategories($products, $request);
        $products = $this->queryVendors($products, $request);
        $products = $this->queryVariations($products, $request);
        $products = $this->queryCreators($products, $request);

        return [
            'products' => $products->paginate(40),
            'filters' => $this->getFilters($request),
            'categories' => $this->getCategories($request),
            'vendors' => $this->getVendors($request),
            'variations' => $this->getVariations($request),
            'creators' => $this->getCreators($request),
        ];
    }

    public function QueryFilters($products, $request)
    {
        if ($request->filters) {
            $products = $products->whereHas('filters', function ($query) use ($request) {
                $query->whereIn('filter_item_id', explode(',', $request->filters));
            });
        }
        return $products;
    }

    public function QueryCategories($products, $request)
    {
        if ($request->categories) {
            $products = $products->whereHas('categories', function ($query) use ($request) {
                $query->whereIn('category_id', explode(',', $request->categories));
            });
        }
        return $products;
    }

    public function QueryVendors($products, $request)
    {
        if ($request->vendors) {
            $products = $products->whereIn('vendor_id', explode(',', $request->vendors));
        }
        return $products;
    }

    public function QueryVariations($products, $request)
    {
        if ($request->variations) {
            foreach ($request->variations as $key => $variation) {
                if ($variation) {
                    $products = $products->whereHas('variations', function ($query) use ($key, $variation) {
                        $query->where('name', $key)->where(function ($query) use ($variation) {
                            foreach (explode(',', $variation) as $key => $value) {
                                $query->orWhereJsonContains('values', $value);
                            }
                        });
                    });
                }
            }
        }
        return $products;
    }

    public function QueryCreators($products, $request)
    {
        if ($request->creators) {
            $products = $products->whereHas('creators', function ($query) use ($request) {
                $query->whereIn('creator_id', explode(',', $request->creators));
            });
        }
        return $products;
    }

    public function getFilters($request)
    {
        $products = Product::active();
        $products = $this->queryCategories($products, $request);
        $products = $this->queryVendors($products, $request);
        $products = $this->queryVariations($products, $request);
        $products = $this->queryCreators($products, $request);

        return collect(
            $products
                ->active()
                ->get()
                ->pluck('filters')
                ->flatten()
                ->unique('id')
                ->values()
                ->toArray()
        )->groupBy('parent')
            ->map(function ($item) {
                return [
                    'name' => $item[0]['parent'],
                    'information' => Filter::find($item[0]['filter_id'])->info,
                    'items' => $item
                ];
            });
    }

    public function getVendors($request)
    {
        $products = Product::active();
        $products = $this->queryFilters($products, $request);
        $products = $this->queryCategories($products, $request);
        $products = $this->queryVariations($products, $request);
        $products = $this->queryCreators($products, $request);

        return $products->get()->pluck('vendor')->flatten()->unique('id')->values()->filter();
    }

    public function getCategories($request)
    {
        $products = Product::active();
        $products = $this->queryFilters($products, $request);
        $products = $this->queryVendors($products, $request);
        $products = $this->queryVariations($products, $request);
        $products = $this->queryCreators($products, $request);

        return $products->get()->pluck('categories')->flatten()->unique('id')->values();
    }

    public function getVariations($request)
    {
        $products = Product::active();
        $products = $this->queryFilters($products, $request);
        $products = $this->queryCategories($products, $request);
        $products = $this->queryVendors($products, $request);
        $products = $this->queryCreators($products, $request);

        return $products->get()->pluck('variations')
            ->flatten()
            ->unique('id')
            ->values()
            ->groupBy('name')
            ->map(function ($item) {
                return $item->pluck('values')->flatten()->unique()->values();
            });
    }

    public function getCreators($request)
    {
        $products = Product::active();
        $products = $this->queryFilters($products, $request);
        $products = $this->queryVendors($products, $request);
        $products = $this->queryVariations($products, $request);
        $products = $this->queryCategories($products, $request);

        return $products->get()->pluck('creators')->flatten()->unique('id')->values();
    }
}
