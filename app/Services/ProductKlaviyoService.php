<?php

namespace App\Services;

use App\DTO\Klaviyo\CatalogItemDTO;
use App\Product;

class ProductKlaviyoService {

    /**
     * Convert a product to a CatalogItemDTO.
     *
     * @param Product $product
     * @return CatalogItemDTO
     */
    public static function convertProductToCatalogItem(Product $product): CatalogItemDTO
    {
        $variationInfos = $product->variationInfos()->get();
        $custom_metadata = [];
        $categories = $product->categories()->get();
        if ($product->vendor) {
            $custom_metadata['brand'] = $product->vendor->name;
        }
        if ($product->gtin) {
            $custom_metadata['gtni'] = $product->gtin;
        }
        if ($product->sku) {
            $custom_metadata['sku'] = $product->sku;
        }
        if (count($variationInfos) > 0) {
            foreach ($variationInfos as $variationInfo) {
                $meta = json_decode($variationInfo->meta, true);
                foreach ($meta as $key => $value) {
                    if (!isset($custom_metadata[$key])) {
                        $custom_metadata[$key] = '';
                    }
                    if (is_array($value)) {
                        $custom_metadata[$key] .= implode(',', $value) . ',';
                    } else {
                        $custom_metadata[$key] .= $value . ',';
                    }
                }
            }
        }

        return new CatalogItemDTO(
            id: $product->id,
            title: $product->title,
            price: $product->price ?? 0.0,
            description: strip_tags(htmlspecialchars_decode($product->description, ENT_QUOTES)),
            url: env('APP_URL') . $product->path,
            image_full_url: $product->getFirstMediaUrl('media', 'full'),
            image_thumbnail_url: $product->getFirstMediaUrl('media', 'thumbnail'),
            custom_metadata: $custom_metadata,
            categories: $categories->pluck('id')->toArray(),
        );
    }
}
