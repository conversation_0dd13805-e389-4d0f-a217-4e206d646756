<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
class ProductSalesService
{
    /**
     * Retrieves sales data for a specified period.
     *
     * @param string $startDate
     * @param string $endDate
     * @return array<int, array{quantity: int, total_amount: float}>
     */
    public static function getSalesDataForPeriod(string $startDate, string $endDate): array
    {
        $startDate = Carbon::parse($startDate)->startOfDay();
        $endDate = Carbon::parse($endDate)->endOfDay();

        $orders = DB::table('orders')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->pluck('products');

        if ($orders->isEmpty()) {
            return [];
        }

        $salesData = collect($orders)
            ->flatMap(fn ($json) => json_decode($json, true) ?? [])
            ->groupBy('product_id')
            ->mapWithKeys(function ($items, $key) {
                return [
                    $key => [
                        'quantity' => collect($items)->sum('quantity'),
                        'total_amount' => collect($items)->sum('total_amount'),
                    ],
                ];
            });

        return $salesData->toArray();
    }

    /**
     * Retrieves sales data for a specific product within a given period.
     *
     * @param int $productId
     * @param string $startDate
     * @param string $endDate
     * @return array{quantity: int, total_amount: float}
     */
    public static function getSalesForProduct(int $productId, string $startDate, string $endDate): array
    {
        $startDate = Carbon::parse($startDate)->startOfDay();
        $endDate = Carbon::parse($endDate)->endOfDay();

        $orders = DB::table('orders')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->whereRaw("JSON_CONTAINS(products, '{\"product_id\": $productId}', '$')")
            ->pluck('products');

        if ($orders->isEmpty()) {
            return [
                'quantity' => 0,
                'total_amount' => 0.0,
            ];
        }

        $salesData = collect($orders)
            ->flatMap(fn ($json) => json_decode($json, true) ?? [])
            ->groupBy('product_id')
            ->mapWithKeys(function ($items, $key) {
                return [
                    $key => [
                        'quantity' => collect($items)->sum('quantity'),
                        'total_amount' => collect($items)->sum('total_amount'),
                    ],
                ];
            })->toArray();

        return $salesData[$productId];
    }
}
