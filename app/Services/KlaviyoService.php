<?php

namespace App\Services;

use App\Category;
use App\DTO\Klaviyo\BulkCatalogJobDTO;
use App\DTO\Klaviyo\CatalogItemDTO;
use App\DTO\Klaviyo\CreateKlaviyoEventDTO;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;

class KlaviyoService {

    public Client $client;

    public function __construct(){
        $this->client = new Client([
            'base_uri' => config('klaviyo.api_url'),
            'headers' => [
                'Authorization' => 'Klaviyo-API-Key ' . config('klaviyo.private_key'),
                'Accept' => 'application/vnd.api+json',
                'content-type' => 'application/vnd.api+json',
                'revision' => '2025-04-15',
            ]
        ],);
    }

    /**
     * Get the list of email templates
     *
     * @return array
     * @throws \Exception
     */
    public function getTemplates(): array
    {
        try {
            $response = $this->client->get('api/templates');
        } catch (GuzzleException $e) {
            Log::error('Error fetching Klaviyo templates: ' . $e->getMessage());
            throw new \Exception('Error fetching Klaviyo templates: ' . $e->getMessage());
        }
        if ($response->getStatusCode() == 200) {
            $body = json_decode($response->getBody(), true);
            return $body['data'] ?? [];
        } else {
            throw new \Exception('Failed to fetch templates: ' . $response->getStatusCode());
        }
    }

    /**
     * Create a new event in Klaviyo
     *
     * @param CreateKlaviyoEventDTO $data
     * @return array
     * @throws \Exception
     */
    public function createEvent(CreateKlaviyoEventDTO $data): array
    {
        try {
            $response = $this->client->post('api/events', [
                'body' => json_encode($data->toApiPayload()),
            ]);
            if ($response->getStatusCode() == 202) {
                $body = json_decode($response->getBody(), true);
                return $body['data'] ?? [];
            } else {
                throw new \Exception('Failed to create event: ' . $response->getStatusCode());
            }
        } catch (GuzzleException $e) {
            Log::error('Request failed Klaviyo: ' . $e->getMessage());
            throw new \Exception('Request failed Klaviyo: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error creating Klaviyo event: ' . $e->getMessage());
            throw new \Exception('Error creating Klaviyo event: ' . $e->getMessage());
        }
    }

    /**
     * Get the list of catalog items from Klaviyo
     *
     * @return array
     * @throws \Exception
     */
    public function getCatalogItems(string $fullUrl = null): array
    {
        try {
            $response = $fullUrl
                ? $this->client->get($fullUrl)
                : $this->client->get('api/catalog-items', [
                    'query' => [
                        'fields[catalog-item]' => 'external_id',
                    ],
                ]);
            if ($response->getStatusCode() == 200) {
                return json_decode($response->getBody(), true);
            } else {
                throw new \Exception('Failed to fetch catalog items: ' . $response->getStatusCode());
            }
        } catch (GuzzleException $e) {
            Log::error('Request failed Klaviyo: ' . $e->getMessage());
            throw new \Exception('Request failed Klaviyo: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error fetching catalog items: ' . $e->getMessage());
            throw new \Exception('Error fetching catalog items: ' . $e->getMessage());
        }
    }

    /**
     * Send a bulk update job for catalog items to Klaviyo
     *
     * @param BulkCatalogJobDTO $job
     * @return array
     * @throws \Exception
     */
    public function bulkUpdateCatalogItems(BulkCatalogJobDTO $job): array
    {
        try {
            $response = $this->client->post('api/catalog-item-bulk-update-jobs', [
                'body' => json_encode($job->toApiPayload()),
            ]);

            if (in_array($response->getStatusCode(), [200, 201, 202])) {
                return json_decode($response->getBody(), true);
            }

            throw new \Exception('Failed to send bulk update: ' . $response->getStatusCode());

        } catch (GuzzleException $e) {
            Log::error('Request failed (bulk update): ' . $e->getMessage());
            throw new \Exception('Request failed (bulk update): ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error in bulk catalog update: ' . $e->getMessage());
            throw new \Exception('Error in bulk catalog update: ' . $e->getMessage());
        }
    }

    /**
     * Send a bulk create job for catalog items to Klaviyo
     * @param BulkCatalogJobDTO $job
     * @return array
     * @throws \Exception
     */
    public function bulkCreateCatalogItems(BulkCatalogJobDTO $job): array {
        try {
            $response = $this->client->post('api/catalog-item-bulk-create-jobs', [
                'body' => json_encode($job->toApiPayload()),
            ]);

            if (in_array($response->getStatusCode(), [200, 201, 202])) {
                return json_decode($response->getBody(), true);
            }

            throw new \Exception('Failed to send bulk create: ' . $response->getStatusCode());

        } catch (GuzzleException $e) {
            Log::error('Request failed (bulk create): ' . $e->getMessage());
            throw new \Exception('Request failed (bulk create): ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error in bulk catalog create: ' . $e->getMessage());
            throw new \Exception('Error in bulk catalog create: ' . $e->getMessage());
        }
    }

    /**
     * Bulk delete catalog items in Klaviyo
     * @param string[] $ids
     * @return array
     * @throws \Exception
     */
    public function bulkDeleteCatalogItems(array $ids): array
    {
        try {
            $response = $this->client->post('api/catalog-item-bulk-delete-jobs', [
                'body' => json_encode([
                    'data' => [
                        'type' => 'catalog-item-bulk-delete-job',
                        'attributes' => [
                            'items' => [
                                'data' => array_map(fn($id) => [
                                    'type' => 'catalog-item',
                                    'id' => $id
                                ], $ids),
                            ],
                        ],
                    ],
                ]),
            ]);

            if (in_array($response->getStatusCode(), [200, 201, 202])) {
                return json_decode($response->getBody(), true);
            }

            throw new \Exception('Failed to send bulk delete: ' . $response->getStatusCode());

        } catch (GuzzleException $e) {
            Log::error('Request failed (bulk delete): ' . $e->getMessage());
            throw new \Exception('Request failed (bulk delete): ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error in bulk catalog delete: ' . $e->getMessage());
            throw new \Exception('Error in bulk catalog delete: ' . $e->getMessage());
        }
    }

    /**
     * Update a single catalog item in Klaviyo
     *
     * @param int|string $id
     * @param CatalogItemDTO $data
     * @return array
     * @throws \Exception
     */
    public function updateCatalogItem(int|string $id, CatalogItemDTO $data): array
    {
        try {
            $response = $this->client->patch('api/catalog-items/$custom:::$default:::product-' . $id, [
                'body' => json_encode([
                    'data' => $data->toApiItem(true, false),
                ])
            ]);

            if (in_array($response->getStatusCode(), [200, 201, 202])) {
                return json_decode($response->getBody(), true);
            }

            throw new \Exception('Failed to send bulk update: ' . $response->getStatusCode());

        } catch (GuzzleException $e) {
            Log::error('Request failed (bulk update): ' . $e->getMessage());
            throw new \Exception('Request failed (bulk update): ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error in bulk catalog update: ' . $e->getMessage());
            throw new \Exception('Error in bulk catalog update: ' . $e->getMessage());
        }
    }

    /**
     * Create a new catalog item in Klaviyo
     *
     * @param CatalogItemDTO $data
     * @return array
     * @throws \Exception
     */
    public function createCatalogItem(CatalogItemDTO $data): array
    {
        try {
            $response = $this->client->post('api/catalog-items', [
                'body' => json_encode([
                    'data' => $data->toApiItem(false, true),
                ]),
            ]);

            if (in_array($response->getStatusCode(), [200, 201, 202])) {
                return json_decode($response->getBody(), true);
            }

            throw new \Exception('Failed to create catalog item: ' . $response->getStatusCode());

        } catch (GuzzleException $e) {
            Log::error('Request failed (create catalog item): ' . $e->getMessage());
            throw new \Exception('Request failed (create catalog item): ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error creating catalog item: ' . $e->getMessage());
            throw new \Exception('Error creating catalog item: ' . $e->getMessage());
        }
    }

    /**
     * Delete a catalog item in Klaviyo
     *
     * @param int $id
     * @return bool
     * @throws \Exception
     */
    public function deleteCatalogItem(int $id): bool
    {
        try {
            $response = $this->client->delete('api/catalog-items/$custom:::$default:::product-' . $id);

            if (in_array($response->getStatusCode(), [200, 204])) {
                return true;
            }

            throw new \Exception('Failed to delete catalog item: ' . $response->getStatusCode());

        } catch (GuzzleException $e) {
            Log::error('Request failed (delete catalog item): ' . $e->getMessage());
            throw new \Exception('Request failed (delete catalog item): ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error deleting catalog item: ' . $e->getMessage());
            throw new \Exception('Error deleting catalog item: ' . $e->getMessage());
        }
    }

    /**
     * Get a specific catalog category by ID
     *
     * @param int $id
     * @return array | null
     */
    public function getCatalogCategory(int $id): array | null
    {
        try {
            $response = $this->client->get('api/catalog-categories/$custom:::$default:::category-' . $id);
            Log::debug('Klaviyo getCatalogCategory response: ', [
                'status' => $response->getStatusCode(),
                'body' => $response->getBody()->getContents(),
            ]);
            if ($response->getStatusCode() == 200) {
                return json_decode($response->getBody(), true);
            } else {
                return null;
            }
        } catch (\Exception $e) {
            Log::error('Error fetching catalog category: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create a new catalog category in Klaviyo
     *
     * @param Category $category
     * @return array
     * @throws \Exception
     */
    public function createCatalogCategory(Category $category): array
    {
        try {
            $response = $this->client->post('api/catalog-categories', [
                'body' => json_encode([
                    'data' => [
                        'type' => 'catalog-category',
                        'attributes' => [
                            'name' => $category->name,
                            'external_id' => 'category-' . $category->id,
                        ],
                    ],
                ]),
            ]);

            if (in_array($response->getStatusCode(), [200, 201, 202])) {
                return json_decode($response->getBody(), true);
            }

            throw new \Exception('Failed to create catalog category: ' . $response->getStatusCode());

        } catch (GuzzleException $e) {
            Log::error('Request failed (create catalog category): ' . $e->getMessage());
            throw new \Exception('Request failed (create catalog category): ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error creating catalog category: ' . $e->getMessage());
            throw new \Exception('Error creating catalog category: ' . $e->getMessage());
        }
    }
}
