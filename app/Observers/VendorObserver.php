<?php

namespace App\Observers;

use App\Jobs\UpdateProductSearch;
use App\Product;
use App\Vendor;
use Illuminate\Support\Facades\Log;

class VendorObserver
{
    public function updated(Vendor $vendor): void
    {
        Product::isSearchable()->where('vendor_id', $vendor->id)->select('id')->chunk(500, function ($products) {
            $products = $products->pluck('id')->toArray();
            UpdateProductSearch::dispatch($products);
        });
    }

    public function deleted(Vendor $vendor): void
    {
        Product::isSearchable()->where('vendor_id', $vendor->id)->select('id')->chunk(500, function ($products) {
            $products = $products->pluck('id')->toArray();
            UpdateProductSearch::dispatch($products);
        });
    }
}
