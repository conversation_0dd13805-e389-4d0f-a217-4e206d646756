<?php

namespace App\Observers;

use App\Bundle;
use App\BundleItem;
use Illuminate\Support\Str;

class BundleObserver
{
    public function saving(Bundle $bundle)
    {
        $bundle->slug = $this->make_slug($bundle);
    }

    public function saved(Bundle $bundle)
    {
        $this->saveItems($bundle);
        $this->saveIds($bundle);

        $bundle->updateSearchFeild();
    }

    public function saveIds($bundle)
    {
        $request = request();
        if (count($request->all()) > 0) {
            $bundle->filters()->sync(explode(',', $request->filter_ids));
            $bundle->categories()->sync(explode(',', $request->categories_ids));
        }
    }

    private function saveItems($bundle)
    {
        $items = collect(json_decode(request()->formatted_products))->map(function ($item) {
            switch ($item->type) {
                case 'product':
                    $type = 'App\Product';
                    break;

                case 'variation':
                    $type = 'App\VariationInfo';
                    break;
            }
            return BundleItem::make([
                'model_type' => $type,
                'model_id' => $item->id,
                'quantity' => $item->quantity,
            ]);
        });
        $bundle->bundleItems()->delete();
        $bundle->bundleItems()->saveMany($items);
    }

    public function make_slug($bundle)
    {
        return Str::slug($bundle->title);
    }
}
