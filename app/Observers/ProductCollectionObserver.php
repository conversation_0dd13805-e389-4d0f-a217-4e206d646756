<?php

namespace App\Observers;

use App\ProductCollection;
use Illuminate\Support\Str;

class ProductCollectionObserver
{
    public function saving(ProductCollection $productCollection)
    {
        $productCollection->slug = $this->make_slug($productCollection);
    }

    public function saved(ProductCollection $productCollection)
    {
        $this->saveProducts($productCollection);
    }

    private function saveProducts($productCollection)
    {
        $request = request();
        if ($request->has('formatted_products') && $request->formatted_products) {
            $items = collect(json_decode($request->formatted_products))->map(function ($item) {
                return $item->id;
            });

            $productCollection->products = $items->toArray();
            $productCollection->saveQuietly(); // Save without triggering events again
        }
    }

    public function make_slug($productCollection)
    {
        return Str::slug($productCollection->name);
    }
}
