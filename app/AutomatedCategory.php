<?php

namespace App;

use <PERSON><PERSON>\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class AutomatedCategory extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $guarded = [];

    protected $casts = [
        'categories_and' => 'array',
        'categories_or' => 'array',
        'categories_exclude' => 'array',
        'filters_and' => 'array',
        'filters_or' => 'array',
        'filters_exclude' => 'array',
        'vendors_and' => 'array',
        'vendors_or' => 'array',
        'vendors_exclude' => 'array',
        'creators_and' => 'array',
        'creators_or' => 'array',
        'creators_exclude' => 'array',
        'tags_and' => 'array',
        'tags_or' => 'array',
        'tags_exclude' => 'array',
        'labels_and' => 'array',
        'labels_or' => 'array',
        'labels_exclude' => 'array',
    ];

    public function getPathAttribute()
    {
        return "/a/categories/" . str_slug($this->name) . "/{$this->id}";
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->background('#ffffff')
            ->width(130)
            ->height(130);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image')
            ->singleFile();
    }

    public function products()
    {
        // Get product IDs that match our criteria first
        $baseQuery = \App\Product::query();
        $this->applyAutomatedCriteria($baseQuery);
        $productIds = $baseQuery->pluck('id')->toArray();


        return $this->HasMany(\App\Product::class, 'id')
            ->where(function ($query) {
                // Clear any existing constraints and apply our automated criteria
                $query->whereRaw('1=1'); // Always true condition to override pivot constraints
            })
            ->orwhereIn('products.id', $productIds);
    }



    protected function applyAutomatedCriteria($query)
    {
        // Apply price filters
        if ($this->from_price) {
            $query->where('price', '>=', $this->from_price);
        }
        if ($this->to_price) {
            $query->where('price', '<=', $this->to_price);
        }

        // Apply category filters
        if ($this->categories_and) {
            $categoryIds = explode(',', $this->categories_and);
            // Product must be in ALL specified categories
            foreach ($categoryIds as $categoryId) {
                $query->whereHas('categories', function ($q) use ($categoryId) {
                    $q->where('categories.id', $categoryId);
                });
            }
        }
        if ($this->categories_or) {
            $categoryIds = explode(',', $this->categories_or);
            $query->whereHas('categories', function ($q) use ($categoryIds) {
                $q->whereIn('categories.id', $categoryIds);
            });
        }
        if ($this->categories_exclude) {
            $categoryIds = explode(',', $this->categories_exclude);
            $query->whereDoesntHave('categories', function ($q) use ($categoryIds) {
                $q->whereIn('categories.id', $categoryIds);
            });
        }

        // Apply vendor filters
        if ($this->vendors_and) {
            $vendorIds = explode(',', $this->vendors_and);
            $query->whereIn('vendor_id', $vendorIds);
        }
        if ($this->vendors_or) {
            $vendorIds = explode(',', $this->vendors_or);
            $query->whereIn('vendor_id', $vendorIds);
        }
        if ($this->vendors_exclude) {
            $vendorIds = explode(',', $this->vendors_exclude);
            $query->whereNotIn('vendor_id', $vendorIds);
        }

        // Apply creator filters
        if ($this->creators_and) {
            $creatorIds = explode(',', $this->creators_and);
            // Product must have ALL specified creators
            foreach ($creatorIds as $creatorId) {
                $query->whereHas('creators', function ($q) use ($creatorId) {
                    $q->where('creators.id', $creatorId);
                });
            }
        }
        if ($this->creators_or) {
            $creatorIds = explode(',', $this->creators_or);
            $query->whereHas('creators', function ($q) use ($creatorIds) {
                $q->whereIn('creators.id', $creatorIds);
            });
        }
        if ($this->creators_exclude) {
            $creatorIds = explode(',', $this->creators_exclude);
            $query->whereDoesntHave('creators', function ($q) use ($creatorIds) {
                $q->whereIn('creators.id', $creatorIds);
            });
        }

        // Apply tag filters
        if ($this->tags_and) {
            $tagIds = explode(',', $this->tags_and);
            // Product must have ALL specified tags
            foreach ($tagIds as $tagId) {
                $query->whereHas('tags', function ($q) use ($tagId) {
                    $q->where('tags.id', $tagId);
                });
            }
        }
        if ($this->tags_or) {
            $tagIds = explode(',', $this->tags_or);
            $query->whereHas('tags', function ($q) use ($tagIds) {
                $q->whereIn('tags.id', $tagIds);
            });
        }
        if ($this->tags_exclude) {
            $tagIds = explode(',', $this->tags_exclude);
            $query->whereDoesntHave('tags', function ($q) use ($tagIds) {
                $q->whereIn('tags.id', $tagIds);
            });
        }

        // Apply filter item filters
        if ($this->filters_and) {
            $filterIds = explode(',', $this->filters_and);
            // Product must have ALL specified filters
            foreach ($filterIds as $filterId) {
                $query->whereHas('filters', function ($q) use ($filterId) {
                    $q->where('filter_items.id', $filterId);
                });
            }
        }
        if ($this->filters_or) {
            $filterIds = explode(',', $this->filters_or);
            $query->whereHas('filters', function ($q) use ($filterIds) {
                $q->whereIn('filter_items.id', $filterIds);
            });
        }
        if ($this->filters_exclude) {
            $filterIds = explode(',', $this->filters_exclude);
            $query->whereDoesntHave('filters', function ($q) use ($filterIds) {
                $q->whereIn('filter_items.id', $filterIds);
            });
        }

        // Apply status filter
        if ($this->status === 'sale') {
            $query->where('sale_price', '>', 0);
        } elseif ($this->status === 'new') {
            $query->where('created_at', '>=', now()->subDays(30 * 5));
        }

        // Apply search query if provided
        if ($this->contains) {
            $query->where(function ($q) {
                $q->where('title', 'like', '%' . $this->contains . '%')
                  ->orWhere('description', 'like', '%' . $this->contains . '%')
                  ->orWhere('sku', 'like', '%' . $this->contains . '%');
            });
        }
    }

}
