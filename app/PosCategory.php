<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class PosCategory extends Model
{
    protected $guarded = [];

    protected $withCount = ['products'];

    public function PosSubCategories() {
        return $this->hasMany(PosSubCategory::class, 'parent', 'name');
    }

    public function products() {
        return $this->hasMany(Product::class, 'store_category', 'name');
    }

    public function variations() {
        return $this->hasMany(VariationInfo::class, 'store_category', 'name');
    }

    protected static function boot() {
        parent::boot();

        static::saved(function ($posCategory) {
            
            $increase = $posCategory->increase;

            $productIds = $posCategory->products()->pluck('id')->chunk(50);
            $productIds->each(function ($ids) use ($increase) {
                dispatch(function () use ($ids, $increase) { 
                    \App\Product::find($ids)
                        ->each(function ($product) use ($increase) {
                            if($increase) {
                                $data = [
                                    'percent' => $increase + 100,
                                    'based_on' => 'store_price',
                                ];
                                $product->onlinePrice()->updateOrCreate([], $data);
                            } else {
                                $product->onlinePrice ? $product->onlinePrice->delete() : null;
                            }

                            $product->updateSearchFeild();
                            $product->refreshCache();
                        });
                });
            });

            $variationIds = $posCategory->variations()->pluck('id')->chunk(50);
            $variationIds
                ->each(function ($ids) use ($increase) {
                    dispatch(function () use ($ids, $increase) { 
                        \App\VariationInfo::find($ids)
                            ->each(function ($variation) use ($increase){
                                if($increase) {
                                    $data = [
                                        'percent' => $increase + 100,
                                        'based_on' => 'store_price',
                                    ];
                                    $variation->onlinePrice()->updateOrCreate([], $data);
                                } else {
                                    $variation->onlinePrice ? $variation->onlinePrice->delete() : null;
                                }
                                $variation->updateProductJson();
                                $variation->product->updateSearchFeild();
                                $variation->product->refreshCache();
                            });
                    });
                });
        });
    }
}
