<?php

namespace App;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;

class SubscriptionType extends Model
{
    public $guarded = [];

    public $appends = [
        'dates',
    ];

    public function combinations()
    {
        return $this->hasMany(Combination::class, 'type_id', 'id');
    }

    public function groups()
    {
        return $this->hasMany(SubscriptionGroup::class, 'filter_id', 'filter_id');
    }

    public function done()
    {
        $last_date = $this->items->sortByDesc('date')->first()->date;
        return now()->isAfter($last_date);
    }

    public function filter()
    {
        return $this->belongsTo(Filter::class);
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class)
            ->where('status', null)
            ->orWhere('status', '!=', 'canceled');
    }

    public function items()
    {
        return $this->hasMany(SubscriptionTypeItem::class);
    }

    public function ___getFirstProducts($group)
    {
        $nexts = $this->items()->whereDate('date', '>=', today())->orderBy('date');

        $product_ids = $group->items->map->product_id;

        $filter_ids = DB::table('filter_item_product')
            ->whereIn('product_id', $product_ids)
            ->get()->pluck('filter_item_id');

        $next = $nexts->whereIn('filter_item_id', $filter_ids)->first();

        if (!$next) {
            return null;
        }
        $products = Product::setEagerLoads([])->find(
            DB::table('filter_item_product')
                ->where('filter_item_id', $next->filter_item_id)
                ->whereIn('product_id', $product_ids)
                ->get()->pluck('product_id')
        )->map->front_end;

        return [
            'date' => $next->date,
            'name' => $this->name,
            'product' => $products,
            'filter_name' => $group->filter->name,
            'filter_item_name' => $next->filter_item->name,
        ];
    }

    public function ___getAnyProduct($group)
    {
        $nexts = $this->items()->orderByDesc('date');

        $product_ids = $group->items->map->product_id;

        $filter_ids = DB::table('filter_item_product')
            ->whereIn('product_id', $product_ids)
            ->get()->pluck('filter_item_id');

        $next = $nexts->whereIn('filter_item_id', $filter_ids)->first();

        if (!$next) {
            return null;
        }
        $products = Product::setEagerLoads([])->find(
            DB::table('filter_item_product')
                ->where('filter_item_id', $next->filter_item_id)
                ->whereIn('product_id', $product_ids)
                ->get()->pluck('product_id')
        )->map->front_end;

        return [
            'date' => $next->date,
            'name' => $this->name,
            'product' => $products,
            'filter_name' => $group->filter->name,
            'filter_item_name' => $next->filter_item->name,
        ];
    }

    public function ___getNextProduct($group_id)
    {
        $nexts = $this->next();

        $products = $this->groups->find($group_id)->items->map->model;

        return $products->map(function ($product) use ($nexts) {
            if (!$product) {
                return;
            }
            if ($product->product) {
                $parent = $product->product;
            } else {
                $parent = $product;
            }
            return $nexts->map(function ($next) use ($parent, $product) {
                if ($filter = $parent->filters->find($next->filter_item_id)) {
                    return [
                        'date' => $next->date,
                        'name' => $filter->name,
                        'product' => $product->front_end,
                        'filter_name' => $this->filter->name,
                    ];
                }
            })->filter();
        })->flatten(1)->groupBy('date');
    }

    public function getDatesAttribute()
    {
        return $this->items->map(function ($item) {
            return [
                'id' => $item->filter_item_id,
                'date' => $item->date,
            ];
        });
    }

    public function setDatesAttribute()
    {
        //see boot function
        return;
    }

    public function next()
    {
        return $this
            ->items()
            ->whereDate('date', '>=', today())
            ->get();
    }

    public function hasForToday()
    {
        return $this->hasForDate(today());
    }

    public static function hasForDate($date)
    {
        return self::whereHas('items', function ($query) use ($date) {
            $query->whereDate('date', $date);
        })->get();
    }

    public function todaysSubscriptions()
    {
        return $this->hasForToday()->map(function ($type) {
            return $type->subscriptions;
        });
    }

    public static function sendTodaysEmails()
    {
        $date = today()->addWeek(3);
        $types = self::hasForDate($date);

        $types->map->subscriptions->flatten(1)->each->sendEmail($date);
    }

    public static function createTodaysOrders()
    {
        $date = today()->addWeeks(2);
        $types = self::hasForDate($date);

        $types->map->subscriptions->flatten(1)->each->createOrder($date);
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($subscription_type) {
            $request = request();
            $ids = [];
            if ($request->has('dates')) {
                collect(forceArray($request->dates))->each(function ($item) use ($subscription_type, &$ids) {
                    $ids[] = $subscription_type->items()->updateOrCreate(
                        ['filter_item_id' => $item['id']],
                        ['date' => data_get($item, 'date')]
                    )->id;
                });
                $subscription_type->items()->whereNotIn('id', $ids)->delete();
            }
            Combination::typeSaved($subscription_type);
        });
    }
}
