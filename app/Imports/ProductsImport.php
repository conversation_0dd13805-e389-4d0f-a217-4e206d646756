<?php

namespace App\Imports;

use App\Product;
use Illuminate\Database\Eloquent\Model;
use Maatwebsite\Excel\Concerns\ToModel;

class ProductsImport implements ToModel
{
    /**
     * @param array $row
     *
     * @return Model|null
     */
    public function model(array $row)
    {
        return Product::updateOrCreate(['id' => $row['id']], [
            'title' => $row[$title_index],
            'sku' => $row[$sku_index],
            'list_price' => $row[$list_price_index],
            'store_price' => $row[$store_price_index],
            'barcode' => $row[$barcode_price_index],
            'visibilty' => 0,
        ]);
    }
}
