<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ZipCode extends Model
{
    protected $fillable = ['zip_code', 'state', 'shipping_zone_id', 'tax_rate'];

    public function shippingZone()
    {
        return $this->belongsTo('App\ShippingZone');
    }

    public static function getCode($zip_code)
    {
        return self::whereIn('zip_code', [
            $zip_code,
            substr($zip_code, 0, 5),
            substr($zip_code, 0, 4),
            substr($zip_code, 0, 3),
        ])->get()->sortByDesc(function ($zip) {
            return strlen($zip->zip_code);
        })->first();
    }
}
