<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Department extends Model
{
    protected $appends = ['path'];

    protected $hidden = ['created_at', 'updated_at'];

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function getPathAttribute()
    {
        return "/departments/" . str_slug($this->name) . "/{$this->id}";
    }
}
