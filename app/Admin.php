<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Route;

class Admin extends Authenticatable
{
    use Notifiable, HasFactory;

    // protected $guard = 'admin';

    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];
    public static $options = [
        'Super Admin' => 'Super Admin',
        'Product Lister' => 'Product Lister',
        'Order Fulfiller' => 'Order Fulfiller',
        'Return Fulfiller' => 'Return Fulfiller',
        'Product Viewer' => 'Product Viewer',
    ];

    public function getOptionsAttribute()
    {
        return static::$options;
    }

    public static function routes()
    {
        Route::get('login', 'Auth\Admin\LoginController@showLoginForm')->name('admin.login');
        Route::post('login', 'Auth\Admin\LoginController@login');
        Route::post('logout', 'Auth\Admin\LoginController@logout')->name('admin.logout');
        Route::post('password/email', 'Auth\Admin\ForgotPasswordController@sendResetLinkEmail')
            ->name('admin.password.email');
        Route::get('password/reset', 'Auth\Admin\ForgotPasswordController@showLinkRequestForm')
            ->name('admin.password.request');
        Route::post('password/reset', 'Auth\Admin\ResetPasswordController@reset')
            ->name('admin.password.update');
        Route::post('password/reset/{token}', 'Auth\Admin\ResetPasswordController@showResetForm')
            ->name('admin.password.reset');
    }
}
