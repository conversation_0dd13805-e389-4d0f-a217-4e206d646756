<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\Http\Controllers\Api\PayPalController;

class PaypalPayment extends Model
{
    protected $guarded = [];

    public $casts = [
        'captures' => 'array',
        'authorization' => 'array',
        'refunds' => 'array',
        'amount' => 'array',
        'payer_info' => 'array',
    ];

    public function order()
    {
        return $this->morphOne(Order::class, 'payment');
    }

    public function getTotalAttribute()
    {
        return $this->amount['total'];
    }

    public function getRefundAttribute()
    {
        return collect($this->refunds)->pluck('amount.total')->sum();
    }

    public function charge($order, $amount)
    {
        PayPalController::Capture($order, $amount);
    }

    public function authorize($order, $amount)
    {
        return PayPalController::AuthorizePP($order, $amount);
    }

    public function capture($order)
    {
        $amount = data_get($order->payment, 'authorization.amount.total_left');
        return PayPalController::Capture($order, $amount);
    }

    public function cancel($order)
    {
        return PayPalController::Void($order->payment_id);
    }

    public function refund($order, $amount)
    {
        return PayPalController::Refund($order->payment_id, data_get($order->payment, 'captures.captureId'), $amount);
    }
}
