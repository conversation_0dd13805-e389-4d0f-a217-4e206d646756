<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class PosSubCategory extends Model
{
    protected $guarded = [];

    protected $appends = ['products_count'];
    
    public function posCategory() {
        return $this->belongsTo(PosCategory::class, 'parent', 'name');
    }

    public function products() {
        return $this
            ->hasMany(Product::class, 'store_sub_category', 'name')
            ->where('store_category', $this->parent);
    }

    public function variations() {
        return $this
            ->hasMany(VariationInfo::class, 'store_sub_category', 'name')
            ->where('store_category', $this->parent);
    }

    function getProductsCountAttribute() {
        return $this->products()->count();
    }


    protected static function boot() {
        parent::boot();

        static::saved(function ($posSubCategory) {
            
            $increase = $posSubCategory->increase;

            $productIds = $posSubCategory->products()->pluck('id')->chunk(50);
            $productIds->each(function ($ids) use ($increase) {
                dispatch(function () use ($ids, $increase) { 
                    \App\Product::find($ids)
                        ->each(function ($product) use ($increase) {
                            if($increase) {
                                $data = [
                                    'percent' => $increase + 100,
                                    'based_on' => 'store_price',
                                ];
                                $product->onlinePrice()->updateOrCreate([], $data);
                            } else {
                                $product->onlinePrice ? $product->onlinePrice->delete() : null;
                            }

                            $product->updateSearchFeild();
                            $product->refreshCache();
                        });
                });
            });

            $variationIds = $posSubCategory->variations()->pluck('id')->chunk(50);
            $variationIds
                ->each(function ($ids) use ($increase) {
                    dispatch(function () use ($ids, $increase) { 
                        \App\VariationInfo::find($ids)
                            ->each(function ($variation) use ($increase){
                                if($increase) {
                                    $data = [
                                        'percent' => $increase + 100,
                                        'based_on' => 'store_price',
                                    ];
                                    $variation->onlinePrice()->updateOrCreate([], $data);
                                } else {
                                    $variation->onlinePrice ? $variation->onlinePrice->delete() : null;
                                }
                                $variation->updateProductJson();
                                $variation->product->updateSearchFeild();
                                $variation->product->refreshCache();
                            });
                    });
                });
        });
    }
}
