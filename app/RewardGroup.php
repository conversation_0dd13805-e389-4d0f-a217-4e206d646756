<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class RewardGroup extends Model
{
    public static function getHighestStatus($points, $currentStatus)
    {
        if ($currentStatus == 'Blue') {
            return static::where('amount', '<', $points)->orderBy('amount', 'desc')->first()['name'] ?? $currentStatus;
        }
        $rewardGroups = static::all();
        $newGroup = ['name' => 'Blue', 'amount' => null];
        $pointBracket = null;
        $currentBracket = null;
        foreach ($rewardGroups as $group) {
            if ($group['amount'] <= $points && $group['amount'] > $pointBracket) { //this will look for the highest bracket that his points can earn him.
                $pointBracket = $group['amount'];
                $newGroup = $group;
            }
            if ($group['name'] == $currentStatus) { // this will find the bracket that he's in currently.
                $currentBracket = $group['amount'];
            }
        }
        if ($newGroup['name'] == $currentStatus) {
            return $currentStatus;
        }
        return ($currentBracket != $pointBracket) ? $newGroup['name'] : $currentStatus;
    }

    public static function getReward($status)
    {
        return static::where('name', '=', $status)->first();
    }
}
