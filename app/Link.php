<?php

namespace App;

use App\Observers\ProductObserver;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Model;

class Link extends Model
{
    public function filter()
    {
        return $this->belongsTo(Filter::class);
    }

    public function linkItems()
    {
        return $this->hasMany(LinkItem::class);
    }

    public function paths($id = null)
    {
        if (!$this->filter) {
            return [];
        }
        return [
            $this->filter->name => $this->linkItems->sortBy('order')
                ->map(function ($item) use ($id) {
                    if ($model = $item->model) {
                        return [
                            'name' => optional($model->filters->where('filter_id', $this->filter->id)->first())->name,
                            'path' => $model->path,
                            'price' => $model->price,
                            'current' => $model->id == $id,
                        ];
                    }
                })->filter()->values()
        ];
    }

    public function setFrontEndItemsAttribute()
    {
        return;
    }

    public function getFrontEndItemsAttribute()
    {
        if (!$this->filter) {
            return [];
        }
        return $this->linkItems->map(function ($linkItem) {
            $model = $linkItem->model;
            if (!$model) {
                return;
            }
            return [
                'id' => $model->id,
                'title' => $model->title,
                // 'price' => $model->toArray()['price'],
                'media' => $model->search['image'],
                'sku' => $model->sku,
                'difference' => optional($model->filters->where('filter_id', $this->filter->id)->first())->name,
                'used' => $model
                    ->morphMany(LinkItem::class, 'model')
                    ->get()
                    ->where('link_id', '!=', $this->id)
                    ->map(function ($link) {
                        return optional($link->link)->name;
                    })->values()

            ];
        });
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($link) {
            $request = request();
            if ($request->front_end_items) {
                $items = collect(json_decode($request->front_end_items))
                    ->map(function ($item, $index) {
                        return LinkItem::make([
                            'model_type' => 'App\\Product',
                            'model_id' => $item->id,
                            'order' => $index,
                        ]);
                    });
                $newItems = $items->diff($link->linkItems);
                $productObserver = new ProductObserver();
                foreach ($newItems as $newItem) {
                    $product = Product::find($newItem->id);
                    $productObserver->sendNotifications($product);
                }
                $link->linkItems()->delete();
                $link->linkItems()->saveMany($items);
            }
        });
        static::deleted(function ($link) {
            $link->linkItems()->delete();
        });
    }
}
