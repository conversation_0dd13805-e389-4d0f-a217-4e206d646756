<?php

namespace App;

use App\Jobs\Miscellaneous;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;
use Illuminate\Support\Facades\Cache;

class Filter extends Model implements Sortable
{
    use SortableTrait;

    public $sortable = [
        'order_column_name' => 'sort_order',
        'sort_when_creating' => true,
    ];

    protected $casts = [
        'options' => 'array',
        'show' => 'boolean',
        'assistant' => 'boolean',
    ];

    public function items()
    {
        return $this->hasMany(FilterItem::class);
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class);
    }

    public function products()
    {
        return $this->items->map->products->flatten(1);
    }

    public function refreshCache($filter)
    {
        $filter->products()->each->refreshCache();
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($filter) {
            if ($filter->isDirty(['name', 'show', 'sort_order', 'assistant', 'info'])) {
                $ids = DB::table('filter_items')
                    ->where('filter_id', $filter->id)
                    ->pluck('id');
                $ids = DB::table('filter_item_product')
                    ->whereIn('filter_item_id', $ids)
                    ->pluck('product_id');

                $ids->chunk(50)->map(function ($ids) {
                    dispatch(function () use ($ids) {
                        Product::find($ids)->map->updateSearchFeild();
                    });
                });
                Miscellaneous::dispatch(Filter::class, 'refreshCache', $filter);
                Cache::forget('filterSorts');
            }
            refreshCache();
        });

        static::deleted(function ($filter) {
            $filter->items()->delete();
            refreshCache();
            Cache::forget('filterSorts');
        });
    }
}
