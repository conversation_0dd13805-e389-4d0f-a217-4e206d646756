<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class ShippingOption extends Model
{
    protected $guarded = [];

    public function fulfillmentSchematic()
    {
        return $this->hasOne(FulfillmentSchematic::class, 'delivery', 'delivery');
    }

    public function closedDays()
    {
        return $this->belongsToMany(ClosedDay::class);
    }

    public function shippingZones()
    {
        return $this->belongsToMany(ShippingZone::class);
    }

    public function internationalShippingZones()
    {
        return $this->belongsToMany('App\InternationalShippingZone', 'international_shipping_option_shipping_zone');
    }

    public function getZipCodes()
    {
        return $this->shippingZones->map(function ($zone) {
            return $zone->zipCodes->pluck('zip_code');
        })->flatten();
    }

    public function discounts()
    {
        return $this->belongsToMany(Discount::class);
    }

    public function zones()
    {
        return $this->hasMany(ShippingOptionShippingZone::class);
    }
}
