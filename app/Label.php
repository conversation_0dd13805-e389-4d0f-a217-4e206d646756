<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\Jobs\Miscellaneous;

class Label extends Model
{

    public function refreshCache($label)
    {
        $label->products->each->refreshCache();
    }

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($label) {
            if ($label->getOriginal('name') != $label->name) {
                $label->products->map->updateSearchFeild();
                Miscellaneous::dispatch(Label::class, 'refreshCache', $label);
            }
            refreshCache();
        });

        static::deleted(function ($label) {
            refreshCache();
        });
    }
}
