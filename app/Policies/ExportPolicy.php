<?php

namespace App\Policies;

use App\Admin;
use App\Export;
use Illuminate\Auth\Access\HandlesAuthorization;

class ExportPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the admin can view any exports.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function viewAny(Admin $admin)
    {
        return true;
    }

    /**
     * Determine whether the admin can view the export.
     *
     * @param Admin $admin
     * @param Export $export
     * @return mixed
     */
    public function view(Admin $admin, Export $export)
    {
        return true;
    }

    /**
     * Determine whether the admin can create exports.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function create(Admin $admin)
    {
        return false;
    }

    /**
     * Determine whether the admin can update the export.
     *
     * @param Admin $admin
     * @param Export $export
     * @return mixed
     */
    public function update(Admin $admin, Export $export)
    {
        return true;
    }

    /**
     * Determine whether the admin can delete the export.
     *
     * @param Admin $admin
     * @param Export $export
     * @return mixed
     */
    public function delete(Admin $admin, Export $export)
    {
        return true;
    }

    /**
     * Determine whether the admin can restore the export.
     *
     * @param Admin $admin
     * @param Export $export
     * @return mixed
     */
    public function restore(Admin $admin, Export $export)
    {
        //
    }

    /**
     * Determine whether the admin can permanently delete the export.
     *
     * @param Admin $admin
     * @param Export $export
     * @return mixed
     */
    public function forceDelete(Admin $admin, Export $export)
    {
        //
    }
}
