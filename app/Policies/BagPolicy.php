<?php

namespace App\Policies;

use App\Bag;
use App\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;

class BagPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any bags.
     *
     * @param Admin $user
     * @return mixed
     */
    public function viewAny(Admin $user)
    {
        return $user->role != 'Product Lister';
    }

    /**
     * Determine whether the user can view the bag.
     *
     * @param Admin $user
     * @param Bag $bag
     * @return mixed
     */
    public function view(Admin $user, Bag $bag)
    {
        return true;
    }

    /**
     * Determine whether the user can create bags.
     *
     * @param Admin $user
     * @return mixed
     */
    public function create(Admin $user)
    {
        return false;
    }

    /**
     * Determine whether the user can update the bag.
     *
     * @param Admin $user
     * @param Bag $bag
     * @return mixed
     */
    public function update(Admin $user, Bag $bag)
    {
        return false;
    }

    /**
     * Determine whether the user can delete the bag.
     *
     * @param Admin $user
     * @param Bag $bag
     * @return mixed
     */
    public function delete(Admin $user, Bag $bag)
    {
        return false;
    }

    /**
     * Determine whether the user can restore the bag.
     *
     * @param Admin $user
     * @param Bag $bag
     * @return mixed
     */
    public function restore(Admin $user, Bag $bag)
    {
        return true;
    }

    /**
     * Determine whether the user can permanently delete the bag.
     *
     * @param Admin $user
     * @param Bag $bag
     * @return mixed
     */
    public function forceDelete(Admin $user, Bag $bag)
    {
        return true;
    }
}
