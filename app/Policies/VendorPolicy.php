<?php

namespace App\Policies;

use App\Admin;
use App\VendorType;
use Illuminate\Auth\Access\HandlesAuthorization;

class VendorPolicy
{
    use HandlesAuthorization;

    public function before($admin, $ability)
    {
        if ($admin->role === "Super Admin" || $admin->role === "Product Lister") {
            return true;
        }
    }

    /**
     * Determine whether the admin can view any vendor types.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function viewAny(Admin $admin)
    {
        if ($admin->role === "Order Fulfiller") {
            return true;
        }
    }

    /**
     * Determine whether the admin can view the vendor type.
     *
     * @param Admin $admin
     * @param VendorType $vendorType
     * @return mixed
     */
    public function view(Admin $admin, VendorType $vendorType)
    {
        if ($admin->role === "Order Fulfiller") {
            return true;
        }
    }

    /**
     * Determine whether the admin can create vendor types.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function create(Admin $admin)
    {
        //
    }

    /**
     * Determine whether the admin can update the vendor type.
     *
     * @param Admin $admin
     * @param VendorType $vendorType
     * @return mixed
     */
    public function update(Admin $admin, VendorType $vendorType)
    {
        //
    }

    /**
     * Determine whether the admin can delete the vendor type.
     *
     * @param Admin $admin
     * @param VendorType $vendorType
     * @return mixed
     */
    public function delete(Admin $admin, VendorType $vendorType)
    {
        //
    }

    /**
     * Determine whether the admin can restore the vendor type.
     *
     * @param Admin $admin
     * @param VendorType $vendorType
     * @return mixed
     */
    public function restore(Admin $admin, VendorType $vendorType)
    {
        //
    }

    /**
     * Determine whether the admin can permanently delete the vendor type.
     *
     * @param Admin $admin
     * @param VendorType $vendorType
     * @return mixed
     */
    public function forceDelete(Admin $admin, VendorType $vendorType)
    {
        //
    }
}
