<?php

namespace App\Policies;

use App\Admin;
use App\GiftNote;
use Illuminate\Auth\Access\HandlesAuthorization;

class GiftNotePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any gift notes.
     *
     * @param Admin $user
     * @return mixed
     */
    public function viewAny(Admin $user)
    {
        return true;
    }

    /**
     * Determine whether the user can view the gift note.
     *
     * @param Admin $user
     * @param GiftNote $giftNote
     * @return mixed
     */
    public function view(Admin $user, GiftNote $giftNote)
    {
        return true;
    }

    /**
     * Determine whether the user can create gift notes.
     *
     * @param Admin $user
     * @return mixed
     */
    public function create(Admin $user)
    {
        return true;
    }

    /**
     * Determine whether the user can update the gift note.
     *
     * @param Admin $user
     * @param GiftNote $giftNote
     * @return mixed
     */
    public function update(Admin $user, GiftNote $giftNote)
    {
        return true;
    }

    /**
     * Determine whether the user can delete the gift note.
     *
     * @param Admin $user
     * @param GiftNote $giftNote
     * @return mixed
     */
    public function delete(Admin $user, GiftNote $giftNote)
    {
        return true;
    }

    /**
     * Determine whether the user can restore the gift note.
     *
     * @param Admin $user
     * @param GiftNote $giftNote
     * @return mixed
     */
    public function restore(Admin $user, GiftNote $giftNote)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the gift note.
     *
     * @param Admin $user
     * @param GiftNote $giftNote
     * @return mixed
     */
    public function forceDelete(Admin $user, GiftNote $giftNote)
    {
        //
    }
}
