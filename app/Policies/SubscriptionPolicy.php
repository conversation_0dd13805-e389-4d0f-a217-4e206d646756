<?php

namespace App\Policies;

use App\Admin;
use App\Subscription;
use Illuminate\Auth\Access\HandlesAuthorization;

class SubscriptionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any subscriptions.
     *
     * @param Admin $user
     * @return mixed
     */
    public function viewAny(Admin $user)
    {
        return true;
    }

    /**
     * Determine whether the user can view the subscription.
     *
     * @param Admin $user
     * @param Subscription $subscription
     * @return mixed
     */
    public function view(Admin $user, Subscription $subscription)
    {
        return true;
    }

    /**
     * Determine whether the user can create subscriptions.
     *
     * @param Admin $user
     * @return mixed
     */
    public function create(Admin $user)
    {
        return false;
    }

    /**
     * Determine whether the user can update the subscription.
     *
     * @param Admin $user
     * @param Subscription $subscription
     * @return mixed
     */
    public function update(Admin $user, Subscription $subscription)
    {
        return true;
    }

    /**
     * Determine whether the user can delete the subscription.
     *
     * @param Admin $user
     * @param Subscription $subscription
     * @return mixed
     */
    public function delete(Admin $user, Subscription $subscription)
    {
        return false;
    }

    /**
     * Determine whether the user can restore the subscription.
     *
     * @param Admin $user
     * @param Subscription $subscription
     * @return mixed
     */
    public function restore(Admin $user, Subscription $subscription)
    {
        return true;
    }

    /**
     * Determine whether the user can permanently delete the subscription.
     *
     * @param Admin $user
     * @param Subscription $subscription
     * @return mixed
     */
    public function forceDelete(Admin $user, Subscription $subscription)
    {
        return true;
    }
}
