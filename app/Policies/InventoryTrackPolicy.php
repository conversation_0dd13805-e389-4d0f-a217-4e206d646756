<?php

namespace App\Policies;

use App\Admin;
use App\InventoryTrack;
use Illuminate\Auth\Access\HandlesAuthorization;

class InventoryTrackPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any inventory tracks.
     *
     * @param Admin $user
     * @return mixed
     */
    public function viewAny(Admin $user)
    {
        return true;
    }

    /**
     * Determine whether the user can view the inventory track.
     *
     * @param Admin $user
     * @param InventoryTrack $inventoryTrack
     * @return mixed
     */
    public function view(Admin $user, InventoryTrack $inventoryTrack)
    {
        //
    }

    /**
     * Determine whether the user can create inventory tracks.
     *
     * @param Admin $user
     * @return mixed
     */
    public function create(Admin $user)
    {
        //
    }

    /**
     * Determine whether the user can update the inventory track.
     *
     * @param Admin $user
     * @param InventoryTrack $inventoryTrack
     * @return mixed
     */
    public function update(Admin $user, InventoryTrack $inventoryTrack)
    {
        //
    }

    /**
     * Determine whether the user can delete the inventory track.
     *
     * @param Admin $user
     * @param InventoryTrack $inventoryTrack
     * @return mixed
     */
    public function delete(Admin $user, InventoryTrack $inventoryTrack)
    {
        //
    }

    /**
     * Determine whether the user can restore the inventory track.
     *
     * @param Admin $user
     * @param InventoryTrack $inventoryTrack
     * @return mixed
     */
    public function restore(Admin $user, InventoryTrack $inventoryTrack)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the inventory track.
     *
     * @param Admin $user
     * @param InventoryTrack $inventoryTrack
     * @return mixed
     */
    public function forceDelete(Admin $user, InventoryTrack $inventoryTrack)
    {
        //
    }
}
