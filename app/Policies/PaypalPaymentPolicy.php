<?php

namespace App\Policies;

use App\Admin;
use App\PaypalPayment;
use Illuminate\Auth\Access\HandlesAuthorization;

class PaypalPaymentPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any paypal payments.
     *
     * @param Admin $user
     * @return mixed
     */
    public function viewAny(Admin $user)
    {
        return true;
    }

    /**
     * Determine whether the user can view the paypal payment.
     *
     * @param Admin $user
     * @param PaypalPayment $paypalPayment
     * @return mixed
     */
    public function view(Admin $user, PaypalPayment $paypalPayment)
    {
        return true;
    }

    /**
     * Determine whether the user can create paypal payments.
     *
     * @param Admin $user
     * @return mixed
     */
    public function create(Admin $user)
    {
        //
    }

    /**
     * Determine whether the user can update the paypal payment.
     *
     * @param Admin $user
     * @param PaypalPayment $paypalPayment
     * @return mixed
     */
    public function update(Admin $user, PaypalPayment $paypalPayment)
    {
        //
    }

    /**
     * Determine whether the user can delete the paypal payment.
     *
     * @param Admin $user
     * @param PaypalPayment $paypalPayment
     * @return mixed
     */
    public function delete(Admin $user, PaypalPayment $paypalPayment)
    {
        //
    }

    /**
     * Determine whether the user can restore the paypal payment.
     *
     * @param Admin $user
     * @param PaypalPayment $paypalPayment
     * @return mixed
     */
    public function restore(Admin $user, PaypalPayment $paypalPayment)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the paypal payment.
     *
     * @param Admin $user
     * @param PaypalPayment $paypalPayment
     * @return mixed
     */
    public function forceDelete(Admin $user, PaypalPayment $paypalPayment)
    {
        //
    }
}
