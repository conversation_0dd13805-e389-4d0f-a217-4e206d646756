<?php

namespace App\Policies;

use App\Admin;
use App\Returns;
use Illuminate\Auth\Access\HandlesAuthorization;

class ReturnsPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any returns.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function viewAny(Admin $admin)
    {
        $role = $admin->role;
        return $role == 'Super Admin' || $role == 'Return Fulfiller' || $role == 'Order Fulfiller';
    }

    /**
     * Determine whether the user can view the return.
     *
     * @param Admin $admin
     * @param Returns $return
     * @return mixed
     */
    public function view(Admin $admin, Returns $return)
    {
        return false;
    }

    /**
     * Determine whether the user can create returns.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function create(Admin $admin)
    {
        //
    }

    /**
     * Determine whether the user can update the return.
     *
     * @param Admin $admin
     * @param Returns $return
     * @return mixed
     */
    public function update(Admin $admin, Returns $return)
    {
        $role = $admin->role;
        return $role == 'Super Admin' || $role == 'Return Fulfiller' || $role == 'Order Fulfiller';
    }

    /**
     * Determine whether the user can delete the return.
     *
     * @param Admin $admin
     * @param Returns $return
     * @return mixed
     */
    public function delete(Admin $admin, Returns $return)
    {
        return false;
    }

    /**
     * Determine whether the user can restore the return.
     *
     * @param Admin $admin
     * @param Returns $return
     * @return mixed
     */
    public function restore(Admin $admin, Returns $return)
    {
        return true;
    }

    /**
     * Determine whether the user can permanently delete the return.
     *
     * @param Admin $admin
     * @param Returns $return
     * @return mixed
     */
    public function forceDelete(Admin $admin, Returns $return)
    {
        //
    }
}
