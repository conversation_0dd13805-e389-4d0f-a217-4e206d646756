<?php

namespace App\Policies;

use App\Admin;
use App\ProductType;
use Illuminate\Auth\Access\HandlesAuthorization;

class ProductTypePolicy
{
    use HandlesAuthorization;

    public function before($admin, $ability)
    {
        if ($admin->role === "Super Admin" || $admin->role === "Product Lister") {
            return true;
        }
    }

    /**
     * Determine whether the admin can view any product types.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function viewAny(Admin $admin)
    {
        if ($admin->role === "Order Fulfiller") {
            return true;
        }
    }

    /**
     * Determine whether the admin can view the product type.
     *
     * @param Admin $admin
     * @param ProductType $productType
     * @return mixed
     */
    public function view(Admin $admin, ProductType $productType)
    {
        if ($admin->role === "Order Fulfiller") {
            return true;
        }
    }

    /**
     * Determine whether the admin can create product types.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function create(Admin $admin)
    {
        //
    }

    /**
     * Determine whether the admin can update the product type.
     *
     * @param Admin $admin
     * @param ProductType $productType
     * @return mixed
     */
    public function update(Admin $admin, ProductType $productType)
    {
        //
    }

    /**
     * Determine whether the admin can delete the product type.
     *
     * @param Admin $admin
     * @param ProductType $productType
     * @return mixed
     */
    public function delete(Admin $admin, ProductType $productType)
    {
        //
    }

    /**
     * Determine whether the admin can restore the product type.
     *
     * @param Admin $admin
     * @param ProductType $productType
     * @return mixed
     */
    public function restore(Admin $admin, ProductType $productType)
    {
        //
    }

    /**
     * Determine whether the admin can permanently delete the product type.
     *
     * @param Admin $admin
     * @param ProductType $productType
     * @return mixed
     */
    public function forceDelete(Admin $admin, ProductType $productType)
    {
        //
    }
}
