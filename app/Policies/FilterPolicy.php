<?php

namespace App\Policies;

use App\Admin;
use App\Filter;
use Illuminate\Auth\Access\HandlesAuthorization;

class FilterPolicy
{
    use HandlesAuthorization;

    public function before($admin, $ability)
    {
        if ($admin->role === "Super Admin" || $admin->role === "Product Lister") {
            return true;
        }
    }

    /**
     * Determine whether the admin can view any filters.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function viewAny(Admin $admin)
    {
        if ($admin->role === "Order Fulfiller") {
            return true;
        }
    }

    /**
     * Determine whether the admin can view the filter.
     *
     * @param Admin $admin
     * @param Filter $filter
     * @return mixed
     */
    public function view(Admin $admin, Filter $filter)
    {
        if ($admin->role === "Order Fulfiller") {
            return true;
        }
    }

    /**
     * Determine whether the admin can create filters.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function create(Admin $admin)
    {
    }

    /**
     * Determine whether the admin can update the filter.
     *
     * @param Admin $admin
     * @param Filter $filter
     * @return mixed
     */
    public function update(Admin $admin, Filter $filter)
    {
        //
    }

    /**
     * Determine whether the admin can delete the filter.
     *
     * @param Admin $admin
     * @param Filter $filter
     * @return mixed
     */
    public function delete(Admin $admin, Filter $filter)
    {
        //
    }

    /**
     * Determine whether the admin can restore the filter.
     *
     * @param Admin $admin
     * @param Filter $filter
     * @return mixed
     */
    public function restore(Admin $admin, Filter $filter)
    {
        //
    }

    /**
     * Determine whether the admin can permanently delete the filter.
     *
     * @param Admin $admin
     * @param Filter $filter
     * @return mixed
     */
    public function forceDelete(Admin $admin, Filter $filter)
    {
        //
    }
}
