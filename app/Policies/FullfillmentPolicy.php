<?php

namespace App\Policies;

use App\Admin as Customer;
use App\FulfillmentSchematic;
use Illuminate\Auth\Access\HandlesAuthorization;

class FullfillmentPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any fulfillment schematics.
     *
     * @param \App\Customer $user
     * @return mixed
     */
    public function viewAny(Customer $user)
    {
        return true;
    }

    /**
     * Determine whether the user can view the fulfillment schematic.
     *
     * @param \App\Customer $user
     * @param FulfillmentSchematic $fulfillmentSchematic
     * @return mixed
     */
    public function view(Customer $user, FulfillmentSchematic $fulfillmentSchematic)
    {
        return true;
    }

    /**
     * Determine whether the user can create fulfillment schematics.
     *
     * @param \App\Customer $user
     * @return mixed
     */
    public function create(Customer $user)
    {
        //
    }

    /**
     * Determine whether the user can update the fulfillment schematic.
     *
     * @param \App\Customer $user
     * @param FulfillmentSchematic $fulfillmentSchematic
     * @return mixed
     */
    public function update(Customer $user, FulfillmentSchematic $fulfillmentSchematic)
    {
        return true;
    }

    /**
     * Determine whether the user can delete the fulfillment schematic.
     *
     * @param \App\Customer $user
     * @param FulfillmentSchematic $fulfillmentSchematic
     * @return mixed
     */
    public function delete(Customer $user, FulfillmentSchematic $fulfillmentSchematic)
    {
        //
    }

    /**
     * Determine whether the user can restore the fulfillment schematic.
     *
     * @param \App\Customer $user
     * @param FulfillmentSchematic $fulfillmentSchematic
     * @return mixed
     */
    public function restore(Customer $user, FulfillmentSchematic $fulfillmentSchematic)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the fulfillment schematic.
     *
     * @param \App\Customer $user
     * @param FulfillmentSchematic $fulfillmentSchematic
     * @return mixed
     */
    public function forceDelete(Customer $user, FulfillmentSchematic $fulfillmentSchematic)
    {
        //
    }
}
