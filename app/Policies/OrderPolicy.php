<?php

namespace App\Policies;

use App\Admin;
use App\Order;
use Illuminate\Auth\Access\HandlesAuthorization;

class OrderPolicy
{
    use HandlesAuthorization;

    public function _before($admin, $ability)
    {
        $role = $admin->role;
        return $role == 'Super Admin' || $role == 'Return Fulfiller' || $role == 'Order Fulfiller';
    }

    /**
     * Determine whether the admin can view any orders.
     *
     *
     * @param Admin $admin
     * @return mixed
     */
    public function viewAny(Admin $admin)
    {
        $role = $admin->role;
        return $role == 'Super Admin' || $role == 'Return Fulfiller' || $role == 'Order Fulfiller';
    }

    /**
     * Determine whether the admin can view the order.
     *
     * @param Admin $admin
     * @param Order $order
     * @return mixed
     */
    public function view(Admin $admin, Order $order)
    {
        return false;
    }

    /**
     * Determine whether the admin can create orders.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function create(Admin $admin)
    {
        return false;
    }

    /**
     * Determine whether the admin can update the order.
     *
     * @param Admin $admin
     * @param Order $order
     * @return mixed
     */
    public function update(Admin $admin, Order $order)
    {
        $role = $admin->role;
        return $role == 'Super Admin' || $role == 'Return Fulfiller' || $role == 'Order Fulfiller';
    }

    /**
     * Determine whether the admin can delete the order.
     *
     * @param Admin $admin
     * @param Order $order
     * @return mixed
     */
    public function delete(Admin $admin, Order $order)
    {
        return false;
    }

    /**
     * Determine whether the admin can restore the order.
     *
     * @param Admin $admin
     * @param Order $order
     * @return mixed
     */
    public function restore(Admin $admin, Order $order)
    {
        //
    }

    /**
     * Determine whether the admin can permanently delete the order.
     *
     * @param Admin $admin
     * @param Order $order
     * @return mixed
     */
    public function forceDelete(Admin $admin, Order $order)
    {
        //
    }
}
