<?php

namespace App\Policies;

use App\Admin;
use App\Order;
use App\Product;
use Illuminate\Auth\Access\HandlesAuthorization;

class ProductPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    public function _before($admin, $ability)
    {
        return true;
    }

    public function viewAny(Admin $admin)
    {
        return true;
    }

    public function view(Admin $admin, Product $product)
    {
        return true;
    }

    /**
     * Determine whether the admin can create orders.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function create(Admin $admin)
    {
        return true;
    }

    /**
     * Determine whether the admin can update the order.
     *
     * @param Admin $admin
     * @param Order $order
     * @return mixed
     */
    public function update(Admin $admin, Product $product)
    {
        return true;
    }

    /**
     * Determine whether the admin can delete the order.
     *
     * @param Admin $admin
     * @param Order $order
     * @return mixed
     */
    public function delete(Admin $admin, Order $order)
    {
        return true;
    }
}
