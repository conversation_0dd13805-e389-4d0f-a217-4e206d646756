<?php

namespace App\Policies;

use App\Admin;
use App\Setting;
use Illuminate\Auth\Access\HandlesAuthorization;

class SettingsPolicy
{
    use HandlesAuthorization;


    /**
     * Determine whether the user can view any settings.
     *
     * @param Admin $user
     * @return mixed
     */
    public function viewAny(Admin $user)
    {
        return true;
    }

    /**
     * Determine whether the user can view the setting.
     *
     * @param Admin $user
     * @param Setting $setting
     * @return mixed
     */
    public function view(Admin $user, Setting $setting)
    {
        // return !$setting->admin;
        return true;
    }

    /**
     * Determine whether the user can create settings.
     *
     * @param Admin $user
     * @return mixed
     */
    public function create(Admin $user)
    {
        return false;
    }

    /**
     * Determine whether the user can update the setting.
     *
     * @param Admin $user
     * @param Setting $setting
     * @return mixed
     */
    public function update(Admin $user, Setting $setting)
    {
        return !$setting->admin;
        return true;
    }

    /**
     * Determine whether the user can delete the setting.
     *
     * @param Admin $user
     * @param Setting $setting
     * @return mixed
     */
    public function delete(Admin $user, Setting $setting)
    {
        return false;
    }

    /**
     * Determine whether the user can restore the setting.
     *
     * @param Admin $user
     * @param Setting $setting
     * @return mixed
     */
    public function restore(Admin $user, Setting $setting)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the setting.
     *
     * @param Admin $user
     * @param Setting $setting
     * @return mixed
     */
    public function forceDelete(Admin $user, Setting $setting)
    {
        //
    }
}
