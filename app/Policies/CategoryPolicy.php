<?php

namespace App\Policies;

use App\Admin;
use App\Category;
use Illuminate\Auth\Access\HandlesAuthorization;

class CategoryPolicy
{
    use HandlesAuthorization;

    public function before($admin, $ability)
    {
        if ($admin->role === "Super Admin" || $admin->role === "Product Lister") {
            return true;
        }
    }

    /**
     * Determine whether the admin can view any categories.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function viewAny(Admin $admin)
    {
        if ($admin->role === "Order Fulfiller") {
            return true;
        }
    }

    /**
     * Determine whether the admin can view the category.
     *
     * @param Admin $admin
     * @param Category $category
     * @return mixed
     */
    public function view(Admin $admin, Category $category)
    {
        if ($admin->role === "Order Fulfiller") {
            return true;
        }
    }

    /**
     * Determine whether the admin can create categories.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function create(Admin $admin)
    {
        //
    }

    /**
     * Determine whether the admin can update the category.
     *
     * @param Admin $admin
     * @param Category $category
     * @return mixed
     */
    public function update(Admin $admin, Category $category)
    {
        //
    }

    /**
     * Determine whether the admin can delete the category.
     *
     * @param Admin $admin
     * @param Category $category
     * @return mixed
     */
    public function delete(Admin $admin, Category $category)
    {
        //
    }

    /**
     * Determine whether the admin can restore the category.
     *
     * @param Admin $admin
     * @param Category $category
     * @return mixed
     */
    public function restore(Admin $admin, Category $category)
    {
        //
    }

    /**
     * Determine whether the admin can permanently delete the category.
     *
     * @param Admin $admin
     * @param Category $category
     * @return mixed
     */
    public function forceDelete(Admin $admin, Category $category)
    {
        //
    }
}
