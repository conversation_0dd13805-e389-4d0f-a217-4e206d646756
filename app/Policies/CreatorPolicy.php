<?php

namespace App\Policies;

use App\Admin;
use App\Creator;
use Illuminate\Auth\Access\HandlesAuthorization;

class CreatorPolicy
{
    use HandlesAuthorization;

    public function before($admin, $ability)
    {
        if ($admin->role === "Super Admin" || $admin->role === "Product Lister") {
            return true;
        }
    }

    /**
     * Determine whether the admin can view any creators.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function viewAny(Admin $admin)
    {
        if ($admin->role === "Order Fulfiller") {
            return true;
        }
    }

    /**
     * Determine whether the admin can view the creator.
     *
     * @param Admin $admin
     * @param Creator $creator
     * @return mixed
     */
    public function view(Admin $admin, Creator $creator)
    {
        if ($admin->role === "Order Fulfiller") {
            return true;
        }
    }

    /**
     * Determine whether the admin can create creators.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function create(Admin $admin)
    {
        //
    }

    /**
     * Determine whether the admin can update the creator.
     *
     * @param Admin $admin
     * @param Creator $creator
     * @return mixed
     */
    public function update(Admin $admin, Creator $creator)
    {
        //
    }

    /**
     * Determine whether the admin can delete the creator.
     *
     * @param Admin $admin
     * @param Creator $creator
     * @return mixed
     */
    public function delete(Admin $admin, Creator $creator)
    {
        //
    }

    /**
     * Determine whether the admin can restore the creator.
     *
     * @param Admin $admin
     * @param Creator $creator
     * @return mixed
     */
    public function restore(Admin $admin, Creator $creator)
    {
        //
    }

    /**
     * Determine whether the admin can permanently delete the creator.
     *
     * @param Admin $admin
     * @param Creator $creator
     * @return mixed
     */
    public function forceDelete(Admin $admin, Creator $creator)
    {
        //
    }
}
