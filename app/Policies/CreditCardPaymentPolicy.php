<?php

namespace App\Policies;

use App\CreditCardPayment;
use App\Admin;
use Illuminate\Auth\Access\HandlesAuthorization;

class CreditCardPaymentPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any credit card payments.
     *
     * @param Admin $user
     * @return mixed
     */
    public function viewAny(Admin $user)
    {
        return true;
    }

    /**
     * Determine whether the user can view the credit card payment.
     *
     * @param Admin $user
     * @param CreditCardPayment $creditCardPayment
     * @return mixed
     */
    public function view(Admin $user, CreditCardPayment $creditCardPayment)
    {
        return true;
    }

    /**
     * Determine whether the user can create credit card payments.
     *
     * @param Admin $user
     * @return mixed
     */
    public function create(Admin $user)
    {
        //
    }

    /**
     * Determine whether the user can update the credit card payment.
     *
     * @param Admin $user
     * @param CreditCardPayment $creditCardPayment
     * @return mixed
     */
    public function update(Admin $user, CreditCardPayment $creditCardPayment)
    {
        //
    }

    /**
     * Determine whether the user can delete the credit card payment.
     *
     * @param Admin $user
     * @param CreditCardPayment $creditCardPayment
     * @return mixed
     */
    public function delete(Admin $user, CreditCardPayment $creditCardPayment)
    {
        //
    }

    /**
     * Determine whether the user can restore the credit card payment.
     *
     * @param Admin $user
     * @param CreditCardPayment $creditCardPayment
     * @return mixed
     */
    public function restore(Admin $user, CreditCardPayment $creditCardPayment)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the credit card payment.
     *
     * @param Admin $user
     * @param CreditCardPayment $creditCardPayment
     * @return mixed
     */
    public function forceDelete(Admin $user, CreditCardPayment $creditCardPayment)
    {
        //
    }
}
