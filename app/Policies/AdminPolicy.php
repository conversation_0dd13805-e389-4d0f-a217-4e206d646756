<?php

namespace App\Policies;

use App\Admin;
use App\AdminType;
use Illuminate\Auth\Access\HandlesAuthorization;

class AdminPolicy
{
    use HandlesAuthorization;

    public function before($admin, $ability)
    {
        if ($admin->role === "Super Admin") {
            return true;
        }
    }

    /**
     * Determine whether the admin can view any admin types.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function viewAny(Admin $admin)
    {
        //
    }

    /**
     * Determine whether the admin can view the admin type.
     *
     * @param Admin $admin
     * @param AdminType $adminType
     * @return mixed
     */
    public function view(Admin $admin, $user)
    {
        //
    }

    /**
     * Determine whether the admin can create admin types.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function create(Admin $admin)
    {
        //
    }

    /**
     * Determine whether the admin can update the admin type.
     *
     * @param Admin $admin
     * @param AdminType $adminType
     * @return mixed
     */
    public function update(Admin $admin, $user)
    {
        //
    }

    /**
     * Determine whether the admin can delete the admin type.
     *
     * @param Admin $admin
     * @param AdminType $adminType
     * @return mixed
     */
    public function delete(Admin $admin, AdminType $adminType)
    {
        //
    }

    /**
     * Determine whether the admin can restore the admin type.
     *
     * @param Admin $admin
     * @param AdminType $adminType
     * @return mixed
     */
    public function restore(Admin $admin, AdminType $adminType)
    {
        //
    }

    /**
     * Determine whether the admin can permanently delete the admin type.
     *
     * @param Admin $admin
     * @param AdminType $adminType
     * @return mixed
     */
    public function forceDelete(Admin $admin, AdminType $adminType)
    {
        //
    }

    public function uploadFiles(Admin $admin)
    {
        return true;
    }
}
