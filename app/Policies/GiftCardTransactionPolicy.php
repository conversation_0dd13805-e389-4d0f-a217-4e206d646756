<?php

namespace App\Policies;

use App\Admin;
use App\GiftCardTransaction;
use Illuminate\Auth\Access\HandlesAuthorization;

class GiftCardTransactionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any gift card transactions.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function viewAny(Admin $admin)
    {
        return true;
    }

    /**
     * Determine whether the user can view the gift card transaction.
     *
     * @param Admin $admin
     * @param GiftCardTransaction $giftCardTransaction
     * @return mixed
     */
    public function view(Admin $admin, GiftCardTransaction $giftCardTransaction)
    {
        return false;
    }

    /**
     * Determine whether the user can create gift card transactions.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function create(Admin $admin)
    {
        return false;
    }

    /**
     * Determine whether the user can update the gift card transaction.
     *
     * @param Admin $admin
     * @param GiftCardTransaction $giftCardTransaction
     * @return mixed
     */
    public function update(Admin $admin, GiftCardTransaction $giftCardTransaction)
    {
        return false;
    }

    /**
     * Determine whether the user can delete the gift card transaction.
     *
     * @param Admin $admin
     * @param GiftCardTransaction $giftCardTransaction
     * @return mixed
     */
    public function delete(Admin $admin, GiftCardTransaction $giftCardTransaction)
    {
        return false;
    }

    /**
     * Determine whether the user can restore the gift card transaction.
     *
     * @param Admin $admin
     * @param GiftCardTransaction $giftCardTransaction
     * @return mixed
     */
    public function restore(Admin $admin, GiftCardTransaction $giftCardTransaction)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the gift card transaction.
     *
     * @param Admin $admin
     * @param GiftCardTransaction $giftCardTransaction
     * @return mixed
     */
    public function forceDelete(Admin $admin, GiftCardTransaction $giftCardTransaction)
    {
        return false;
    }
}
