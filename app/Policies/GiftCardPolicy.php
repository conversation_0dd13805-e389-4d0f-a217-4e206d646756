<?php

namespace App\Policies;

use App\Admin;
use App\GiftCard;
use Illuminate\Auth\Access\HandlesAuthorization;

class GiftCardPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any gift cards.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function viewAny(Admin $admin)
    {
        return true;
    }

    /**
     * Determine whether the user can view the gift card.
     *
     * @param Admin $admin
     * @param GiftCard $giftCard
     * @return mixed
     */
    public function view(Admin $admin, GiftCard $giftCard)
    {
        return true;
    }

    /**
     * Determine whether the user can create gift cards.
     *
     * @param Admin $admin
     * @return mixed
     */
    public function create(Admin $admin)
    {
        return false;
    }

    /**
     * Determine whether the user can update the gift card.
     *
     * @param Admin $admin
     * @param GiftCard $giftCard
     * @return mixed
     */
    public function update(Admin $admin, GiftCard $giftCard)
    {
        return true;
    }

    /**
     * Determine whether the user can delete the gift card.
     *
     * @param Admin $admin
     * @param GiftCard $giftCard
     * @return mixed
     */
    public function delete(Admin $admin, GiftCard $giftCard)
    {
        return false;
    }

    /**
     * Determine whether the user can restore the gift card.
     *
     * @param Admin $admin
     * @param GiftCard $giftCard
     * @return mixed
     */
    public function restore(Admin $admin, GiftCard $giftCard)
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the gift card.
     *
     * @param Admin $admin
     * @param GiftCard $giftCard
     * @return mixed
     */
    public function forceDelete(Admin $admin, GiftCard $giftCard)
    {
        return false;
    }
}
