<?php

namespace App\Policies;

use App\Customer;
use App\Admin;

use Illuminate\Auth\Access\HandlesAuthorization;

class CustomerPolicy
{
    use HandlesAuthorization;

    // public function before($admin, $ability)
    // {
    //     if ($admin->role === "Super Admin" || $admin->role === "Fullfiller") {
    //         return true;
    //     }
    // }

    /**
     * Determine whether the user can view any customers.
     *
     * @param Customer $user
     * @return mixed
     */
    public function viewAny(Admin $admin)
    {
        return true;
    }

    /**
     * Determine whether the user can view the customer.
     *
     * @param Customer $customer
     * @return mixed
     */
    public function view(Admin $admin, Customer $customer)
    {
        return true;
    }

    /**
     * Determine whether the user can create customers.
     *
     * @param Customer $user
     * @return mixed
     */
    public function create(Admin $admin)
    {
        return true;
    }

    /**
     * Determine whether the user can update the customer.
     *
     * @param Customer $user
     * @param Customer $customer
     * @return mixed
     */
    public function update(Admin $admin, Customer $customer)
    {
        return true;
    }

    /**
     * Determine whether the user can delete the customer.
     *
     * @param Customer $user
     * @param Customer $customer
     * @return mixed
     */
    public function delete(Admin $admin, Customer $customer)
    {
        return false;
    }

    /**
     * Determine whether the user can restore the customer.
     *
     * @param Customer $user
     * @param Customer $customer
     * @return mixed
     */
    public function restore(Admin $admin, Customer $customer)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the customer.
     *
     * @param Customer $user
     * @param Customer $customer
     * @return mixed
     */
    public function forceDelete(Admin $admin, Customer $customer)
    {
        //
    }
}
