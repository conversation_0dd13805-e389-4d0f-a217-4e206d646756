<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Route4Me\Route4Me;

class Route extends Model
{
    public $guarded = [];

    public $casts = [
        'meta' => 'array'
    ];

    public function orders()
    {
        return $this->belongsToMany(Order::class);
    }

    public function orderRoutes()
    {
        return $this->hasMany(OrderRoute::class);
    }

    public function insertWebhook($data)
    {
        if (!data_get($this->meta, 'was_fetched') && data_get($data, 'activity_type') == 'route-started') {
            Route4Me::setApiKey('********************************');
            $route_for_me_route = new \Route4Me\Route();

            $routeResults = $route_for_me_route->getRoutes(['route_id' => $this->route_id]);

            collect(data_get(collect($routeResults), 'addresses'))->each(function ($address) {
                if ($order_route = OrderRoute::firstWhere(
                    'route_destination_id',
                    data_get($address, 'route_destination_id')
                )) {
                    $order_route->update([
                        'meta' => array_merge(
                            $order_route->meta ?? [],
                            [
                                'estimated_arrival' => now()->timestamp(
                                    data_get($address, 'manifest.estimated_arrival_time_ts')
                                )->toDateTimeString()
                            ]
                        ),
                    ]);
                    // $order = $order_route->order;
                    // $order->withoutEvents(function () use ($order, $address) {
                    //     $order->update([
                    //         'shipping' => array_merge($order->shipping ?? [], ['estimated_arrival' => now()->timestamp(data_get($address, 'manifest.estimated_arrival_time_ts'))->toDateTimeString()]),
                    //         shipping . shippingType . estimated_arrival
                    //     ]);
                    // });
                    $order_route->sendShippedEmail();
                }
            });

            $this->update([
                'meta' => array_merge($this->meta ?? [], ['was_fetched' => true])
            ]);
        }
    }
}
