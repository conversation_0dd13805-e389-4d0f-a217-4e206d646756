<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ClearShippingCache
{
    protected $response;

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $this->response = $next($request);

        $key = 'shipping_updated_at';

        $time = cache()->store('file')->get($key)
            ? cache()->store('file')->get($key)
            : null;

        if ($time && $time > now()->subDay()) {
            $this->response->headers->set($key, $time, false);
        }

        return $this->response;
    }
}
