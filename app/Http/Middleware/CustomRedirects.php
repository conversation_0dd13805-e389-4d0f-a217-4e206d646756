<?php

namespace App\Http\Middleware;

use App\Redirect;
use Closure;
use Illuminate\Http\Request;

class CustomRedirects
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if ($request->is('admin/*') || $request->is('nova-api/*') || $request->is('nova-vendor/*')) {
            return $next($request);
        }
        $ajax = $request->ajax();
        if ($ajax) {
            $url = $request->headers->get('referer');
        } else {
            $url = $request->url();
        }

        $redirect = Redirect::fromUrls()
            ->where('from', $url)
            ->first();

        if ($redirect) {
            if ((data_get($redirect, 'to_type')) == 'search_term') {
                if ($ajax) {
                    abort(302, '/search?q=' . (data_get($redirect, 'to')));
                } else {
                    return redirect('/search?q=' . (data_get($redirect, 'to')));
                }
            } elseif ((data_get($redirect, 'to_type')) == 'url') {
                if ($ajax) {
                    abort(302, '/' . (data_get($redirect, 'to')));
                } else {
                    return redirect('/' . data_get($redirect, 'to'));
                }
            }
        }


        if ($request->q) {
            $redirect = Redirect::getAllSearchRedirects()
                ->where('from', strtolower($request->q))
                ->first();
            if ($redirect) {
                if (data_get($redirect, 'to_type') == 'search_term') {
                    $request->q = data_get($redirect, 'to');
                } elseif (data_get($redirect, 'to_type') == 'url' && !request()->header('app')) {
                    if ($ajax) {
                        abort(302, data_get($redirect, 'to'));
                    } else {
                        return redirect(data_get($redirect, 'to'));
                    }
                }
            }
        }


        return $next($request);
    }
}
