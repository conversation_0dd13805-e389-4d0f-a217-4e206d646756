<?php

namespace App\Http\Controllers;

use Route4Me\Geocoding;
use Route4Me\Route4Me;

class RouteForMeController extends Controller
{
    public static function geocodeAddress($address)
    {
        Route4Me::setApi<PERSON>ey('********************************');

        $geocodingParameters = array(
            'strExportFormat' => 'json',
            "addresses" => $address
        );
        return data_get(forceArray((new Geocoding())->forwardGeocoding($geocodingParameters)), '0');
    }


    public function verifyAddress($order)
    {
        if (data_get($order, 'shipping.shippingType.name') == 'Local Delivery') {
            if ($this->addressString($order) == data_get($order->shipping, 'route_4_me_address.original')) {
                return;
            }
            $order->shipping = array_merge($order->shipping ?? [], [
                'route_4_me_address' => array_merge(
                    data_get($order->shipping, 'route_4_me_address') ?? [],
                    self::geocodeAddress($this->addressString($order))
                )
            ]);

            if (collect(explode(',', data_get($order, 'shipping.route_4_me_address.address')))->count() >= 4) {
                $route_4_me_address = array_merge(data_get($order->shipping, 'route_4_me_address'), ['verified' => true]
                );
                $order->shipping = array_merge(
                    $order->shipping ?? [],
                    ['route_4_me_address' => $route_4_me_address]
                );
            }
        }
        $order->withoutEvents(function () use ($order) {
            $order->save();
        });
    }


    public function addressString($order)
    {
        return data_get($order, 'shipping.shippingInfo.address_line_1') . ', ' . data_get(
                $order,
                'shipping.shippingInfo.city'
            ) . ', ' . data_get($order, 'shipping.shippingInfo.state') . ' ' . data_get(
                $order,
                'shipping.shippingInfo.postal_code'
            );
    }
}
