<?php

namespace App\Http\Controllers;

use App\CreditCardPayment;
use App\GiftCard;
use App\Http\Controllers\Api\CalculateRefundController;
use App\Http\Controllers\Api\CreditCardController;
use App\Http\Controllers\Api\GiftCardController;
use App\Payment;
use App\PaypalPayment;

class ChargeController extends Controller
{
    public static function MakePayments($order)
    {
        $giftCardTotal = collect($order->payments['giftCard'])->pluck('amount')->sum();

        if (bcsub($order->grand_total, $giftCardTotal, 2) <= 0) {
            $order->update([
                'status' => 'paid',
                'payment_status' => 'paid'
            ]);
        }

        if (data_get($order, 'payments.creditInfo.payment_type') == 'payPal') {
            $pay_pal = PaypalPayment::where([
                'payer_id' => data_get($order, 'payments.creditInfo.paypal_payer_id'),
                'token' => data_get($order, 'payments.creditInfo.paypal_token')
            ])->first();

            if ($pay_pal == null) {
                $order->update([
                    'staus' => 'cancelled',
                    'payment_status' => 'declined'
                ]);
                return response()->json([
                    'Message' => 'Paypal Undefined'
                ], 404);
            }

            $payer_info = collect($pay_pal->payer_info);

            $result = $payer_info->merge($payer_info['shipping_address'])->merge(['type' => 'Pay Pal']);

            $order->update([
                'payment_type' => PaypalPayment::class,
                'payment_id' => $pay_pal->id,
                'payments->creditInfo' => $result->merge($order, 'payments.creditInfo')
            ]);
            // $order->authorizePayment($order->grand_total - $giftCardTotal);
        } elseif (data_get($order, 'payments.creditInfo.payment_type') == 'creditCard') {
            $payment = Payment::find(data_get($order, 'payments.creditInfo.id'));

            $creditCard = CreditCardPayment::create([
                'amount' => $order->grand_total - $giftCardTotal,
                'token' => data_get($payment, 'token') ?? data_get($order, 'payments.creditInfo.token'),
                'security_code' => data_get($payment, 'securityCode') ?? data_get(
                        $order,
                        'payments.creditInfo.securityCode'
                    )
            ]);
            $order->update([
                'payment_type' => CreditCardPayment::class,
                'payment_id' => $creditCard->id
            ]);
        }

        $chargeNow = max(self::GetChargeNowAmount($order) - $giftCardTotal, 0);

        $amount = $chargeNow >= $giftCardTotal ? max($order->grand_total - $chargeNow, 0) : max(
            $order->grand_total - $giftCardTotal,
            0
        );
        if ($chargeNow) {
            $order->Charge($chargeNow);
        }


        if ($amount) {
            $order->AuthorizePayment($amount);
        }

        self::ChargeGiftcards($order);
    }


    public static function GetChargeNowAmount($order)
    {
        return collect($order->products)->filter(function ($p) {
            return data_get($p, 'item_type') == 'digital' || data_get($p, 'item_type') == 'both' || data_get(
                    $p,
                    'item_type'
                ) == 'service';
        })
            ->map(function ($p) {
                return (data_get($p, 'price') * data_get($p, 'quantity')) + data_get($p, 'tax_amount') - data_get(
                        $p,
                        'discount_amount'
                    );
                // if (array_key_exists('tax_amount', $p)) {
                //     return  ($p['price'] * $p['quantity']) + $p['tax_amount'];
                // } else {
                //     return ($p['price'] * $p['quantity']);
                // };
            })
            ->sum();
    }

    public static function ChargeGiftcards($order)
    {
        $total = $order->grand_total;

        collect(data_get($order, 'payments.giftCard'))->each(function ($card) use ($order) {
            $giftCard = GiftCard::find(data_get($card, 'id'));

            $transaction = $giftCard->transactions()->create([
                'order_id' => $order->id,
                'amount' => data_get($card, 'amount'),
                'type' => 'Order'
            ]);

            $giftCard->ChargeBalance(data_get($card, 'amount'), $order, $transaction);
        });
    }


    public static function RefundReturnPayments($return)
    {
        $charge_refund = data_get($return, 'payments.creditInfo.amount');

        if ($charge_refund > 0) {
            $return->order->refundPayment($charge_refund);
        }

        collect(data_get($return, 'payments.giftCard'))->each(function ($card) use ($return) {
            $giftCard = GiftCard::find(data_get($card, 'id'));

            $giftCard->update([
                'balance' => $giftCard->balance + data_get($card, 'amount')
            ]);

            $transaction = $giftCard->transactions()->create([
                'order_id' => $return->order->id,
                'amount' => data_get($card, 'amount'),
                'type' => 'Refund'
            ]);

            GiftCardController::refillVoucher($transaction);
        });
    }
    //Capture before refund and return
    //By cancel if it was captured then return money
    public static function ReturnCancelOrderPayments($order)
    {
        $breakDown = (new CalculateRefundController)->GetBreakDown(request()->merge([
            'products' => collect($order->products)->where('status', 'cancelled'),
            'order_id' => $order->id,
        ]));

        $hasNonCancellableItems = $order->HasNonCancellableItems();

        if (!$hasNonCancellableItems) {
            if ($order->charge > 0) {
                if (data_get($order, 'meta.captured')) {
                    $order->refundPayment($order->charge);
                } else {
                    $order->cancelPayment();
                }
            }
        }

        collect(data_get($breakDown, 'payments.giftCard'))->each(function ($card) use ($order) {
            $giftCard = GiftCard::find(data_get($card, 'id'));

            $giftCard->update([
                'balance' => $giftCard->balance + data_get($card, 'amount')
            ]);

            $transaction = $giftCard->transactions()->create([
                'order_id' => $order->id,
                'amount' => data_get($card, 'amount'),
                'type' => 'Refund'
            ]);


            GiftCardController::refillVoucher($transaction);
        });
    }

    public static function ModifyPayments($order, $transactions)
    {
        if (data_get($transactions, 'type') == 'refund') {
            $charge_refund = data_get($transactions, 'creditInfo.amount');

            if ($charge_refund > 0) {
                $order->refundPayment($charge_refund);
            }
            collect(data_get($transactions, 'giftCards'))->each(function ($card) use ($order) {
                $giftCard = GiftCard::find(data_get($card, 'id'));

                $giftCard->update([
                    'balance' => $giftCard->balance + data_get($card, 'amount')
                ]);

                $transaction = $giftCard->transactions()->create([
                    'order_id' => $order->id,
                    'amount' => data_get($card, 'amount'),
                    'type' => 'Refund'
                ]);

                GiftCardController::refillVoucher($transaction);
            });
        } elseif (data_get($transactions, 'type') == 'charge') {
            $charge_add = data_get($transactions, 'creditInfo.amount');

            if ($charge_add > 0) {
                if (!data_get($order, 'meta.captured')) {
                    $refnum = $order->payment->refnum;
                    CreditCardController::AuthorizeCC($order, $charge_add);
                    CreditCardController::CancelViaRefnum($refnum);
                } else {
                    CreditCardController::Charge($order, $charge_add);
                }
            }

            $cards = collect(data_get($order, 'giftCards'))->map(function ($card) {
                return GiftCard::find(data_get($card, 'id'));
            });

            collect($cards)->each(function ($giftCard) use ($order, &$total) {
                $transaction = $giftCard->transactions()->create([
                    'order_id' => $order->id,
                    'amount' => $total,
                    'type' => 'Order'
                ]);

                $total = $giftCard->ChargeBalance($total, $order, $transaction);
            });
        }
    }
}
