<?php

namespace App\Http\Controllers;

use App\Jobs\Miscellaneous;
use App\Product;
use App\VariationInfo;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;

class SyncInventoryController extends Controller
{
    public static function syncAll()
    {
        $client = new Client;
        $response = $client->post("https://api5.firstchoicepos.com/v1/user/Authenticate", [
            'json' => [
                "Username" => 'EichlersAPI',
                "Password" => 'eichlersOnline',
                "EnterpriseSid" => '0256b8b4-31e4-4a77-99d3-4a8ce967acdf',
            ]
        ]);
        $result = $response->getBody()->getContents();
        $token = forceArray($result)['Token'];
        //PRODUCTS
        $eachChunk = 50;
        $productCount = Product::count();
        $timesToLoop = $productCount / $eachChunk;

        for ($i = 0; $i <= $timesToLoop; $i++) {
            dispatch(function () use ($i, $eachChunk, $token) {
                $client = new Client;

                $products = DB::table('products')->select(
                    'sku',
                    'store_quantity',
                    'store_price',
                    'website_quantity',
                    'store_category',
                    'store_sub_category',
                    'store_vendor'
                )->skip($i * $eachChunk)->take($eachChunk)->get();

                $response = $client->post("https://api5.firstchoicepos.com/v1/Report/Read", [
                    'headers' => ['Authorization' => 'Bearer ' . $token],
                    'json' => [
                        'ReportKey' => 'API_Items',
                        'Parameters' => [
                            'SKUs' => $products->filter(function ($item) {
                                return $item->sku;
                            })->implode('sku', ','),
                        ]
                    ]
                ]);

                $results = collect(forceArray($response->getBody()->getContents())['Results']);

                $array = [];
                collect($results)->each(function ($result) use (&$array) {
                    $array[] = [
                        'sku' => data_get($result, 'LookupCode'),
                        'quantity' => data_get($result, 'OnHandStore'),
                        'online_quantity' => data_get($result, 'OnHandOnline'),
                        'type' => 'sync',
                        'result' => json_encode($result),
                        'created_at' => now()
                    ];
                });
                DB::table('inventory_track')->insert($array);

                $final = collect($results)
                    ->merge(
                        $products->map(function ($product) {
                            return [
                                'LookupCode' => $product->sku,
                                'OnHandStore' => $product->store_quantity,
                                'OnHandOnline' => $product->website_quantity,
                                'StorePrice' => $product->store_price,
                                'Category' => $product->store_category,
                                'SubCategory' => $product->store_sub_category,
                                'vendor' => $product->store_vendor,
                            ];
                        })
                    )
                    ->groupBy('LookupCode')
                    ->map(function ($item) {
                        if ($item->unique(function ($i) {
                                return data_get($i, 'OnHandOnline') . '_' . data_get(
                                        $i,
                                        'OnHandStore'
                                    ) . '_' . data_get($i, 'StorePrice') . '_' . data_get($i, 'Category')
                                    . '_' . data_get($i, 'SubCategory') . '_' . data_get($i, 'vendor');
                            })->count() > 1) {
                            return $item->first();
                        }
                    })
                    ->filter();

                Miscellaneous::dispatch(PosReportsController::class, 'resolveResults', $final);
            })->onQueue('sync');
        }


        //VARIATION INFOS
        $productCount = VariationInfo::count();
        $timesToLoop = $productCount / $eachChunk;

        for ($i = 0; $i <= $timesToLoop; $i++) {
            dispatch(function () use ($i, $eachChunk, $token) {
                $client = new Client;

                $products = DB::table('variation_infos')->select(
                    'sku',
                    'store_quantity',
                    'store_price',
                    'website_quantity',
                    'store_category',
                    'store_sub_category',
                    'store_vendor'
                )->skip($i * $eachChunk)->take($eachChunk)->get();

                $response = $client->post("https://api5.firstchoicepos.com/v1/Report/Read", [
                    'headers' => ['Authorization' => 'Bearer ' . $token],
                    'json' => [
                        'ReportKey' => 'API_Items',
                        'Parameters' => [
                            'SKUs' => $products->filter(function ($item) {
                                return $item->sku;
                            })->implode('sku', ','),
                        ]
                    ]
                ]);


                $results = collect(forceArray($response->getBody()->getContents())['Results']);

                $array = [];
                collect($results)->each(function ($result) use (&$array) {
                    $array[] = [
                        'sku' => data_get($result, 'LookupCode'),
                        'quantity' => data_get($result, 'OnHandStore'),
                        'online_quantity' => data_get($result, 'OnHandOnline'),
                        'type' => 'sync',
                        'result' => json_encode($result),
                        'created_at' => now()
                    ];
                });
                DB::table('inventory_track')->insert($array);

                $final = collect($results)
                    ->merge(
                        $products->map(function ($product) {
                            return [
                                'LookupCode' => $product->sku,
                                'OnHandStore' => $product->store_quantity,
                                'OnHandOnline' => $product->website_quantity,
                                'StorePrice' => $product->store_price,
                                'Category' => $product->store_category,
                                'SubCategory' => $product->store_sub_category,
                                'vendor' => $product->store_vendor,
                            ];
                        })
                    )
                    ->groupBy('LookupCode')
                    ->map(function ($item) {
                        if ($item->unique(function ($i) {
                                return data_get($i, 'OnHandOnline') . '_' . data_get(
                                        $i,
                                        'OnHandStore'
                                    ) . '_' . data_get($i, 'StorePrice') . '_' . data_get($i, 'Category')
                                    . '_' . data_get($i, 'SubCategory') . '_' . data_get($i, 'vendor');
                            })->count() > 1) {
                            return $item->first();
                        }
                    })
                    ->filter();

                Miscellaneous::dispatch(PosReportsController::class, 'resolveResults', $final);
            })->onQueue('sync');
        }
    }

    public static function syncAllSaleItems()
    {
        $client = new Client;
        $response = $client->post("https://api5.firstchoicepos.com/v1/user/Authenticate", [
            'json' => [
                "Username" => 'EichlersAPI',
                "Password" => 'eichlersOnline',
                "EnterpriseSid" => '0256b8b4-31e4-4a77-99d3-4a8ce967acdf',
            ]
        ]);
        $result = $response->getBody()->getContents();
        $token = forceArray($result)['Token'];
        //PRODUCTS
        $eachChunk = 50;
        $productCount = DB::table('products')->count();
        $timesToLoop = $productCount / $eachChunk;

        for ($i = 0; $i <= $timesToLoop; $i++) {
            dispatch(function () use ($i, $eachChunk, $token, $timesToLoop) {
                $client = new Client;

                $products = DB::table('products')->select(
                    'sku',
                    'sale_price',
                    'online_price',
                    'store_price',
                    'list_price'
                )->skip($i * $eachChunk)->take($eachChunk)->get();
                $batch = $i + 1 . '-of-' . $timesToLoop;
                $response = $client->post("https://api5.firstchoicepos.com/v1/Report/Read?batch={$batch}", [
                    'headers' => ['Authorization' => 'Bearer ' . $token],
                    'json' => [
                        'ReportKey' => 'API_SaleItemPricing',
                        'Parameters' => [
                            'SKUs' => $products->filter(function ($item) {
                                return $item->sku;
                            })->implode('sku', ','),
                        ]
                    ]
                ]);

                $final = collect(forceArray($response->getBody()->getContents())['Results'])
                    ->merge(
                        $products->map(function ($product) {
                            return [
                                'LookupCode' => $product->sku,
                                'sale_price' => $product->sale_price,
                                'source' => true,
                                // 'price' => $product->list_price
                                //         ?? $product->store_price
                            ];
                        })
                    )
                    ->groupBy('LookupCode')
                    ->map(function ($item) {
                        if ($item->count() > 1) {
                            $first = $item->first();
                            $last = $item->last();

                            if (!data_get($first, 'PercentDiscountActive')) {
                                $sale_price = null;
                            } else {
                                $price = self::price($first);
                                $sale_price = $price - ((data_get($first, 'PercentDiscountActive') / 100) * $price);
                            }
                            if ($item->where('source')->where('sale_price', '!=', $sale_price)->count() > 0) {
                                // if (data_get($last, 'sale_price') != $sale_price) {
                                return [
                                    'LookupCode' => data_get($last, 'LookupCode'),
                                    'sale_price' => $sale_price,
                                    'percent' => data_get($first, 'PercentDiscountActive')
                                ];
                            }
                        }
                    })
                    ->filter();

                $final->chunk(50)->each(function ($items) {
                    Miscellaneous::dispatch(
                        PosReportsController::class,
                        'resolveSalePriceResults',
                        $items
                    );
                });
            })->onQueue('sync');
        }


        //VARIATION INFOS
        $productCount = DB::table('variation_infos')->count();
        $timesToLoop = $productCount / $eachChunk;

        for ($i = 0; $i <= $timesToLoop; $i++) {
            dispatch(function () use ($i, $eachChunk, $token, $timesToLoop) {
                $client = new Client;


                $products = DB::table('variation_infos')->select(
                    'sku',
                    'sale_price',
                    'online_price',
                    'store_price',
                    'list_price',
                    'product_json'
                )->skip($i * $eachChunk)->take($eachChunk)->get();

                $batch = $i + 1 . '-of-' . $timesToLoop;
                $response = $client->post("https://api5.firstchoicepos.com/v1/Report/Read?batch={$batch}", [
                    'headers' => ['Authorization' => 'Bearer ' . $token],
                    'json' => [
                        'ReportKey' => 'API_SaleItemPricing',
                        'Parameters' => [
                            'SKUs' => $products->filter(function ($item) {
                                return $item->sku;
                            })->implode('sku', ','),
                        ]
                    ]
                ]);

                $final = collect(forceArray($response->getBody()->getContents())['Results'])
                    ->merge(
                        $products->map(function ($product) {
                            return [
                                'LookupCode' => $product->sku,
                                'sale_price' => $product->sale_price,
                                'source' => true,
                                // 'price' => $product->list_price
                                //         ?? data_get(json_decode($product->product_json), 'list_price')
                                //         ?? $product->store_price
                                //         ?? data_get(json_decode($product->product_json), 'store_price')
                            ];
                        })
                    )
                    ->groupBy('LookupCode')
                    ->map(function ($item) {
                        if ($item->count() > 1) {
                            $first = $item->first();
                            $last = $item->last();

                            if (!data_get($first, 'PercentDiscountActive')) {
                                $sale_price = null;
                            } else {
                                $price = self::price($first);
                                $sale_price = $price - ((data_get($first, 'PercentDiscountActive') / 100) * $price);
                            }
                            if ($item->where('source')->where('sale_price', '!=', $sale_price)->count() > 0) {
                                // if (data_get($last, 'sale_price') != $sale_price) {
                                return [
                                    'LookupCode' => data_get($last, 'LookupCode'),
                                    'sale_price' => $sale_price,
                                    'percent' => data_get($first, 'PercentDiscountActive')
                                ];
                            }
                        }
                    })
                    ->filter();

                $final->chunk(50)->map(function ($items) {
                    Miscellaneous::dispatch(
                        PosReportsController::class,
                        'resolveSalePriceResults',
                        $items
                    );
                });
            })->onQueue('sync');
        }
    }

    public static function price($first)
    {
        $source = data_get($first, 'Source');
        $price_array = [
            0 => 'Price',
            1 => 'MSRP',
            2 => 'Cost',
        ];
        $price_type = data_get($price_array, $source);
        return data_get($first, $price_type) ?? null;
    }
}
