<?php

namespace App\Http\Controllers;

use Cookie;
use Facades\Spatie\Referer\Referer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class UtmController extends Controller
{
    public static function AddSources($order)
    {
        $utm = data_get($order, 'meta.request.utm');
        if ($utm) {
            $order->utm()->create($utm);
            $customer = $order->customer;
            if (!$customer->utm) {
                $customer->utm()->create($utm);
            }
        }
    }

    public function SetUTM(Request $request)
    {
        $id = session()->getId();

        if (!Cache::has($id)) {
            Cache::put($id, [
                'utm_source' => Referer::get(),
                'utm_medium' => data_get($request, 'utm_medium'),
                'utm_campaign' => data_get($request, 'utm_campaign'),
                'utm_term' => data_get($request, 'utm_term'),
                'utm_content' => data_get($request, 'utm_content'),
            ], now()->addDay());
        }

        if ($user = auth()->user()) {
            if (!$user->utm) {
                $user->utm()->create(Cache::get(session()->getId()) ?? []);
            }
        }
        Referer::forget();
    }
}
