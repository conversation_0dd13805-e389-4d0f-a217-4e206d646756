<?php

namespace App\Http\Controllers;

class InventoryController extends Controller
{
    public static function DeductFromInventory($order)
    {
        $products = collect($order->products)->map(function ($item) {
            $product = GetFromFrontEnd([$item])->first()->model;

            if ($product && $product->shippable) {
                if ($item['type'] == 'bundle') {
                    $item['products'] = collect($item['products'])->map(function ($bundle_item) use ($item) {
                        $bundle_product = GetFromFrontEnd([$bundle_item])->first()->model;

                        $deduct = $bundle_product->getDeductQuantity(
                            $item['quantity'] * $bundle_item['quantity']
                        ) ?? [];

                        $bundle_item['website_deduct'] = data_get($deduct, 'website_deduct');
                        $bundle_item['store_deduct'] = data_get($deduct, 'store_deduct');

                        $bundle_product->deductQuantity($item['quantity'] * $bundle_item['quantity']);

                        $bundle_product->removeFromBags();

                        return $bundle_item;
                    });
                } else {
                    $deduct = $product->getDeductQuantity($item['quantity']) ?? [];

                    $item['website_deduct'] = data_get($deduct, 'website_deduct');
                    $item['store_deduct'] = data_get($deduct, 'store_deduct');

                    $product->deductQuantity($item['quantity']);

                    $product->removeFromBags();
                }
            }

            return $item;
        });

        $order->update([
            'products' => $products
        ]);
    }


    public static function AddToInventory($products)
    {
        collect($products)->each(function ($item) {
            $product = GetFromFrontEnd([$item])->first()->model;

            if ($product && $product->shippable) {
                if ($item['type'] == 'bundle') {
                    collect($item['products'])->each(function ($bundle_item) {
                        $bundle_product = GetFromFrontEnd([$bundle_item])->first()->model;

                        $website_add = min(data_get($bundle_item, 'website_deduct'), $bundle_item['quantity']);
                        $store_add = min(
                            max($bundle_item['quantity'] - data_get($bundle_item, 'website_deduct'), 0),
                            data_get($bundle_item, 'store_deduct')
                        );

                        $bundle_product->update([
                            'website_quantity' => $bundle_product->website_quantity + $website_add,
                            'store_quantity' => $bundle_product->store_quantity + $store_add
                        ]);
                    });
                } else {
                    $website_add = min(data_get($item, 'website_deduct'), $item['quantity']);
                    $store_add = min(
                        max($item['quantity'] - data_get($item, 'website_deduct'), 0),
                        data_get($item, 'store_deduct')
                    );

                    $product->update([
                        'website_quantity' => $product->website_quantity + $website_add,
                        'store_quantity' => $product->store_quantity + $store_add
                    ]);
                }
            }
        });
    }
}
