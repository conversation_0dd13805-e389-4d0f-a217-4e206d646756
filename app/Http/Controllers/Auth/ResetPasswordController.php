<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Auth;
use Illuminate\Foundation\Auth\ResetsPasswords;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;

class ResetPasswordController extends Controller
{
    use ResetsPasswords;

    protected $redirectTo = '/';
    private $user;

    public function __construct()
    {
        $this->middleware('guest');
    }

    public function token(Request $request)
    {
        return encrypt($request->password);
    }

    public function reset(Request $request)
    {
        $request->validate($this->rules(), $this->validationErrorMessages());

        $response = $this->broker()->reset(
            $this->credentials($request), function ($user, $password) {
            $this->user = $user;
            $this->resetPassword($user, $password);
        }
        );

        if ($response == Password::PASSWORD_RESET) {
            Auth::loginUsingId($this->user->id);
            return auth()->user();
        }
        return $response == Password::PASSWORD_RESET
            ? $this->sendResetResponse($request, $response)
            : $this->sendResetFailedResponse($request, $response);
    }
}
