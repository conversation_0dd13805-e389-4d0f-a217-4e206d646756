<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Api\EmailsController;
use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\SendsPasswordResetEmails;
use Illuminate\Http\Request;

class ForgotPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset emails and
    | includes a trait which assists in sending these notifications from
    | your application to your users. Feel free to explore this trait.
    |
    */

    use SendsPasswordResetEmails;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    public function sendResetLinkEmail(Request $request)
    {
        $request->validate(['email' => 'required|email|exists:customers,email']);

        $email = $request->email;
        $link = env('APP_URL')
            . '/password/reset/'
            . $this->broker()->createToken(
                $this->broker()->getUser($this->credentials($request))
            );

        (new EmailsController)->SendPasswordReset(compact('link', 'email'));
        return response(200);
    }
}
