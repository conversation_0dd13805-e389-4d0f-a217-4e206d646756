<?php

namespace App\Http\Controllers;

use App\Returns;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use App\Http\Controllers\Api\ShipEngineController;

class ReturnLabelController extends Controller
{
    public static function CreateReturnLabel($return)
    {
        $client = new Client;
        $weight = collect($return->products)->map(function ($product) {
            return data_get($product, 'quantity') * data_get($product, 'weight');
        })->sum();

        $weight = $weight > 0 ? $weight : 1;

        $response = $client->post('https://api.shipengine.com/v1/labels', [
            'headers' => [
                'Content-Type' => 'application/json',
                'API-Key' => env('SHIP_ENGINE_API'),
            ],
            'json' => [

                "shipment" => [
                    "service_code" => settings()->getValue('return_service'),
                    "ship_to" => [
                        "phone" => settings()->getValue('phone'),
                        "company_name" => settings()->getValue('store_name'),
                        "address_line1" => settings()->getValue('shipping_address_line_1'),
                        "address_line2" => settings()->getValue('shipping_address_line_2'),
                        "city_locality" => settings()->getValue('shipping_city'),
                        "state_province" => settings()->getValue('shipping_state'),
                        "postal_code" => settings()->getValue('shipping_postal_code'),
                        "country_code" => settings()->getValue('shipping_country'),
                        "address_residential_indicator" => "No"
                    ],
                    "ship_from" => [
                        "name" => data_get($return, 'shippingInfo.name'),
                        "address_line1" => data_get($return, 'shippingInfo.address_line_1'),
                        "address_line2" => data_get($return, 'shippingInfo.address_line_2'),
                        "city_locality" => data_get($return, 'shippingInfo.city'),
                        "state_province" => data_get($return, 'shippingInfo.state'),
                        "postal_code" => data_get($return, 'shippingInfo.postal_code'),
                        "country_code" => data_get($return, 'shippingInfo.country'),
                    ],
                    "packages" => [
                        [
                            "weight" => [
                                "value" => $weight,
                                "unit" => "pound"
                            ]
                        ]
                    ]
                ],
                "is_return_label" => true,
                "rma_number" => $return->id,
            ]

        ]);
        $result = $response->getBody()->getContents();
        $result = forceArray($result);
        $return->update([
            'shipping->tracking' => [
                'label_id' => data_get($result, 'label_id'),
                'shipment_id' => data_get($result, 'shipment_id'),
                'shipment_cost' => data_get($result, 'shipment_cost.amount'),
                'tracking_number' => data_get($result, 'tracking_number'),
                'rma_number' => data_get($result, 'rma_number'),
                'service_code' => data_get($result, 'service_code'),
                'carrier_code' => data_get($result, 'carrier_code'),
                'label_pdf' => data_get($result, 'label_download.pdf'),
                'label_zpl' => data_get($result, 'label_download.zpl'),
            ]
        ]);

        $info = (new ShipEngineController)->GetLinkAndArrivalDate($return);
        $return->update([
            'shipping->tracking->link' => $info['link'],
            'shipping->tracking->arrival_date' => $info['arrival_date'],
        ]);
        (new ShipEngineController)->SetUpWebhook($return);

        return [
            'pdf' => data_get($result, 'label_download.pdf'),
            'zpl' => data_get($result, 'label_download.zpl'),
        ];
    }

    public static function VoidOldLabels()
    {
        $returns = Returns::where('status', 'pending')->get();
        if ($returns->isEmpty()) {
            return;
        }
        $returns->each(function ($return) {
            $label_id = data_get($return, 'shipping.tracking.label_id');

            $client = new Client;

            if (!$return->isValid()) {
                try {
                    $client->put("https://api.shipengine.com/v1/labels/{$label_id}/void", [
                        'headers' => [
                            'Content-Type' => 'application/json',
                            'API-Key' => env('SHIP_ENGINE_API'),
                        ]
                    ]);
                    $return->update([
                        'status' => 'voided'
                    ]);
                } catch (Exception $ex) {
                }
            }
        });
    }
}
