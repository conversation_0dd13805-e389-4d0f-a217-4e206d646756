<?php

namespace App\Http\Controllers;

class GiftNotesController extends Controller
{
    public static function CreateGiftNotes($order)
    {
        $products = collect($order->products)->map(function ($product) use ($order) {
            if ($note = data_get($product, 'gift_options')) {
                $giftNote = $order->giftNotes()->create([
                    'gift_notes_setting_id' => data_get($note, 'giftNote.id'),
                    'occasionText' => data_get($note, 'occasionText'),
                    'textDirection' => data_get($note, 'textDirection'),
                    'sku' => data_get($product, 'sku')
                ]);

                $product['gift_options']['gift_note_id'] = $giftNote->id;
            }
            return $product;
        });

        $order->update([
            'products' => $products
        ]);
    }
}
