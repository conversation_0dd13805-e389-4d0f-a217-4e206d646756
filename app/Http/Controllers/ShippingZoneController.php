<?php

namespace App\Http\Controllers;

use App\ShippingOptionShippingZone;
use App\ShippingZone;
use App\ZipCode;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ShippingZoneController extends Controller
{
    /**
     * GET /shippingzone
     * Fetches all current shipping zones with zip codes
     * @return JsonResponse
     */
    public function browseShippingZones()
    {
        $shippingZones = ShippingZone::all();
        $shippingZonesCombined = [];

        foreach ($shippingZones as $shippingZone) {
            array_push($shippingZonesCombined, [
                'shipping_zone' => $shippingZone,
                'zip_codes' => $shippingZone->zipCodes()->get(),
                'shipping_options' => $shippingZone->shippingOptions()->get()
            ]);
        }

        return response()->json($shippingZonesCombined);
    }

    /**
     * GET /shippingzone/{id}
     * Fetches specified shipping zone with zip codes
     * @param int id - The DB ID of the requested shipping zone
     * @return JsonResponse
     */
    public function readShippingZone($id)
    {
        try {
            $shippingZone = ShippingZone::findOrFail($id);

            return response()->json([
                'shipping_zone' => $shippingZone,
                'zip_codes' => $shippingZone->zipCodes()->get(),
                'shipping_options' => $shippingZone->shippingOptions()->get()
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json(false);
        }
    }

    /**
     * POST /shippingzone
     * Adds a new shipping zone
     * @param string name - Name of the shipping zone
     * @param string zip_codes - A comma-delimited list of properly formatted 3-digit zip codes,
     *          either separately identified or defined in dash-separated ranges (i.e. 005-010)
     * @param array shipping_options - Array of JSON objects specifying shipping_option details (see below)
     * @param int shipping_option_id - The DB ID of the shipping option that will be associated with the newly created shipping zone
     * @param double price_per_pound
     * @param double base_rate
     * @param double duration
     * @param boolean duration_is_hourly
     * @param double free_shipping_above (nullable)
     * @return JsonResponse
     */
    public function addShippingZone(Request $request)
    {
        try {
            $shippingZone = ShippingZone::create($request->all());

            foreach ($request->input('shipping_options') as $shippingOption) {
                ShippingOptionShippingZone::create(array_merge((array)$shippingOption, [
                    'shipping_zone_id' => $shippingZone->id
                ]));
            }

            $this->processZipCodeShippingZones($request->input('zip_codes'), $shippingZone->id);

            return response()->json(['success' => true]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Create failed']);
        }
    }

    /**
     * PATCH /shippingzone/{id}
     * Edits a shipping zone
     * @param int id - The DB ID of the shipping zone to be edited
     * @param string zip_codes - A comma-delimited list of properly formatted 3-digit zip codes,
     *          either separately identified or defined in dash-separated ranges (i.e. 005-010)
     * @param array shipping_options - Array of JSON objects specifying shipping_option details (see below)
     * @param int shipping_option_id - The DB ID of the shipping option that will be associated with the newly created shipping zone
     * @param double price_per_pound
     * @param double base_rate
     * @param double duration
     * @param boolean duration_is_hourly
     * @param double free_shipping_above (nullable)
     * @return JsonResponse
     */
    public function editShippingZone(Request $request, $id)
    {
        try {
            ShippingZone::findOrFail($id)
                ->update($request->all());

            $submittedShippingOptions = $request->input('shipping_options');

            if (count($submittedShippingOptions)) {
                foreach ($submittedShippingOptions as $shippingOption) {
                    ShippingOptionShippingZone::updateOrCreate(
                        ['shipping_option_id' => $shippingOption['shipping_option_id'], 'shipping_zone_id' => $id],
                        $shippingOption
                    );
                }
            }

            $existingShippingOptions = ShippingOptionShippingZone::where(['shipping_zone_id' => $id])->get();

            if (count($existingShippingOptions)) {
                foreach ($existingShippingOptions as $existingShippingOption) {
                    if (!in_array($existingShippingOption->shipping_option_id, $submittedShippingOptions)) {
                        ShippingOptionShippingZone::destroy($existingShippingOption->id);
                    }
                }
            }

            $this->processZipCodeShippingZones($request->input('zip_codes'), $id);

            return response()->json(['success' => true]);
        } catch (ModelNotFoundException $e) {
            return response()->json(['success' => false, 'message' => 'Submitted ID does not exist']);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Update failed']);
        }
    }

    /**
     * DELETE /shippingzone/{id}
     * Deletes a shipping zone
     * @param int id - The DB ID of the shipping zone to be deleted
     * @return JsonResponse
     */
    public function deleteShippingZone($id)
    {
        try {
            ShippingZone::destroy($id);

            return response()->json(['success' => true]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Delete failed']);
        }
    }

    /**
     * Associates a shipping zone with the specified zip codes
     * @param string $zipCodes - A comma-delimited list of properly formatted 3-digit zip codes,
     *          either separately identified or defined in dash-separated ranges (i.e. 005-010)
     * @param int $id - The ID of the shipping zone to be associated with the provided zip codes
     * @return
     */
    private function processZipCodeShippingZones($zipCodes, $id)
    {
        if (preg_match_all("/[^-,\s\d]/", $zipCodes) > 0) {
            return response()->json(
                [
                    'success' => false,
                    'message' => 'Format of zip codes submission is incorrect; must be comma-delimited list of numbers with no line breaks'
                ]
            );
        }

        ZipCode::where('shipping_zone_id', $id)->update(['shipping_zone_id' => null]);

        $zipCodesIdentifiers = explode(",", str_replace(" ", "", $zipCodes));

        foreach ($zipCodesIdentifiers as $zipCodeIdentifier) {
            if (strpos($zipCodeIdentifier, '-') === false) {
                $formattedZipCode = $this->formatZipCode($zipCodeIdentifier);

                ZipCode::where(['zip_code' => $formattedZipCode])
                    ->update(['shipping_zone_id' => $id]);
            } else {
                $range = explode("-", $zipCodeIdentifier);

                for ($i = intval($range[0]); $i <= intval($range[1]); $i++) {
                    $formattedZipCode = $this->formatZipCode($i);

                    ZipCode::where(['zip_code' => $formattedZipCode])
                        ->update(['shipping_zone_id' => $id]);
                }
            }
        }
    }

    /**
     * Formats a 1-3 digit zip code into a properly formatted 3-digit zip code with leading zeros
     * @param int $zipCode - The 1-3 digit zip code to be formatted into a 3-digit zip code
     * @return string|null
     */
    private function formatZipCode($zipCode)
    {
        switch (strlen($zipCode)) {
            case 1:
                return '00' . $zipCode;
            case 2:
                return '0' . $zipCode;
            case 3:
                return $zipCode;
            default:
                return null;
        }
    }

}
