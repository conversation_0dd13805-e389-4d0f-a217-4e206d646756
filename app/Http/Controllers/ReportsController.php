<?php

namespace App\Http\Controllers;

use App\Customer;
use App\Export;
use App\GiftCard;
use App\Order;
use App\Product;
use App\ZipCode;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Rap2hpoutre\FastExcel\FastExcel;

class ReportsController extends Controller
{
    public static $dateGroups = ['date' => 'm/d/Y', 'month' => 'Y:M', 'year' => 'Y'];

    public function Report(Request $request)
    {
        $options = [
            'name' => $request->name,
            'from' => $request->from,
            'start' => $request->start,
            'end' => $request->end,
            'groupBy' => $request->groupBy,
            'title' => $request->start ? Str::title(
                    str_replace('_', ' ', $request->name)
                ) . ' From ' . $request->start . ' To ' . $request->end : 'All ' . Str::title(
                    str_replace('_', ' ', $request->name)
                ),
            'active' => $request->active,
            'categories' => $request->categories,
            'vendors' => $request->vendors,
            'all_vendors' => $request->all_vendors,
            'all_creators' => $request->all_creators,
            'digital_vendors' => $request->digital_vendors,
        ];

        $name = $options['name'];

        $this->$name($options);
    }

    public function sales_by_date($options)
    {
        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $dateGroup = data_get($options, 'groupBy');

        $orders = $start ? Order::setEagerLoads([])->whereBetween('created_at', [$start, $end]) : Order::setEagerLoads(
            []
        );

        $orders = $orders->where('status', '!=', 'cancelled')
            ->get(
                [
                    'created_at',
                    'status',
                    'sub_total',
                    'discount_amount',
                    'discount',
                    'grand_total',
                    'tax_amount',
                    'shipping_amount',
                    'payments'
                ]
            );


        $listing = $orders->groupBy(function ($order) use ($dateGroup) {
            if ($dateGroup != 'week' && $dateGroup != 'all') {
                return $order->created_at->format(self::$dateGroups[$dateGroup]);
            } elseif ($dateGroup == 'week') {
                return $this->weekFilter($order);
            } else {
                return 'All';
            }
        })
            ->sortBy(function ($group) {
                return $group->first()->created_at;
            })
            ->map(function ($group, $key) {
                $gc_totals = $group->map->payments->map(function ($payment) {
                    $payment = is_array($payment) ? $payment : json_decode($payment);
                    return collect(data_get($payment, 'giftCard'))->sum('amount');
                })->sum();

                return [
                    'DATE' => $key,
                    'ORDERS' => $group->count(),
                    'GROSS NET' => $group->sum('sub_total') ?? 0.00,
                    'DISCOUNT AMOUNT' => $group->sum(function ($item) {
                            if (data_get($item, 'discount.type') != 'freeShipping') {
                                return $item->discount_amount;
                            }
                        }) ?? 0.00,
                    'NET SALES' => $net_sales = $group->sum('grand_total') - $group->sum('tax_amount') - $group->sum(
                        'shipping_amount'
                    ) ?? 0.00,
                    'NET SALES WITH OUT GC' => $net_sales - $gc_totals,
                    'SHIPPING' => $group->sum('shipping_amount') ?? 0.00,
                    'TAX' => $group->sum('tax_amount') ?? 0.00,
                    'AVERAGE SALES' => $group->avg('grand_total') ?? 0.00,
                    'TOTAL SALES' => $group->sum('grand_total') ?? 0.00
                ];
            });
        $total = [
            [
                'DATE' => 'TOTAL',
                'ORDERS' => $listing->sum('ORDERS'),
                'GROSS NET' => $listing->sum('GROSS NET') ?? 0.00,
                'DISCOUNT AMOUNT' => $listing->sum('DISCOUNT AMOUNT') ?? 0.00,
                'NET SALES' => $listing->sum('NET SALES') ?? 0.00,
                'NET SALES WITH OUT GC' => $listing->sum('NET SALES WITH OUT GC') ?? 0.00,
                'SHIPPING' => $listing->sum('SHIPPING') ?? 0.00,
                'TAX' => $listing->sum('TAX') ?? 0.00,
                'AVERAGE SALES' => $listing->avg('AVERAGE SALES') ?? 0.00,
                'TOTAL SALES' => $listing->sum('TOTAL SALES') ?? 0.00
            ]
        ];
        $this->createReport(
            collect(array_merge($total, $listing->toArray())),
            data_get($options, 'title') . ' Group by ' . Str::title($dateGroup)
        );
    }

    public function weekFilter($order)
    {
        return $order->created_at->format('Y: ')
            . $order->created_at->startOfWeek()->format('M d - ')
            . $order->created_at->endOfWeek()->format('M d');
    }

    public function createReport($options, $name)
    {
        $name = str_replace('/', '-', $name);
        dispatch(function () use ($options, $name) {
            (new FastExcel($this->getUsersOneByOne($options)))->export(
                storage_path("app/{$name}.csv")
            ); //->export('storage/app/exports.csv');
            (new ReportsController)->create($name);
        });
    }

    public function getUsersOneByOne($options)
    {
        foreach ($options as $option) {
            $object = [];
            foreach ($option as $key => $value) {
                $object[$key] = $value;
            }
            yield $object;
        }
    }

    public function create($name)
    {
        $export = Export::create([
            'name' => $name,
        ]);

        // try {
        $export->addMedia(storage_path("app/{$name}.csv"))->toMediaCollection('file');
        Storage::delete("{$name}.csv");
        // } catch (\Exception $ex) {
        //     $export->delete();
        // }
    }

    public function digital_sales_by_vendor($options)
    {
        $title = data_get($options, 'title');

        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $orders = $start ? Order::setEagerLoads([])->whereBetween('created_at', [$start, $end]) : Order::setEagerLoads(
            []
        );

        $vendors = collect();
        if (data_get($options, 'digital_vendors')) {
            $title .= ' For Vendors: ';

            collect(data_get($options, 'digital_vendors'))->each(function ($vendor) use (&$vendors, &$title) {
                $name = DB::table('vendors')->select('name')->where('id', $vendor)->first()->name;
                $title .= $name . ', ';
                $vendors->push($name);
            });
        }

        $orders = $orders
            ->get(['id', 'products', 'created_at'])
            ->where('status', '!=', 'cancelled');

        $listings = $orders
            ->filter(function ($order) {
                return collect($order->products)->contains(function ($product) {
                    return data_get($product, 'item_type') == 'digital';
                });
            })->map(function ($order) use ($vendors) {
                return collect($order->products)->filter(function ($product) use ($vendors) {
                    if (collect($vendors)->count()) {
                        return data_get($product, 'item_type') == 'digital' && collect($vendors)->contains(
                                data_get($product, 'vendor')
                            );
                    }
                    return data_get($product, 'item_type') == 'digital';
                })->map(function ($product) use ($order) {
                    return [
                        'VENDOR' => data_get($product, 'vendor'),
                        'ORDER ID' => $order->id,
                        'ORDER DATE' => $order->created_at->toDateTimeString(),
                        'SKU' => data_get($product, 'sku'),
                        'ID' => data_get($product, 'id'),
                        'TITLE' => data_get($product, 'title'),
                        'PRICE' => data_get($product, 'price'),
                        'QUANTITY' => data_get($product, 'quantity'),
                    ];
                })->values();
            })->flatten(1)
            ->values()
            ->sortBy('vendor');

        $this->createReport(collect($listings->toArray()), $title);
    }

    public function sales_by_product_by_vendor($options)
    {
        $title = data_get($options, 'title');

        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $vendors = collect();
        if (data_get($options, 'all_vendors')) {
            $title .= ' For Vendors: ';

            collect(data_get($options, 'all_vendors'))->each(function ($vendor) use (&$vendors, &$title) {
                $name = DB::table('vendors')->select('name')->where('id', $vendor)->first()->name;
                $title .= $name . ', ';
                $vendors->push($name);
            });
        }

        $orders = $start ? DB::table('orders')->where('status', '!=', 'cancelled')->whereBetween(
            'created_at',
            [$start, $end]
        )->select('payments')->get()
            : DB::table('orders')->where('status', '!=', 'cancelled')->select('payments')->get();
        $gc_totals = $orders->map->payments->map(function ($payment) {
            $payment = is_array($payment) ? $payment : json_decode($payment);
            return collect(data_get($payment, 'giftCard'))->sum('amount');
        })->sum();

        $products = collect();
        foreach (
            $start ? DB::table('orders')->where('status', '!=', 'cancelled')->whereBetween('created_at', [$start, $end]
            )->select('products')->cursor()
                : DB::table('orders')->where('status', '!=', 'cancelled')->select('products')->cursor() as $order
        ) {
            $products->push(json_decode($order->products));
        }
        // dd($vendors);
        $products = $products->flatten(1)->where('product_id')->whereIn('vendor', $vendors)->groupBy('product_id');

        $listings = $products->flatten(1)
            ->where('product_id')
            ->groupBy('product_id')
            ->map(function ($product) use ($gc_totals) {
                return [
                    'TITLE' => data_get($product->first(), 'title'),
                    'SKU' => data_get($product->first(), 'sku'),
                    'TYPE' => data_get($product->first(), 'item_type') ?? 'physical',
                    'VENDOR' => data_get($product->first(), 'vendor'),
                    'QUANTITY' => $product->sum('quantity'),
                    'GROSS SALES' => $product->sum('total') ?? 0.00,
                    'DISCOUNTS' => $product->sum('discount_amount') ?? 0.00,
                    'NET SALES' => $net_sales = $product->sum(function ($item) {
                        return data_get($item, 'total') - data_get($item, 'discount_amount');
                    }) ?? 0.00,
                    // 'NET SALES WITH OUT GC' => $net_sales - $gc_totals,
                    'TAX' => $product->sum('tax_amount') ?? 0.00,
                    'TOTAL SALES' => $product->sum(function ($item) {
                            return (data_get($item, 'total') - data_get($item, 'discount_amount')) + data_get(
                                    $item,
                                    'tax_amount'
                                );
                        }) ?? 0.00
                ];
            });
        $summary = [
            [
                'TITLE' => 'SUMMARY',
                'SKU' => '',
                'TYPE' => '',
                'VENDOR' => '',
                'QUANTITY' => $listings->sum('QUANTITY'),
                'GROSS SALES' => $listings->sum('GROSS SALES') ?? 0.00,
                'DISCOUNTS' => $listings->sum('DISCOUNTS') ?? 0.00,
                'NET SALES' => $listings->sum('NET SALES') ?? 0.00,
                // 'NET SALES WITH OUT GC' => $listings->sum('NET SALES WITH OUT GC') ?? 0.00,
                'TAX' => $listings->sum('TAX') ?? 0.00,
                'TOTAL SALES' => $listings->sum('TOTAL SALES') ?? 0.00
            ]
        ];
        $this->createReport(collect(array_merge($summary, $listings->toArray())), $title);
    }

    public function sales_by_product_by_creator($options)
    {
        $title = data_get($options, 'title');

        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $creators = collect();
        if (data_get($options, 'all_creators')) {
            $title .= ' For Creators: ';

            collect(data_get($options, 'all_creators'))->each(function ($creator) use (&$creators, &$title) {
                $name = DB::table('creators')->select('name')->where('id', $creator)->first()->name;
                $title .= $name . ', ';
                $creators->push($name);
            });
        }


        $orders = $start ? DB::table('orders')->where('status', '!=', 'cancelled')->whereBetween(
            'created_at',
            [$start, $end]
        )->select('payments')->get()
            : DB::table('orders')->where('status', '!=', 'cancelled')->select('payments')->get();
        $gc_totals = $orders->map->payments->map(function ($payment) {
            $payment = is_array($payment) ? $payment : json_decode($payment);
            return collect(data_get($payment, 'giftCard'))->sum('amount');
        })->sum();


        $products = collect();
        foreach (
            $start ? DB::table('orders')->where('status', '!=', 'cancelled')->whereBetween('created_at', [$start, $end]
            )->select('products')->cursor()
                : DB::table('orders')->where('status', '!=', 'cancelled')->select('products')->cursor() as $order
        ) {
            $products->push(json_decode($order->products));
        }
        // dd($vendors);
        $products = $products->flatten(1)->where('product_id')->whereIn(
            'product_id',
            DB::table('creator_product')->whereIn('creator_id', data_get($options, 'all_creators'))->get()->pluck(
                'product_id'
            )->unique()->values()
        )->groupBy('product_id');

        $listings = $products->flatten(1)
            ->where('product_id')
            ->groupBy('product_id')
            ->map(function ($product) {
                return [
                    'TITLE' => data_get($product->first(), 'title'),
                    'SKU' => data_get($product->first(), 'sku'),
                    'TYPE' => data_get($product->first(), 'item_type') ?? 'physical',
                    'VENDOR' => data_get($product->first(), 'vendor'),
                    'QUANTITY' => $product->sum('quantity'),
                    'GROSS SALES' => $product->sum('total') ?? 0.00,
                    'DISCOUNTS' => $product->sum('discount_amount') ?? 0.00,
                    'NET SALES' => $product->sum(function ($item) {
                            return data_get($item, 'total') - data_get($item, 'discount_amount');
                        }) ?? 0.00,
                    'TAX' => $product->sum('tax_amount') ?? 0.00,
                    'TOTAL SALES' => $product->sum(function ($item) {
                            return (data_get($item, 'total') - data_get($item, 'discount_amount')) + data_get(
                                    $item,
                                    'tax_amount'
                                );
                        }) ?? 0.00
                ];
            });
        $summary = [
            [
                'TITLE' => 'SUMMARY',
                'SKU' => '',
                'TYPE' => '',
                'VENDOR' => '',
                'QUANTITY' => $listings->sum('QUANTITY'),
                'GROSS SALES' => $listings->sum('GROSS SALES') ?? 0.00,
                'DISCOUNTS' => $listings->sum('DISCOUNTS') ?? 0.00,
                'NET SALES' => $listings->sum('NET SALES') ?? 0.00,
                'NET SALES WITH OUT GC' => $listings->sum('NET SALES WITH OUT GC') ?? 0.00,
                'TAX' => $listings->sum('TAX') ?? 0.00,
                'TOTAL SALES' => $listings->sum('TOTAL SALES') ?? 0.00
            ]
        ];
        $this->createReport(collect(array_merge($summary, $listings->toArray())), $title);
    }

    public function sales_by_product($options)
    {
        $start = data_get($options, 'start');

        $end = data_get($options, 'end');


        $orders = $start ? DB::table('orders')->where('status', '!=', 'cancelled')->whereBetween(
            'created_at',
            [$start, $end]
        )->select('payments')->get()
            : DB::table('orders')->where('status', '!=', 'cancelled')->select('payments')->get();
        $gc_totals = $orders->map->payments->map(function ($payment) {
            $payment = is_array($payment) ? $payment : json_decode($payment);
            return collect(data_get($payment, 'giftCard'))->sum('amount');
        })->sum();


        $products = collect();
        foreach (
            $start ? DB::table('orders')->where('status', '!=', 'cancelled')->whereBetween('created_at', [$start, $end]
            )->select('products')->cursor()
                : DB::table('orders')->where('status', '!=', 'cancelled')->select('products')->cursor() as $order
        ) {
            $products->push(json_decode($order->products));
        }
        $products = $products->flatten(1)->where('product_id')->groupBy('product_id');

        $listings = $products->flatten(1)
            ->where('product_id')
            ->groupBy('product_id')
            ->map(function ($product) use ($gc_totals) {
                return [
                    'TITLE' => data_get($product->first(), 'title'),
                    'SKU' => data_get($product->first(), 'sku'),
                    'TYPE' => data_get($product->first(), 'item_type') ?? 'physical',
                    'VENDOR' => data_get($product->first(), 'vendor'),
                    'QUANTITY' => $product->sum('quantity'),
                    'GROSS SALES' => $product->sum('total') ?? 0.00,
                    'DISCOUNTS' => $product->sum('discount_amount') ?? 0.00,
                    'NET SALES' => $net_sales = $product->sum(function ($item) {
                        return data_get($item, 'total') - data_get($item, 'discount_amount');
                    }) ?? 0.00,
                    // 'NET SALES WITH OUT GC' => $net_sales - $gc_totals,
                    'TAX' => $product->sum('tax_amount') ?? 0.00,
                    'TOTAL SALES' => $product->sum(function ($item) {
                            return (data_get($item, 'total') - data_get($item, 'discount_amount')) + data_get(
                                    $item,
                                    'tax_amount'
                                );
                        }) ?? 0.00
                ];
            });
        $summary = [
            [
                'TITLE' => 'SUMMARY',
                'SKU' => '',
                'TYPE' => '',
                'VENDOR' => '',
                'QUANTITY' => $listings->sum('QUANTITY'),
                'GROSS SALES' => $listings->sum('GROSS SALES') ?? 0.00,
                'DISCOUNTS' => $listings->sum('DISCOUNTS') ?? 0.00,
                'NET SALES' => $listings->sum('NET SALES') ?? 0.00,
                // 'NET SALES WITH OUT GC' => $listings->sum('NET SALES WITH OUT GC') ?? 0.00,
                'TAX' => $listings->sum('TAX') ?? 0.00,
                'TOTAL SALES' => $listings->sum('TOTAL SALES') ?? 0.00
            ]
        ];
        $this->createReport(collect(array_merge($summary, $listings->toArray())), data_get($options, 'title'));
    }

    public function sales_by_vendor($options)
    {
        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $orders = $start ? Order::setEagerLoads([])->whereBetween('created_at', [$start, $end]) : Order::setEagerLoads(
            []
        );

        $orders = $orders->where('status', '!=', 'cancelled')->get(['products', 'payments']);

        $gc_totals = $orders->map->payments->map(function ($payment) {
            $payment = is_array($payment) ? $payment : json_decode($payment);
            return collect(data_get($payment, 'giftCard'))->sum('amount');
        })->sum();

        $products = $orders //  ->whereJsonLength('product_ids', '>', 0)
        ->pluck('products')
            ->flatten(1)
            ->where('product_id');

        $vendors = $products->pluck('vendor')->filter()->unique();

        $listings = $vendors->map(function ($vendor) use ($products, $gc_totals) {
            $sales = $products->where('vendor', '=', $vendor);
            return [
                'VENDOR' => $vendor,
                'QUANTITY' => $sales->sum('quantity'),
                'GROSS SALES' => $sales->sum('total') ?? 0.00,
                'DISCOUNTS' => $sales->sum('discount_amount') ?? 0.00,
                'NET SALES' => $net_sales = $sales->sum(function ($item) {
                    return $item['total'] - $item['discount_amount'];
                }) ?? 0.00,
                // 'NET SALES WITH OUT GC' => $net_sales - $gc_totals,
                'TAX' => $sales->sum('tax_amount') ?? 0.00,
                'TOTAL SALES' => $sales->sum(function ($item) {
                        return ($item['total'] - $item['discount_amount']) + $item['tax_amount'];
                    }) ?? 0.00
            ];
        });
        $summary = [
            [
                'VENDOR' => 'SUMMARY',
                'NET QUANTITY' => $listings->sum('QUANTITY'),
                'GROSS SALES' => $listings->sum('GROSS SALES') ?? 0.00,
                'DISCOUNTS' => $listings->sum('DISCOUNTS') ?? 0.00,
                'NET SALES' => $listings->sum('NET SALES') ?? 0.00,
                // 'NET SALES WITH OUT GC' => $listings->sum('NET SALES WITH OUT GC') ?? 0.00,
                'TAX' => $listings->sum('TAX') ?? 0.00,
                'TOTAL SALES' => $listings->sum('TOTAL SALES') ?? 0.00
            ]
        ];
        $this->createReport(collect(array_merge($summary, $listings->toArray())), data_get($options, 'title'));
    }

    public function sales_by_variation($options)
    {
        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $orders = $start ? Order::setEagerLoads([])->whereBetween('created_at', [$start, $end]) : Order::setEagerLoads(
            []
        );

        $orders = $orders->where('status', '!=', 'cancelled')->get(['products', 'payments']);

        $gc_totals = $orders->map->payments->map(function ($payment) {
            $payment = is_array($payment) ? $payment : json_decode($payment);
            return collect(data_get($payment, 'giftCard'))->sum('amount');
        })->sum();

        $variations = $orders // ->whereRaw('JSON_CONTAINS(products->"$[*]",JSON_OBJECT("type","variation"))')
        ->pluck('products')
            ->flatten(1)
            ->where('type', '=', 'variation');

        $variationIds = $variations->pluck('id')->unique();
        $listings = $variationIds->map(function ($variationId) use ($variations, $gc_totals) {
            $sales = $variations->where('id', '=', $variationId);
            return [
                'TITLE' => $sales->first()['title'],
                'SKU' => $sales->first()['sku'],
                'QUANTITY' => $sales->sum('quantity'),
                'GROSS SALES' => $sales->sum('total') ?? 0.00,
                'DISCOUNTS' => $sales->sum('discount_amount') ?? 0.00,
                'NET SALES' => $net_sales = $sales->sum(function ($item) {
                    return $item['total'] - $item['discount_amount'];
                }) ?? 0.00,
                // 'NET SALES WITH OUT GC' => $net_sales - $gc_totals,
                'TAX' => $sales->sum('tax_amount') ?? 0.00,
                'TOTAL SALES' => $sales->sum(function ($item) {
                        return ($item['total'] - $item['discount_amount']) + $item['tax_amount'];
                    }) ?? 0.00
            ];
        });

        $summary = [
            [
                'TITLE' => 'SUMMARY',
                'SKU' => '',
                'NET QUANTITY' => $listings->sum('QUANTITY') ?? 0.00,
                'GROSS SALES' => $listings->sum('GROSS SALES') ?? 0.00,
                'DISCOUNTS' => $listings->sum('DISCOUNTS') ?? 0.00,
                'NET SALES' => $listings->sum('NET SALES'),
                // 'NET SALES WITH OUT GC' => $listings->sum('NET SALES WITH OUT GC') ?? 0.00,
                'TAX' => $listings->sum('TAX') ?? 0.00,
                'TOTAL SALES' => $listings->sum('TOTAL SALES') ?? 0.00
            ]
        ];
        $this->createReport(collect(array_merge($summary, $listings->toArray())), data_get($options, 'title'));
    }

    public function sales_by_discount($options)
    {
        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $orders = $start ? Order::setEagerLoads([])->whereBetween('created_at', [$start, $end]) : Order::setEagerLoads(
            []
        );

        $orders = $orders->where('status', '!=', 'cancelled')
            ->where('discount_id', '!=', null)
            ->get(
                [
                    'discount_id',
                    'discount',
                    'sub_total',
                    'discount_amount',
                    'grand_total',
                    'tax_amount',
                    'shipping_amount',
                    'shipping',
                    'payments'
                ]
            );


        $gc_totals = $orders->map->payments->map(function ($payment) {
            $payment = is_array($payment) ? $payment : json_decode($payment);
            return collect(data_get($payment, 'giftCard'))->sum('amount');
        })->sum();

        $discountIds = $orders->pluck('discount_id')->unique();

        $listings = $discountIds->map(function ($discountId) use ($orders, $gc_totals) {
            $sales = $orders->where('discount_id', $discountId);
            $discount = $sales->first()->discount;
            return [
                'NAME' => data_get($discount, 'name'),
                'DISCOUNT APPLIED' => data_get($discount, 'automated') ? 'Automated' : 'Code',
                'TYPE' => data_get($discount, 'type'),
                'ORDERS' => $sales->count(),
                'GROSS SALES' => $sales->sum('sub_total') ?? 0.00,
                'DISCOUNT AMOUNT' => $sales->sum('discount_amount') ?? 0.00,
                'NET SALES' => $net_sales = $sales->sum('grand_total') - $sales->sum('tax_amount') - $sales->sum(
                    'shipping_amount'
                ) ?? 0.00,
                'NET SALES WITH OUT GC' => $net_sales - $gc_totals,
                'SHIPPING' => $sales->sum(function ($sale) {
                        return data_get($sale, 'shippingType.price');
                        // return $sale->shipping['shippingType']['price'];
                    }) ?? 0.00,
                'SHIPPING AMOUNT' => data_get($discount, 'type') == 'freeShipping' ? $sales->sum(
                    'discount_amount'
                ) : 0.00,
                'TAX' => $sales->sum('tax_amount') ?? 0.00,
                'TOTAL SALES' => $sales->sum('grand_total') ?? 0.00
            ];
        });
        $summary = [
            [
                'NAME' => 'SUMMARY',
                'DISCOUNT APPLIED' => '',
                'TYPE' => '',
                'ORDERS' => $listings->sum('ORDERS'),
                'GROSS SALES' => $listings->sum('GROSS SALES') ?? 0.00,
                'DISCOUNT AMOUNT' => $listings->sum('DISCOUNT AMOUNT') ?? 0.00,
                'NET SALES' => $listings->sum('NET SALES') ?? 0.00,
                'NET SALES WITH OUT GC' => $listings->sum('NET SALES WITH OUT GC') ?? 0.00,
                'SHIPPING' => $listings->sum('SHIPPING') ?? 0.00,
                'SHIPPING AMOUNT' => $listings->sum('SHIPPING AMOUNT') ?? 0.00,
                'TAX' => $listings->sum('TAX') ?? 0.00,
                'TOTAL SALES' => $listings->sum('TOTAL SALES') ?? 0.00
            ]
        ];
        $this->createReport(collect(array_merge($summary, $listings->toArray())), data_get($options, 'title'));
    }

    public function sales_by_customer($options)
    {
        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $orders = $start ? DB::table('orders')
            ->where('status', '!=', 'cancelled')
            ->whereBetween('created_at', [$start, $end])
            ->select(['customer_id', 'sub_total', 'grand_total', 'tax_amount', 'shipping_amount', 'payments'])
            ->get()
            :
            DB::table('orders')
                ->where('status', '!=', 'cancelled')
                ->select(['customer_id', 'sub_total', 'grand_total', 'tax_amount', 'shipping_amount', 'payments'])
                ->get();

        $listings = $orders->groupBy('customer_id')->map(function ($orders, $key) {
            $customer = DB::table('customers')->select(['name', 'id', 'email'])->where('id', $key)->first();

            $gc_totals = $orders->map->payments->map(function ($payment) {
                $payment = is_array($payment) ? $payment : json_decode($payment);
                return collect(data_get($payment, 'giftCard'))->sum('amount');
            })->sum();

            return [
                'NAME' => data_get($customer, 'name'),
                'EMAIL' => data_get($customer, 'email'),
                'ORDERS' => $orders->count(),
                'GROSS SALES' => $orders->sum('sub_total') ?? 0.00,
                'NET SALES' => $net_sales = $orders->sum('grand_total') - $orders->sum('tax_amount') - $orders->sum(
                    'shipping_amount'
                ) ?? 0.00,
                'NET SALES WITH OUT GC' => $net_sales - $gc_totals,
                'TOTAL SALES' => $orders->sum('grand_total') ?? 0.00
            ];
        });
        $summary = [
            [
                'NAME' => 'SUMMARY',
                'EMAIL' => '',
                'ORDERS' => $listings->sum('ORDERS'),
                'GROSS SALES' => $listings->sum('GROSS SALES') ?? 0.00,
                'NET SALES' => $listings->sum('NET SALES') ?? 0.00,
                'NET SALES WITH OUT GC' => $listings->sum('NET SALES WITH OUT GC') ?? 0.00,
                'TOTAL SALES' => $listings->sum('TOTAL SALES') ?? 0.00
            ]
        ];

        $this->createReport(collect(array_merge($summary, $listings->toArray())), data_get($options, 'title'));
    }

    public function sales_by_location($options)
    {
        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $locationGroup = data_get($options, 'groupBy');

        $orders = $start ? Order::setEagerLoads([])->whereBetween('created_at', [$start, $end]) : Order::setEagerLoads(
            []
        );

        $orders = $orders->where('status', '!=', 'cancelled')
            ->get(
                [
                    'shipping',
                    'sub_total',
                    'grand_total',
                    'shipping_amount',
                    'tax_amount',
                    'discount_amount',
                    'discount',
                    'payments'
                ]
            );


        $gc_totals = $orders->map->payments->map(function ($payment) {
            $payment = is_array($payment) ? $payment : json_decode($payment);
            return collect(data_get($payment, 'giftCard'))->sum('amount');
        })->sum();

        // $orders = $orders->whereJsonLength('shipping', '>', 0);
        if ($locationGroup != 'shipping_zone') {
            $groupedOrders = $orders->groupBy(function ($order) use ($locationGroup) {
                if ($locationGroup == 'state') {
                    return data_get($order, "shipping.shippingInfo.{$locationGroup}") . ', ' . data_get(
                            $order,
                            'shipping.shippingInfo.country'
                        );
                }
                return data_get($order, "shipping.shippingInfo.{$locationGroup}");
            });
        } else {
            $shippingZones = $this->getShippingZones(
                $orders->pluck('shipping.shippingInfo.postal_code')->unique()->values()
            );
            $groupedOrders = $orders->groupBy(function ($order) use ($shippingZones) {
                $postalCode = data_get($order, 'shipping.shippingInfo.postal_code');
                return data_get(
                    $shippingZones->whereIn(
                        'zip_code',
                        [$postalCode, substr($postalCode, 0, 4), substr($postalCode, 0, 3)]
                    )->sortByDesc('zip_code')->first(),
                    'shippingZone.name'
                );
            });
        }
        $listings = $groupedOrders->map(function ($group, $key) use ($gc_totals) {
            return [
                'LOCATION' => $key,
                'QUANTITY' => $group->count(),
                'GROSS NET' => $group->sum('sub_total') ?? 0.00,
                'DISCOUNT AMOUNT' => $group->sum(function ($order) {
                        if (data_get($order, 'discount.type') != 'freeShipping') {
                            return data_get($order, 'discount.savings');
                        }
                    }) ?? 0.00,
                'NET SALES' => $net_sales = $group->sum('grand_total') - $group->sum('tax_amount') - $group->sum(
                    'shipping_amount'
                ) ?? 0.00,
                'NET SALES WITH OUT GC' => $net_sales - $gc_totals,
                'SHIPPING' => $group->sum('shipping_amount') ?? 0.00,
                'TAX' => $group->sum('tax_amount') ?? 0.00,
                'TOTAL SALES' => $group->sum('grand_total') ?? 0.00
            ];
        });
        $total = [
            [
                'LOCATION' => 'SUMMARY',
                'QUANTITY' => $listings->sum('QUANTITY'),
                'GROSS NET' => $listings->sum('GROSS NET') ?? 0.00,
                'DISCOUNT AMOUNT' => $listings->sum('DISCOUNT AMOUNT') ?? 0.00,
                'NET SALES' => $listings->sum('NET SALES') ?? 0.00,
                'NET SALES WITH OUT GC' => $listings->sum('NET SALES WITH OUT GC') ?? 0.00,
                'SHIPPING' => $listings->sum('SHIPPING') ?? 0.00,
                'TAX' => $listings->sum('TAX') ?? 0.00,
                'TOTAL SALES' => $listings->sum('TOTAL SALES') ?? 0.00
            ]
        ];

        $this->createReport(
            collect(array_merge($total, $listings->toArray())),
            data_get($options, 'title') . ' Group by ' . Str::title($locationGroup)
        );
    }

    public function getShippingZones($postalCodes)
    {
        $codes = $postalCodes->map(function ($code) {
            return [$code, substr($code, 0, 4), substr($code, 0, 3)];
        })->flatten()->unique();
        return ZipCode::with('shippingZone')->whereIn('zip_code', $codes)->get();
    }

    public function shipping_by_order($options)
    {
        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $orders = $start ? Order::setEagerLoads([])->whereBetween('created_at', [$start, $end]) : Order::setEagerLoads(
            []
        );

        $orders = $orders->where('status', '!=', 'cancelled')->get(['shipping', 'id', 'discount']);

        $codes = $orders->pluck('shipping.shippingInfo.postal_code')
            ->unique()
            ->values()
            ->map(function ($code) {
                return [$code, substr($code, 0, 4), substr($code, 0, 3)];
            })
            ->flatten()
            ->unique();
        $zipcodes = DB::table('zip_codes')->whereIn('zip_code', $codes)->select(
            'id',
            'zip_code',
            'shipping_zone_id'
        )->get();

        $listings = $orders->map(function ($order) use ($zipcodes) {
            $postalCode = data_get($order, 'shipping.shippingInfo.postal_code');

            $shipping_zone_id = data_get(
                $zipcodes->whereIn(
                    'zip_code',
                    [$postalCode, substr($postalCode, 0, 4), substr($postalCode, 0, 3)]
                )->sortByDesc('zip_code')->first(),
                'shipping_zone_id'
            );

            $shippingZone = optional(
                DB::table('shipping_zones')->where('id', $shipping_zone_id)->select('name')->first()
            )->name;


            return [
                'ORDER ID' => $order->id,
                'SHIPPING ZONE' => $shippingZone,
                'SHIPPING OPTION' => data_get($order, 'shipping.shippingType.name'),
                'POSTAL CODE' => $postalCode,
                'PRICE' => data_get($order, 'shipping.shippingType.price') ?? 0.00,
                'COST' => data_get($order, 'shipping.tracking.shipmentCost') ?? 0.00,
                'SHIPPING DISCOUNT' => data_get(
                    $order,
                    'discount.type'
                ) == 'freeShipping' ? $order->discount['savings'] : 0.00,
                'TOTAL' => $order->shipping_amount - data_get($order, 'shipping.tracking.shipmentCost') ?? 0.00
            ];
        });
        $summary = [
            [
                'ORDER ID' => 'SUMMARY',
                'SHIPPING ZONE' => '',
                'SHIPPING OPTION' => '',
                'POSTAL CODE' => '',
                'PRICE' => $listings->sum('PRICE') ?? 0.00,
                'COST' => $listings->sum('COST') ?? 0.00,
                'SHIPPING DISCOUNT' => $listings->sum('SHIPPING DISCOUNT') ?? 0.00,
                'TOTAL' => $listings->sum('TOTAL') ?? 0.00
            ]
        ];


        $this->createReport(collect(array_merge($summary, $listings->toArray())), data_get($options, 'title'));
    }

    public function gift_cards_balance($options)
    {
        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $cards = $start ? GiftCard::setEagerLoads([])->whereBetween('created_at', [$start, $end]
        ) : GiftCard::setEagerLoads([]);

        $cards = $cards->get(['order_id', 'amount', 'balance']);

        $listings = $cards->map(function ($card) {
            return [
                'ORDER' => $card->order_id,
                'AMOUNT' => $card->amount,
                'BALANCE' => $card->balance ?? 0.00
            ];
        });
        $summary = [
            [
                'ORDER' => 'SUMMARY',
                'AMOUNT' => $listings->sum('AMOUNT'),
                'BALANCE' => $listings->sum('BALANCE') ?? 0.00
            ]
        ];


        $this->createReport(collect(array_merge($summary, $listings->toArray())), data_get($options, 'title'));
    }

    public function gift_cards_transactions($options)
    {
        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $dateGroup = data_get($options, 'groupBy');

        $orders = $start ? Order::setEagerLoads([])->whereBetween('created_at', [$start, $end]) : Order::setEagerLoads(
            []
        );

        $order = $orders->where('status', '!=', 'cancelled')->get(['products', 'payments']);

        $listings = $orders->get()
            ->groupBy(function ($key) use ($dateGroup) {
                if ($dateGroup != 'week' && $dateGroup != 'all') {
                    return $key->created_at->format(self::$dateGroups[$dateGroup]);
                } elseif ($dateGroup == 'week') {
                    return $this->weekFilter($key);
                } else {
                    return 'All';
                }
            })
            ->sortBy(function ($group) {
                return $group->first()->created_at;
            })
            ->map(function ($group, $key) {
                $purchases = $group->pluck('products')->flatten(1)->where('type', '=', 'giftCard');
                $payments = $group->pluck('payments')->pluck('giftCard')->filter()->flatten(1);
                return [
                    'DATE' => $key,
                    'PURCHASES' => $purchases->count() ?? 0,
                    'TOTAL PURCHASES' => $purchases->sum('amount') ?? 0.00,
                    'PAYMENTS' => $payments->count() ?? 0,
                    'TOTAL PAYMENTS' => $payments->sum('amount') ?? 0.00
                ];
            });
        $total = [
            [
                'DATE' => 'SUMMARY',
                'PURCHASES' => $listings->sum('PURCHASES') ?? 0,
                'TOTAL PURCHASES' => $listings->sum('TOTAL PURCHASES') ?? 0.00,
                'PAYMENTS' => $listings->sum('PAYMENTS') ?? 0,
                'TOTAL PAYMENTS' => $listings->sum('TOTAL PAYMENTS') ?? 0.00
            ]
        ];

        $this->createReport(
            collect(array_merge($total, $listings->toArray())),
            data_get($options, 'title') . ' Group by ' . Str::title($dateGroup)
        );
    }

    public function shipping_accuracy($options)
    {
        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $orders = $start ? Order::setEagerLoads([])->whereBetween('created_at', [$start, $end]) : Order::setEagerLoads(
            []
        );

        $order = $orders->where('status', '!=', 'cancelled')->where('shipping->tracking->carrier', '<>', null)->get(
            ['id', 'shipping']
        );

        $listing = $orders->get()
            ->map(function ($order) {
                $expected = new Carbon(data_get($order, 'shipping.shippingType.estimated_arrival'));
                $actual = new Carbon(data_get($order, 'shipping.tracking.arrived_at'));
                return [
                    'ORDER' => $order->id,
                    'LOCATION' => data_get($order, 'shipping.shippingInfo.country') . ' - ' . data_get(
                            $order,
                            'shipping.shippingInfo.postal_code'
                        ),
                    'ESTIMATED ARRIVAL' => $expected->format('Y-m-d'),
                    'ACTUAL ARRIVAL' => $actual->format('Y-m-d'),
                    'ACCURATE' => $actual->isSameDay($expected) || $actual->isBefore($expected) ? '1' : '0'
                ];
            });
        $summary = [
            [
                'ORDER' => 'SUMMARY',
                'LOCATION' => '',
                'ESTIMATED ARRIVAL' => '',
                'ACTUAL ARRIVAL' => '',
                'ACCURATE' => $listing->count() ? number_format(
                        ($listing->sum('ACCURATE') / $listing->count() * 100),
                        2
                    ) . '%' : 0
            ]
        ];


        $this->createReport(collect(array_merge($summary, $listing->toArray())), data_get($options, 'title'));
    }

    public function utm_report($options)
    {
        $start = data_get($options, 'start');
        $end = data_get($options, 'end');
        $from = data_get($options, 'from');

        $arr = [
            'App\Customer' => 'Customers',
            'App\Order' => 'Orders',
        ];
        $type = $arr[$from];

        $listings = DB::table('utm')->when($start, function ($query, $start) use ($end) {
            return $query->whereBetween('created_at', [$start, $end]);
        })
            ->where('model_type', $from)
            ->get()
            ->map(function ($utm) use ($type) {
                if ($type == 'Customers') {
                    $customer = DB::table('customers')->select('name', 'email')->where('id', $utm->model_id)->first();
                    return [
                        'CUSTOMER ID' => $utm->model_id,
                        'CUSTOMER NAME' => optional($customer)->name,
                        'CUSTOMER EMAIL' => optional($customer)->email,
                        'UTM SOURCE' => $utm->utm_source,
                        'UTM MEDIUM' => $utm->utm_medium,
                        'UTM CAMPAIGN' => $utm->utm_campaign,
                        'UTM TERM' => $utm->utm_term,
                        'UTM CONTENT' => $utm->utm_content,
                    ];
                }
                return [
                    'ORDER ID' => $utm->model_id,
                    'UTM SOURCE' => $utm->utm_source,
                    'UTM MEDIUM' => $utm->utm_medium,
                    'UTM CAMPAIGN' => $utm->utm_campaign,
                    'UTM TERM' => $utm->utm_term,
                    'UTM CONTENT' => $utm->utm_content,
                ];
            });

        $this->createReport(($listings), $type . ' ' . data_get($options, 'title'));
    }

    public function orders_report($options)
    {
        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $orders = $start ? Order::setEagerLoads([])->whereBetween('created_at', [$start, $end]) : Order::setEagerLoads(
            []
        );

        $orders = $orders
            ->get([
                'id',
                'sub_total',
                'tax_amount',
                'shipping_amount',
                'discount',
                'discount_amount',
                'grand_total',
                'status',
                'payment_status',
                'customer_id',
                'subscription',
                'products',
                'payments'
            ])
            ->map(function ($order) {
                $customer = DB::table('customers')->select('name', 'email')->where('id', $order->customer_id)->first();
                return [
                    'ID' => $order->id,
                    'SUB TOTAL' => $order->sub_total,
                    'TAX AMOUNT' => $order->tax_amount,
                    'SHIPPING AMOUNT' => $order->shipping_amount,
                    'DISCOUNT AMOUNT' => $order->discount_amount,
                    'DISCOUNT NAME' => data_get($order, 'discount.name'),
                    'GRAND TOTAL' => $order->grand_total,
                    'STATUS' => $order->status,
                    'PAYMENT STATUS' => $order->payment_status,
                    'CUSTOMER' => optional($customer)->name,
                    'CUSTOMER EMAIL' => optional($customer)->email,
                    'SUBSCRIPTION' => $order->subscription ? 'Yes' : '',
                    'PRODUCT COUNT' => collect($order->products)->count(),
                    'TOTAL REFUND' => data_get($order, 'payments.refunds_total'),
                    'TOTAL EGIFT REFUND' => data_get($order, 'payments.egift_refunds_total'),
                ];
            });
        $this->createReport($orders, data_get($options, 'title'));
    }

    public function customers_report()
    {
        $customers = Customer::setEagerLoads([])
            ->get(['id', 'name', 'email'])
            ->map(function ($customer) {
                $address = $customer->address;
                return [
                    'ID' => $customer->id,
                    'NAME' => $customer->name,
                    'EMAIL' => $customer->email,
                    'PHONE' => data_get($address, 'phone'),
                    'ADDRESS LINE 1' => data_get($address, 'address_line_1'),
                    'ADDRESS LINE 2' => data_get($address, 'address_line_2'),
                    'CITY' => data_get($address, 'city'),
                    'STATE' => data_get($address, 'state'),
                    'POSTAL CODE' => data_get($address, 'postal_code'),
                    'COUNTRY' => data_get($address, 'country'),
                ];
            });
        $this->createReport($customers, 'Customers Export');
    }

    public function categories_report()
    {
        $categories = DB::table('categories')->select(['id', 'name'])->get()->map(function ($category) {
            return [
                'ID' => $category->id,
                'NAME' => $category->name,
                'PRODUCTS' => DB::table('category_product')->select('category_id')->where(
                    'category_id',
                    $category->id
                )->count()
            ];
        });
        $this->createReport($categories, 'Categories Export');
    }

    public function creators_report()
    {
        $creators = DB::table('creators')->select(['id', 'name', 'type', 'description', 'heb_name'])->get()->map(
            function ($creator) {
                return [
                    'ID' => $creator->id,
                    'NAME' => $creator->name,
                    'HEBREW NAME' => $creator->heb_name,
                    'TYPE' => $creator->type,
                    'DESCRIPTION' => strip_tags($creator->description),
                    'PRODUCTS' => DB::table('creator_product')->select('creator_id')->where(
                        'creator_id',
                        $creator->id
                    )->count(),
                    'FOLLOWS' => DB::table('follows')->select('model_id', 'model_type')->where(
                        'model_type',
                        'App\Creator'
                    )->where('model_id', $creator->id)->count(),
                ];
            }
        );
        $this->createReport($creators, 'Creators Export');
    }

    public function vendors_report()
    {
        $vendors = DB::table('vendors')->select(['id', 'name', 'description'])->get()->map(function ($vendor) {
            return [
                'ID' => $vendor->id,
                'NAME' => $vendor->name,
                'DESCRIPTION' => strip_tags($vendor->description),
                'PRODUCTS' => DB::table('products')->select('vendor_id')->where('vendor_id', $vendor->id)->count(),
                'FOLLOWS' => DB::table('follows')->select('model_id', 'model_type')->where(
                    'model_type',
                    'App\Vendor'
                )->where('model_id', $vendor->id)->count(),
            ];
        });
        $this->createReport($vendors, 'Vendors Export');
    }

    public function returns_report($options)
    {
        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $returns = collect();
        foreach (
            $start ? DB::table('returns')->whereBetween('created_at', [$start, $end])->cursor() : DB::table(
                'returns'
            )->cursor() as $return
        ) {
            $customerId = DB::table('orders')->where('id', $return->order_id)->select('id', 'customer_id')->first(
            )->customer_id;
            $customer = DB::table('customers')->where('id', $customerId)->select('id', 'name', 'email')->first();
            $returns->push([
                'ID' => $return->id,
                'ORDER ID' => $return->order_id,
                'SUB REFUND' => $return->sub_total,
                'TAX REFUND' => $return->tax_amount,
                'SHIPPING REFUND' => $return->shipping_amount,
                'DISCOUNT REFUND' => $return->discount_amount,
                'GRAND REFUND' => $return->grand_total,
                'STATUS' => $return->status,
                'CUSTOMER' => optional($customer)->name,
                'CUSTOMER EMAIL' => optional($customer)->email,
                'PRODUCT COUNT' => collect(json_decode($return->products))->count(),
                'DATE' => $return->created_at,
            ]);
        }

        $summary = [
            [
                'ID' => '',
                'ORDER ID' => '',
                'SUB REFUND' => $returns->sum('SUB REFUND'),
                'TAX REFUND' => $returns->sum('TAX REFUND'),
                'SHIPPING REFUND' => $returns->sum('SHIPPING REFUND'),
                'DISCOUNT REFUND' => $returns->sum('DISCOUNT REFUND'),
                'GRAND REFUND' => $returns->sum('GRAND REFUND'),
                'STATUS' => '',
                'CUSTOMER' => '',
                'CUSTOMER EMAIL' => '',
                'PRODUCT COUNT' => $returns->sum('PRODUCT COUNT'),
                'DATE' => ''
            ]
        ];

        $this->createReport(collect(array_merge($summary, $returns->toArray())), data_get($options, 'title'));
    }

    public function returns_by_product($options)
    {
        $start = data_get($options, 'start');

        $end = data_get($options, 'end');

        $products = collect();
        foreach (
            $start ? DB::table('returns')->whereBetween('created_at', [$start, $end])->select('products')->cursor()
                : DB::table('returns')->select('products')->cursor() as $return
        ) {
            $products->push(json_decode($return->products));
        }
        $products = $products->flatten(1)->where('product_id')->groupBy('product_id');

        $listings = $products->flatten(1)
            ->where('product_id')
            ->groupBy('product_id')
            ->map(function ($product) {
                return [
                    'TITLE' => data_get($product->first(), 'title'),
                    'SKU' => data_get($product->first(), 'sku'),
                    'TYPE' => data_get($product->first(), 'item_type') ?? 'physical',
                    'VENDOR' => data_get($product->first(), 'vendor'),
                    'QUANTITY' => $product->sum('quantity'),
                    'REFUND' => $product->sum('total') ?? 0.00,
                    'DISCOUNTS' => $product->sum('discount_amount') ?? 0.00,
                    'TAX' => $product->sum('tax_amount') ?? 0.00,
                    'REFUND TOTAL' => $product->sum('refund_total') ?? 0.00,
                ];
            });
        $summary = [
            [
                'TITLE' => 'SUMMARY',
                'SKU' => '',
                'TYPE' => '',
                'VENDOR' => '',
                'QUANTITY' => $listings->sum('QUANTITY'),
                'REFUND' => $listings->sum('REFUND') ?? 0.00,
                'DISCOUNTS' => $listings->sum('DISCOUNTS') ?? 0.00,
                'TAX' => $listings->sum('TAX') ?? 0.00,
                'REFUND TOTAL' => $listings->sum('REFUND TOTAL') ?? 0.00
            ]
        ];
        $this->createReport(collect(array_merge($summary, $listings->toArray())), data_get($options, 'title'));
    }

    public function products_report($options)
    {
        $name = $this->generateTitle($options);
        dispatch(function () use ($options, $name) {
            (new FastExcel($this->getProductsOneByOne($options)))->export(
                storage_path("app/{$name}.csv")
            ); //->export('storage/app/exports.csv');
            (new ReportsController)->create($name);
        });
    }

    public function generateTitle($options)
    {
        $title = '';

        if (data_get($options, 'active') == 1) {
            $title .= 'Active ';
        } elseif (data_get($options, 'active') == 0 && data_get($options, 'active') != null) {
            $title .= 'Non Active ';
        }


        $title .= 'Products ';

        if (data_get($options, 'categories')) {
            $title .= 'For Categories: ';
            collect(data_get($options, 'categories'))->each(function ($category) use (&$title) {
                $title .= DB::table('categories')->select('name')->where('id', $category)->first()->name . ', ';
            });
        }


        if (data_get($options, 'vendors')) {
            $title .= 'For Vendors: ';
            collect(data_get($options, 'vendors'))->each(function ($vendor) use (&$title) {
                $title .= DB::table('vendors')->select('name')->where('id', $vendor)->first()->name . ', ';
            });
        }

        $title .= 'Export';

        return Str::limit($title, 200);
    }

    public function getProductsOneByOne($options)
    {
        $active = data_get($options, 'active');

        $categories = DB::table('categories')
            ->select('name')
            ->whereIn('id', data_get($options, 'categories') ?? [])
            ->pluck('name');

        $eachChunk = 200;
        $productCount = Product::count();
        $timesToLoop = $productCount / $eachChunk;

        for ($i = 0; $i <= $timesToLoop; $i++) {
            $products = DB::table('products')
                ->select('*')
                ->skip($i * $eachChunk)
                ->take($eachChunk)
                ->cursor();

            $all_categories = DB::table('category_product')
                ->whereIn('product_id', $products->pluck('id'))
                ->get();

            $all_filter_items = DB::table('filter_item_product')
                ->whereIn('product_id', $products->pluck('id'))
                ->get();

            foreach ($products as $product) {
                if ((data_get($options, 'active') === null || ($active == data_get($options, 'active')))
                    && (!data_get($options, 'vendors') || collect(data_get($options, 'vendors'))->contains(
                            data_get($product, 'vendor_id')
                        ))
                    && (!data_get($options, 'categories') || $categories->intersect(
                            collect(data_get(json_decode($product->search), 'categories'))
                        )->count())
                ) {
                    yield [
                        'ID' => data_get($product, 'id'),
                        'TITLE' => data_get($product, 'title'),
                        'DESCRIPTION' => strip_tags(data_get($product, 'description')),
                        'SHORT DESCRIPTION' => strip_tags(data_get($product, 'short_desc')),
                        'HEBREW TITLE' => data_get($product, 'heb_title'),
                        'HEBREW DESCRIPTION' => strip_tags(data_get($product, 'heb_description')),
                        'HEBREW SHORT DESCRIPTION' => strip_tags(data_get($product, 'heb_short_desc')),
                        'LIST PRICE' => data_get($product, 'list_price'),
                        'STORE PRICE' => data_get($product, 'store_price'),
                        'ONLINE PRICE' => data_get($product, 'online_price'),
                        'SALE PRICE' => data_get($product, 'sale_price'),
                        'STORE QUANTITY' => data_get($product, 'store_quantity'),
                        'WEBSITE QUANTITY' => data_get($product, 'website_quantity'),
                        'BARCODE' => data_get($product, 'barcode'),
                        'SKU' => data_get($product, 'sku'),
                        'WIDTH' => data_get($product, 'width'),
                        'HEIGHT' => data_get($product, 'height'),
                        'LENGTH' => data_get($product, 'length'),
                        'WEIGHT' => data_get($product, 'weight'),
                        'ORIGIN' => data_get($product, 'origin'),
                        'SYSTEM CODE' => data_get($product, 'system_code'),
                        'TRACK INVENTORY' => data_get($product, 'track_inventory'),
                        'VENDOR ID' => data_get($product, 'vendor_id'),
                        'LABEL ID' => data_get($product, 'label_id'),
                        'CATEGORY IDS' => $all_categories
                            ->where('product_id', data_get($product, 'id'))
                            ->pluck('category_id')->implode(','),
                        'FILTER IDS' => $all_filter_items
                            ->where('product_id', data_get($product, 'id'))
                            ->pluck('filter_item_id')->implode(','),
                        'ITEM TYPE' => data_get($product, 'item_type'),
                        'TAX CODE' => data_get($product, 'tax_code'),
                        'PUBLISH' => data_get($product, 'publish'),
                        'VISIBILITY' => data_get($product, 'visibility'),
                        'EXCLUDED FROM FREE SHIPPING' => data_get($product, 'exclude_free_shipping'),
                        'PICTURE' => data_get(json_decode($product->search), 'image'),
                    ];
                }
            }
        }
    }
}
