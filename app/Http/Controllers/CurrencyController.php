<?php

namespace App\Http\Controllers;

use App\Country;
use App\InternationalShippingZone;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class CurrencyController extends Controller
{
    public static function GetAllCurrencies()
    {
        return Cache::tags(['countries'])->remember("countries", 60 * 60 * 24 * 7, function () {
            $result = file_get_contents('https://api.exchangerate-api.com/v4/latest/USD');

            if (false !== $result) {
                try {
                    $result = forceArray($result)['rates'];

                    $countries = Country::whereIn('currency', collect($result)->keys())->get();

                    $countries = $countries->map(function ($c) {
                        if ($c->active()) {
                            return $c;
                        }
                    })->filter();

                    $currencys = collect($result)->map(function ($value, $key) use ($countries) {
                        $country = $countries->where('currency', $key)->first();
                        return [
                            'name' => data_get($country, 'currency_name'),
                            'currency' => $key,
                            'symbol' => data_get($country, 'symbol'),
                            'rate' => $value
                        ];
                    });
                    return $currencys->where('name', '!=', null)->values();
                } catch (Exception $e) {
                    return [];
                }
            }
        });
    }

    public static function GetAllCountries($active = true, $id = null)
    {
        return Cache::tags(['countries'])->remember(
            "currencies_{$active}_{$id}",
            60 * 60 * 24 * 7,
            function () use ($active, $id) {
                return Country::all()->map(function ($country) use ($active, $id) {
                    if ($country->active() == $active || $country->code == 'US' || ($id && Str::contains(
                                InternationalShippingZone::find($id)->countries_text,
                                $country->code
                            ))) {
                        return [
                            'label' => $country['name'],
                            'value' => $country['code']
                        ];
                    }
                })->sortBy('label')->filter()->values();
            }
        );
    }

}
