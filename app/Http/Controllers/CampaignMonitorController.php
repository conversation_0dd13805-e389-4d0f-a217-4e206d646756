<?php

namespace App\Http\Controllers;

use App\Emails\AddSubscriber;
use Illuminate\Http\Request;

class CampaignMonitorController extends Controller
{
    public static function AddToSubscribers($order)
    {
        if (!data_get($order, 'shipping.shippingInfo.receivePromotions')) {
            return;
        }
        $customer = $order->customer;
        self::subscribeCustomer($customer, 'Order');
    }

    public static function subscribeCustomer($customer, $source = 'Admin')
    {
        $address = optional($customer->addresses)->first();
        $subscriber = array(
            'EmailAddress' => $customer->email,
            'Name' => $customer->name,
            'CustomFields' => array(
                array(
                    'Key' => 'Source',
                    'Value' => $source
                ),
                array(
                    'Key' => 'City',
                    'Value' => data_get($address, 'city')
                ),
                array(
                    'Key' => 'Postal Code',
                    'Value' => data_get($address, 'postal_code')
                ),
                array(
                    'Key' => 'State',
                    'Value' => data_get($address, 'state')
                ),
                array(
                    'Key' => 'Country',
                    'Value' => data_get($address, 'country')
                )
            ),
            'ConsentToTrack' => 'yes',
            'Resubscribe' => true
            //   'RestartSubscriptionBasedAutoResponders' => Whether we should restart subscription based auto responders which are sent when the subscriber first subscribes to a list.
        );
        return json_encode((new AddSubscriber)->add($subscriber));
    }

    public static function subscribedStatus($email)
    {
        return data_get(forceArray(json_encode((new AddSubscriber)->get($email))), 'response.State') ?? '';
    }
}
