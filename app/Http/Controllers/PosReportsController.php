<?php

namespace App\Http\Controllers;

use App\Jobs\Miscellaneous;
use App\Product;
use App\Setting;
use App\VariationInfo;
use Illuminate\Support\Facades\DB;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Cache;

class PosReportsController extends Controller
{
    public static function Reports()
    {
        $updated = Setting::get()->firstWhere('key', 'inventory_updated_at');

        $updated_at = $updated->updated_at;
        $now = now();

        $client = new Client;
        $response = $client->post("https://api5.firstchoicepos.com/v1/user/Authenticate", [
            'json' => [
                "Username" => 'EichlersAPI',
                "Password" => 'eichlersOnline',
                "EnterpriseSid" => '0256b8b4-31e4-4a77-99d3-4a8ce967acdf',
            ]
        ]);
        $result = $response->getBody()->getContents();
        $token = forceArray($result)['Token'];

        $response = $client->post("https://api5.firstchoicepos.com/v1/Report/Read", [
            'headers' => ['Authorization' => 'Bearer ' . $token],
            'json' => [
                'ReportKey' => 'API_Items',
                'Parameters' => [
                    'ModifiedAfter' => $updated_at->toDateTimeString(),
                ]
            ]
        ]);
        $results = $response->getBody()->getContents();
        $results = collect(forceArray($results)['Results']);

        $array = [];

        collect($results)->each(function ($result) use (&$array) {
            $array[] = [
                'sku' => data_get($result, 'LookupCode'),
                'quantity' => data_get($result, 'OnHandStore'),
                'online_quantity' => data_get($result, 'OnHandOnline'),
                'type' => 'get',
                'result' => json_encode($result),
                'created_at' => now()
            ];
        });
        $arrayChunk = collect($array)->chunk(1000);
        $arrayChunk->each(function ($items) {
          \DB::table('inventory_track')->insert($items->toArray());
        });
        // return [
        //   'count' => $results->count(),
        //   'results' => $results
        // ];
        $chunks = $results->chunk(50);

        $chunks->each(function ($result) {
            Miscellaneous::dispatch(PosReportsController::class, 'resolveResults', $result);
        });

        $updated->update([
            'updated_at' => $now
        ]);
    }

    public function resolveResults($results)
    {
        // \Log::channel('pos-inventory')->info($results->map->LookupCode);

        $updateIds = collect();
        $deleteIds = collect();
        $variation_productIds = collect();

        $results->each(function ($result) use (&$updateIds, &$deleteIds, &$variation_productIds) {
            $array = [
                'store_quantity' => 'OnHandStore',
                'website_quantity' => 'OnHandOnline',
                'store_price' => 'StorePrice',
                'cost_price' => 'CostPrice',
                'barcode' => 'Barcode',
                'store_title' => 'ProductTitle',
                'store_category' => 'Catergory',
                'store_sub_category' => 'SubCategory',
                'store_vendor' => 'vendor',
            ];
            $data = [];
            foreach ($array as $key => $value) {
                if (array_key_exists($value, $result)) {
                    $data[$key] = data_get($result, $value);
                }
            }
            $data['pos_meta'] = [
                'updated_at' => now()->toDateTimeString(),
                'result' => $result,
                'type' => 'updated'
            ];
            VariationInfo::setEagerLoads([])->where('sku', $result['LookupCode'])->update($data);
            Product::setEagerLoads([])->where('sku', $result['LookupCode'])->update($data);


            // \App\VariationInfo::setEagerLoads([])->where('sku', $result['LookupCode'])->update([
            //   'store_quantity' => data_get($result, 'OnHandStore'),
            //   'website_quantity' => data_get($result, 'OnHandOnline'),
            //   'store_price' => data_get($result, 'StorePrice'),
            //   'cost_price' => data_get($result, 'CostPrice'),
            //   'barcode' => data_get($result, 'Barcode'),
            //   'store_title' => data_get($result, 'ProductTitle'),
            //   'pos_meta' => [
            //     'updated_at' => now()->toDateTimeString(),
            //     'result' => $result,
            //     'type' => 'updated'
            //   ]
            // ]);


            // \App\Product::setEagerLoads([])->where('sku', $result['LookupCode'])->update([
            //   'store_quantity' => data_get($result, 'OnHandStore'),
            //   'website_quantity' => data_get($result, 'OnHandOnline'),
            //   'store_price' => data_get($result, 'StorePrice'),
            //   'cost_price' => data_get($result, 'CostPrice'),
            //   'barcode' => data_get($result, 'Barcode'),
            //   'store_title' => data_get($result, 'ProductTitle'),
            //   'pos_meta' => [
            //     'updated_at' => now()->toDateTimeString(),
            //     'result' => $result,
            //     'type' => 'updated'
            //   ]
            // ]);


            $updateIds->push(
                Product::setEagerLoads([])->isSearchable()->where('sku', $result['LookupCode'])->get(['id'])->map->id
            );

            $variation_productIds->push(
                DB::table('variation_infos')->where('sku', $result['LookupCode'])->select(['product_id'])->get(
                )->map->product_id
            );

            $updateIds->push(
                Product::whereIn('id', $variation_productIds->flatten()->unique())->isSearchable()->get(['id']
                )->map->id
            );
            $deleteIds->push(
                Product::whereIn('id', $variation_productIds->flatten()->unique())->isunsearchable()->get(['id']
                )->map->id
            );

            $deleteIds->push(
                Product::setEagerLoads([])->isunsearchable()->where('sku', $result['LookupCode'])->get(['id']
                )->map->id
            );
        });

        collect($updateIds)->each(function ($id) {
            Cache::forget("product_{$id}");
        });
        collect($deleteIds)->each(function ($id) {
            Cache::forget("product_{$id}");
        });

        $updateIds = $updateIds->flatten()->unique()->values()->toArray();
        $deleteIds = $deleteIds->flatten()->unique()->values()->toArray();

        dispatch(function () use ($updateIds, $deleteIds) {
            Product::find($updateIds)->map->updateSearchFeild();
            // idsToSwiftype($updateIds);
            // idsToSwiftype($deleteIds, true);
        });
    }


    public static function resolveSalePriceResults($results)
    {
        $updateIds = collect();
        $variation_productIds = collect();

        $results->each(function ($result) use (&$updateIds, &$variation_productIds) {
            VariationInfo::setEagerLoads([])->where('sku', $result['LookupCode'])->update([
                'sale_price' => data_get($result, 'sale_price'),
                'pos_meta->sale' => [
                    'updated_at' => now()->toDateTimeString(),
                    'result' => $result,
                ]
            ]);

            Product::setEagerLoads([])->where('sku', $result['LookupCode'])->update([
                'sale_price' => data_get($result, 'sale_price'),
                'pos_meta->sale' => [
                    'updated_at' => now()->toDateTimeString(),
                    'result' => $result,
                ]
            ]);


            $updateIds->push(
                Product::setEagerLoads([])->isSearchable()->where('sku', $result['LookupCode'])->get(['id'])->map->id
            );

            $variation_productIds->push(
                DB::table('variation_infos')->where('sku', $result['LookupCode'])->select(['product_id'])->get(
                )->map->product_id
            );

            $updateIds->push(
                Product::whereIn('id', $variation_productIds->flatten()->unique())->isSearchable()->get(['id']
                )->map->id
            );
        });

        collect($updateIds)->each(function ($id) {
            Cache::forget("product_{$id}");
        });

        $updateIds = $updateIds->flatten()->unique()->values()->toArray();
        dispatch(function () use ($updateIds) {
            Product::find($updateIds)->map->updateSearchFeild();
            // idsToSwiftype($updateIds);
        });
    }


    public static function GetProductInfo($product)
    {
        try {
            $client = new Client;
            $response = $client->post("https://api5.firstchoicepos.com/v1/user/Authenticate", [
                'json' => [
                    "Username" => 'EichlersAPI',
                    "Password" => 'eichlersOnline',
                    "EnterpriseSid" => '0256b8b4-31e4-4a77-99d3-4a8ce967acdf',
                ]
            ]);
            $result = $response->getBody()->getContents();
            $token = forceArray($result)['Token'];

            $response = $client->post("https://api5.firstchoicepos.com/v1/Report/Read", [
                'headers' => ['Authorization' => 'Bearer ' . $token],
                'json' => [
                    'ReportKey' => 'API_Items',
                    'Parameters' => [
                        'SKU' => $product->sku
                    ]
                ]
            ]);
            $results = $response->getBody()->getContents();
            $results = collect(forceArray($results)['Results']);

            $result = collect($results)->first();

            $product->update([
                'store_quantity' => data_get($result, 'OnHandStore'),
                'website_quantity' => data_get($result, 'OnHandOnline'),
                'store_price' => data_get($result, 'StorePrice'),
                // 'list_price' => data_get($result, 'ListPrice'),
                'cost_price' => data_get($result, 'CostPrice'),
                'barcode' => data_get($result, 'Barcode'),
                'store_vendor' => data_get($result, 'vendor'),
                'store_title' => data_get($result, 'ProductTitle'),
                'store_category' => data_get($result, 'Catergory'),
                'store_sub_category' => data_get($result, 'SubCategory'),
                'pos_meta' => [
                    'updated_at' => now()->toDateTimeString(),
                    'result' => $result,
                    'type' => 'created'
                ]
            ]);

            DB::table('inventory_track')->insert([
                'sku' => data_get($result, 'LookupCode'),
                'quantity' => data_get($result, 'OnHandStore'),
                'online_quantity' => data_get($result, 'OnHandOnline'),
                'type' => 'update',
                'result' => json_encode($result),
                'created_at' => now()
            ]);
        } catch (Exception $ex) {
        }
    }
}
