<?php

namespace App\Http\Controllers;

use App\Personalize;
use Illuminate\Http\Request;

class PersonalizeController extends Controller
{
    public static function getPersonalize($personalizeId, $personalize)
    {
        $personal = Personalize::find($personalizeId);

        $options = collect($personalize)->flatten(1)->map(function ($p) use ($personal) {
            $value = data_get($p, 'value.key') ?? $p['value'];

            return $personal->getInfo($p['key']['key'], $value);
        });

        return [
            'duration' => $personal->duration,
            'total' => $options->map->price->sum(),
            'options' => $options
        ];
    }
}
