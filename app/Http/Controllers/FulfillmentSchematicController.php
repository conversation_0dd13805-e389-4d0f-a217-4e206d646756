<?php

namespace App\Http\Controllers;

use App\FulfillmentSchematic;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class FulfillmentSchematicController extends Controller
{
    /**
     * GET /schematic
     * Fetches all current fulfillment schematics
     * @return FulfillmentSchematic[]|Collection
     */
    public function browseFulfillmentSchematics()
    {
        return FulfillmentSchematic::all();
    }

    /**
     * GET /schematic/{id}
     * Fetches specified fulfillment schematic
     * @param int id - The DB ID of the requested fulfillment schematic
     * @return JsonResponse
     */
    public function readFulfillmentSchematic($id)
    {
        try {
            return FulfillmentSchematic::findOrFail($id);
        } catch (ModelNotFoundException $e) {
            return response()->json(false);
        }
    }

    /**
     * POST /schematic
     * Adds a new fulfillment schematic
     * @param int shipping_option_id - The DB ID of the shipping option to be associated with the fulfillment schematic
     * @param double cutoff
     * @param boolean cutoff_is_hourly
     * @param float sunday_open (nullable)
     * @param float monday_open (nullable)
     * @param float tuesday_open (nullable)
     * @param float wednesday_open (nullable)
     * @param float thursday_open (nullable)
     * @param float friday_open (nullable)
     * @param float saturday_open (nullable)
     * @param float sunday_close (nullable)
     * @param float monday_close (nullable)
     * @param float tuesday_close (nullable)
     * @param float wednesday_close (nullable)
     * @param float thursday_close (nullable)
     * @param float friday_close (nullable)
     * @param float saturday_close (nullable)
     * @return JsonResponse
     */
    public function addFulfillmentSchematic(Request $request)
    {
        try {
            FulfillmentSchematic::create($request->all());

            return response()->json(['success' => true]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Create failed: ' . $e]);
        }
    }

    /**
     * PATCH /schematic/{id}
     * Edits a fulfillment schematic
     * @param int id - The DB ID of the fulfillment schematic to be edited
     * @param int shipping_option_id - The DB ID of the shipping option to be associated with the fulfillment schematic
     * @param double cutoff
     * @param boolean cutoff_is_hourly
     * @param float sunday_open (nullable)
     * @param float monday_open (nullable)
     * @param float tuesday_open (nullable)
     * @param float wednesday_open (nullable)
     * @param float thursday_open (nullable)
     * @param float friday_open (nullable)
     * @param float saturday_open (nullable)
     * @param float sunday_close (nullable)
     * @param float monday_close (nullable)
     * @param float tuesday_close (nullable)
     * @param float wednesday_close (nullable)
     * @param float thursday_close (nullable)
     * @param float friday_close (nullable)
     * @param float saturday_close (nullable)
     * @return JsonResponse
     */
    public function editFulfillmentSchematic(Request $request, $id)
    {
        try {
            FulfillmentSchematic::findOrFail($id)
                ->update($request->all());

            return response()->json(['success' => true]);
        } catch (ModelNotFoundException $e) {
            return response()->json(['success' => false, 'message' => 'Submitted ID does not exist']);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Update failed']);
        }
    }

    /**
     * DELETE /schematic/{id}
     * Deletes a fulfillment schematic
     * @param int id - The DB ID of the fulfillment schematic to be deleted
     * @return JsonResponse
     */
    public function deleteFulfillmentSchematic($id)
    {
        try {
            FulfillmentSchematic::destroy($id);

            return response()->json(['success' => true]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Delete failed']);
        }
    }
}
