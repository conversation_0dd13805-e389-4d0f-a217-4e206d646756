<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use GuzzleHttp\Client;

class ShipEngineStuffController extends Controller
{
    public function GetLinkAndArrivalDate($order)
    {
        return [
            'link' => $this->GetLink($order),
            'arrival_date' => $this->GetArrivalDate($order)
        ];
    }

    public function GetLink($order)
    {
        $codes = $this->GetCodesByService($order['shipping']['tracking']['service']);

        $client = new Client;

        $response = $client->post('https://api.shipengine.com/v-beta/tracking_page', [
            'headers' => [
                'Content-Type' => 'application/json',
                'API-Key' => env('SHIP_ENGINE_API'),
            ],
            'json' => [
                'tracking_pages' => [
                    [
                        "branded_tracking_theme_guid" => env('SHIP_ENGINE_TRACKING_PAGE_ID'),
                        "tracking_number" => $order['shipping']['tracking']['tracking_number'],
                        "carrier_code" => $codes['carrier_code'],
                        "service_code" => $codes['service_code'],
                        "to_city_locality" => $order['shipping']['shippingInfo']['city'],
                        "to_state_province" => $order['shipping']['shippingInfo']['state'],
                        "to_postal_code" => $order['shipping']['shippingInfo']['postal_code'],
                        "to_country_code" => $order['shipping']['shippingInfo']['country'],
                    ],
                ]
            ]
        ]);
        $result = $response->getBody()->getContents();
        return forceArray($result)['tracking_pages'][0]['url'];
    }

    private function GetCodesByService($service)
    {
        $codes = [];
        $carriers = forceArray($this->GetCarriers())['carriers'];

        collect($carriers)->each(function ($carrier) use ($service, &$codes) {
            $code = array_where($carrier['services'], function ($car) use ($service) {
                return $car['name'] == $service;
            });
            if ($code) {
                $codes = [
                    'carrier_code' => array_first($code)['carrier_code'],
                    'service_code' => array_first($code)['service_code']
                ];
            }
        });

        return $codes;
    }

    private function GetCarriers()
    {
        $client = new Client;
        $response = $client->get("https://api.shipengine.com/v1/carriers", [
            'headers' => [
                'API-Key' => env('SHIP_ENGINE_API'),
            ],
        ]);

        return $response->getBody()->getContents();
    }

    private function GetArrivalDate($order)
    {
        $carrier_code = $this->GetCodesByService($order['shipping']['tracking']['service'])['carrier_code'];
        $tracking_number = $order['shipping']['tracking']['tracking_number'];

        $client = new Client;
        $response = $client->get(
            "https://api.shipengine.com/v1/tracking?carrier_code={$carrier_code}&tracking_number={$tracking_number}",
            [
                'headers' => [
                    'API-Key' => env('SHIP_ENGINE_API'),
                ],
            ]
        );

        $result = $response->getBody()->getContents();
        return forceArray($result)['actual_delivery_date'] ?? forceArray($result)['estimated_delivery_date'];
    }

    public function SetUpWebhook($order)
    {
        $carrier_code = $this->GetCodesByService($order['shipping']['tracking']['service'])['carrier_code'];
        $tracking_number = $order['shipping']['tracking']['tracking_number'];

        $client = new Client;
        $response = $client->post(
            "https://api.shipengine.com/v1/tracking/start?carrier_code={$carrier_code}&tracking_number={$tracking_number}",
            [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'API-Key' => env('SHIP_ENGINE_API'),
                ],
            ]
        );

        return $response->getBody()->getContents();
    }

    public function GetEstimatedRate($request)
    {
        $country = $request->country;
        $zipCode = $request->zipCode;
        $products = GetFromFrontEnd($request->products);

        $weight = $products->map(function ($p) {
            return $p->weight;
        })->sum();

        $client = new Client;

        $response = $client->post('https://api.shipengine.com/v1/rates/estimate', [
            'headers' => [
                'Content-Type' => 'application/json',
                'API-Key' => env('SHIP_ENGINE_API'),
            ],
            'json' => [
                "carrier_ids" => collect(collect(json_decode($this->GetCarriers()))['carriers'])->map(function ($c) {
                    return $c->carrier_id;
                }),
                "from_country_code" => settings()->getValue('shipping_country'),
                "from_postal_code" => settings()->getValue('shipping_postal_code'),
                "to_country_code" => $country,
                "to_postal_code" => $zipCode,
                "weight" => ["value" => $weight, "unit" => "pound"],
            ]
        ]);
        //return collect(forceArray($response->getBody()->getContents()));
        return $this->FilterRates(collect(forceArray($response->getBody()->getContents())));
    }

    private function FilterRates($rates)
    {
        //$final = collect([]);


        // $final = $final->merge(['cheapest' => collect($result)->where('shipping_amount.amount', collect($result)->min('shipping_amount.amount'))->sortBy('delivery_days')->values()->first()]);

        // $final = $final->merge(['fastest' => collect($result)->where('delivery_days', collect($result)->min('delivery_days'))->sortBy('shipping_amount.amount')->values()->first()]);
        $result = collect($rates)->where('estimated_delivery_date', '!=', null);

        return $result->map(function ($rate) {
            return [
                'id' => rand(10, 99),
                'name' => $rate['service_type'],
                'price' => $rate['shipping_amount']['amount'],
                'estimated_arrival' => $rate['estimated_delivery_date'],
            ];
        });
    }

    public function GetInternationalRate($request)
    {
        $address = is_array($request->address) ? (object)$$request->address : $request->address;
        $products = GetFromFrontEnd($request->products);

        $weight = $products->map(function ($p) {
            return $p->weight;
        })->sum();
        $client = new Client;

        $response = $client->post('https://api.shipengine.com/v1/rates', [
            'headers' => [
                'Content-Type' => 'application/json',
                'API-Key' => env('SHIP_ENGINE_API'),
            ],
            'json' => [

                "rate_options" => [
                    "carrier_ids" => collect(collect(json_decode($this->GetCarriers()))['carriers'])->map(
                        function ($c) {
                            return $c->carrier_id;
                        }
                    ),
                ],
                "shipment" => [
                    "validate_address" => "validate_only",
                    "ship_to" => [
                        "address_line1" => $address->address_line_1,
                        "city_locality" => $address->city,
                        "state_province" => $address->state,
                        "postal_code" => $address->postal_code,
                        "country_code" => $address->country
                    ],
                    "ship_from" => [
                        "name" => settings()->getValue('store_name'),
                        "phone" => settings()->getValue('phone'),
                        "company_name" => settings()->getValue('store_name'),
                        "address_line1" => settings()->getValue('shipping_address_line_1'),
                        "city_locality" => settings()->getValue('shipping_city'),
                        "state_province" => settings()->getValue('shipping_state'),
                        "postal_code" => settings()->getValue('shipping_postal_code'),
                        "country_code" => settings()->getValue('shipping_country')
                    ],
                    "packages" => [
                        ["weight" => ["value" => $weight, "unit" => "pound"]]
                    ]
                ]
            ]
        ]);

        return $this->FilterRates(collect(forceArray($response->getBody()->getContents()))->first()['rates']);
    }
}
