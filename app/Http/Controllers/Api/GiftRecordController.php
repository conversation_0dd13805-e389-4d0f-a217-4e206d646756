<?php

namespace App\Http\Controllers\Api;

use App\GiftRecord;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class GiftRecordController extends Controller
{
    public function find(Request $request)
    {
        $found = GiftRecord::findRecord($request->gift);
        if ($found) {
            return abort(400, 'This gift was bought already.');
        }
        {
            return 'Not bought yet.';
        }
    }
}
