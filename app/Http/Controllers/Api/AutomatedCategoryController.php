<?php

namespace App\Http\Controllers\Api;

use App\AutomatedCategory;
use App\Category;
use App\Creator;
use App\FilterItem;
use App\Http\Controllers\Controller;
use App\Tag;
use App\Traits\FacetTrait;
use App\Vendor;
use Illuminate\Http\Request;
use Outl1ne\MenuBuilder\Models\MenuItem;

class AutomatedCategoryController extends Controller
{
    use FacetTrait;

    public function products(Request $request, $slug, AutomatedCategory $automatedCategory)
    {
        $request = $request->merge(['q' => $automatedCategory->contains ?? ' ']);

        $searchOptions = [
            'min' => $request->min ?? $automatedCategory->from_price,
            'max' => $request->max ?? $automatedCategory->to_price,
            'sort' => $request->sort ?? $automatedCategory->sort_by,
            'rule' => $automatedCategory->rule,

            'and_categories' => Category::find(explode(',', $automatedCategory->categories_and))->map->name,

            'and_vendors' => Vendor::find(explode(',', $automatedCategory->vendors_and))->map->name,

            'and_creators' => Creator::find(explode(',', $automatedCategory->creators_and))->map->name,

            'and_tags' => Tag::find(explode(',', $automatedCategory->tags_and))
                ->map(function ($tag) {
                    return data_get($tag->name, 'en');
                })->values(),

            'and_filters' => FilterItem::find(explode(',', $automatedCategory->filters_and))
                ->map(function ($filter) {
                    return trim($filter->filter->name) . '_' . trim($filter->name);
                })->values(),


            'any_filters' => FilterItem::find(explode(',', $automatedCategory->filters_or))
                ->map(function ($filter) {
                    return trim($filter->filter->name) . '_' . trim($filter->name);
                })->values(),

            'any_categories' => Category::find(explode(',', $automatedCategory->categories_or))->map->name,

            'any_vendors' => Vendor::find(explode(',', $automatedCategory->vendors_or))->map->name,

            'any_creator' => Creator::find(explode(',', $automatedCategory->creators_or))->map->name,

            'any_tags' => Tag::find(explode(',', $automatedCategory->tags_or))
                ->map(function ($tag) {
                    return data_get($tag->name, 'en');
                })->values(),


            'none_filters' => FilterItem::find(explode(',', $automatedCategory->filters_exclude))
                ->map(function ($filter) {
                    return trim($filter->filter->name) . '_' . trim($filter->name);
                })->values(),

            'none_categories' => Category::find(explode(',', $automatedCategory->categories_exclude))->map->name,

            'none_vendors' => Vendor::find(explode(',', $automatedCategory->vendors_exclude))->map->name,

            'none_creator' => Creator::find(explode(',', $automatedCategory->creators_exclude))->map->name,

            'none_tags' => Tag::find(explode(',', $automatedCategory->tags_exclude))
                ->map(function ($tag) {
                    return data_get($tag->name, 'en');
                })->values(),


            $automatedCategory->status => $automatedCategory->status,
        ];

        $forcedOptions = collect($searchOptions)
            ->filter(function ($item, $key) {
                return $item && starts_with($key, ['and', 'any', 'none']);
            });


        return collect(
            (new SearchController)->index($request->merge($searchOptions), $forcedOptions)
        )->merge([
            'breadcrumbs' => $this->breadcrumbs($automatedCategory),
            'description' => $automatedCategory->seo_desc,
        ]);
    }

    public function breadcrumbs($category)
    {
        $array = [];
        if ($item = MenuItem::where('value', $category->id)->where('class', 'App\Classes\AutomatedCategories')->first(
        )) {
            $array[] = ['name' => $item->name, 'path' => $item->customValue];
            $this->addParent($item, $array);
        }

        return array_reverse($array);
    }

    private function addParent($item, &$array)
    {
        if ($item->parent_id) {
            if ($parent = MenuItem::find($item->parent_id)) {
                $array[] = ['name' => $parent->name, 'path' => $parent->customValue];
                $this->addParent($parent, $array);
            }
        }
    }
}
