<?php

namespace App\Http\Controllers\Api;

use App\AssociatedPurchase;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;

class AssociatedPurchaseController extends Controller
{
    public function getAssociation($productId)
    {
        return Cache::tags(['search'])->remember(
            "association_{$productId}",
            60 * 60 * 24 * 7,
            function () use ($productId) {
                return optional(AssociatedPurchase::findAssociation($productId))
                    ->getAssociatedItems()
                    ?? [];
            }
        );
    }
}
