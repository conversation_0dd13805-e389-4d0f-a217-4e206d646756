<?php

namespace App\Http\Controllers\Api;

use App\Discount;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class DiscountController extends Controller
{
    public function allDiscounts(Request $request)
    {
        $products = GetFromFrontEnd($request->products);
        $min = $this->fullPrice($products);
        $userId = auth()->user() ? auth()->user()->id : null;
        $discounts = Discount::user()->active()->above($min)->automated()->get();
        $array = [];
        foreach ($discounts as $discount) {
            if (!$discount->checkCustomerLimit($userId) || !$discount->aboveLimit() || !$discount->checkLocation(
                    $request->location
                )) {
                continue;
            }
            $savings = $discount->getSavings($products, $request->location);
            if ($savings) {
                $saving = ['id' => $discount->id, 'name' => $discount->name, 'savings' => $savings];
                array_push($array, $saving);
            }
        }
        return $array;
    }

    public function fullPrice($products)
    {
        return $products->map(function ($item) {
            return $item->getFrontEndAttribute($item->quantity)['price'] * $item->quantity;
        })->sum();
        // $total=0;
        // foreach($products as $product){
        //     if ($product['type']=='product') {
        //         $price=Product::find($product['id'])->price;
        //     } else {
        //         $variant=VariationInfo::find($product['id']);
        //         $price = $variant->toArray()['price'];
        //     }
        //     $total+=($price * $product['quantity']);
        // }
        // return $total;
    }

    public function getDiscount(Request $request, $code)
    {
        $products = GetFromFrontEnd($request->products);
        $min = $this->fullPrice($products);
        $discount = $this->findDiscount($code);
        $discount->isActive($min, $request->location);
        $savings = $discount->getSavings($products, $request->location);
        if ($savings == 'wrong_location') {
            abort(400, "Sorry this discount is not eligible for your location");
        }

        $message = '';
        if (!$savings) {
            $message = "None of your items are covered by this discount.";
        }
        $sale_error = false;
        collect($products)->each(function ($bag) use (&$message, &$sale_error, $discount) {
            if (class_basename($bag) == 'Bag') {
                $product = $bag->model;
            }
            if ($product && $product->sale_price && !$discount->apply_to_sale) {
                $sale_error = true;
                $message = 'This discount is not eligible for sale items.';
            }
            if (($bag->model_type == 'App\GiftCard' && !$discount->apply_to_gc)
                || ($product && $product->tax_code == 22 && !$discount->apply_to_gc)
            ) {
                $message = $sale_error ? 'This discount is not eligible for gift cards and sale items.' :
                    'This discount is not eligible for gift cards.';
            }
        });
        if (!$savings) {
            abort(400, $message);
        }
        if (auth()->guest() && ($discount->limit_customer || $discount->eligibility_type == 'App\Customer')) {
            abort(
                400,
                '<p>Please <a style="text-decoration: underline;color:#A50002;" href="/login">login</a> or <a style="text-decoration: underline;color:#A50002;" href="/register"> create an account</a> to use this discount</p>'
            );
        }
        return ['id' => $discount->id, 'name' => $code, 'saving' => $savings, 'message' => $message];
    }

    public function findDiscount($code)
    {
        $discount = Discount::where([
            ['name', '=', $code],
            ['automated', '=', false]
        ])->first();
        if (!$discount) {
            abort(400, 'You have entered an invalid promo code.');
        }
        return $discount;
    }
}
