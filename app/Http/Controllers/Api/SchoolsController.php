<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Controllers\CurrencyController;
use App\Lists;
use App\School;

class SchoolsController extends Controller
{
    public function zip($country, $zip)
    {
        return School::where('country', $country)->where('zip', $zip)
            ->get()->map(function ($school) {
                return [
                    'id' => $school->id,
                    'zip' => $school->zip,
                    'name' => $school->name,
                    'country' => $school->country,
                    'address' => $school->address,
                ];
            });
    }

    public function show(School $school)
    {
        return [
            'school' => [
                'name' => $school->name,
            ],
            'lists' => $school->lists->map(function ($list) {
                $bundle = $list->bundle;
                $products = $list->formatted_products;
                return [
                    'id' => $list->id,
                    'year' => $list->year,
                    'name' => $list->grade,
                    'bundle_id' => $bundle->id,
                    'max' => $bundle->max,
                    'price' => number_format($bundle->price, 2),
                    'products' => $products,
                    'count' => $products->count(),
                    'savings' => min($bundle->fake_price - $bundle->price, 0),
                    'image' => optional(
                        $list->getMedia('media')
                            ->sortBy('order_column')->first()
                    )
                        ->getUrl('grid'),
                ];
            }),
        ];
    }

    public function list($school, Lists $list)
    {
        if ($list->school_id != $school) {
            abort(404);
        }
        $bundle = $list->bundle;
        $products = $list->formatted_products;

        return [
            'school' => $list->school->name,
            'id' => $list->id,
            'year' => $list->year,
            'name' => $list->grade,
            'bundle_id' => $bundle->id,
            'max' => $bundle->max,
            'price' => number_format($bundle->price, 2),
            'products' => $products,
            'count' => $products->count(),
            'savings' => min($bundle->fake_price - $bundle->price, 0),
            'image' => optional(
                $list->getMedia('media')
                    ->sortBy('order_column')->first()
            )
                ->getUrl('grid'),
        ];
    }

    public function countries()
    {
        $countries = School::pluck('country');

        return $active_country = CurrencyController::GetAllCountries(true)
            ->filter(function ($country) use ($countries) {
                return $countries->contains(data_get($country, 'value'));
            });
    }
}
