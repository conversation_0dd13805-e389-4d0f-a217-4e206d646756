<?php

namespace App\Http\Controllers\Api;

use App\Address;
use App\Http\Controllers\Controller;
use App\Payment;
use App\RecurringSetting;

class RecurringController extends Controller
{
    public function index()
    {
        $user = $this->user();

        return $user->recurrings->map(function ($recurring) {
            if ($model = $recurring->model) {
                return [
                    'id' => $recurring->id,
                    'cycle' => $recurring->cycle,
                    'amount' => $recurring->amount,
                    'shipping' => $recurring->shipping,
                    'payments' => $recurring->payments,
                    'quantity' => $recurring->quantity,
                    'next_date' => $recurring->next_date,
                    'product' => $model->front_end,
                    'first_delivery' => data_get($recurring, 'meta.first_delivery'),
                ];
            }
        })->filter()->values();
    }

    private function user()
    {
        return customer();
    }

    public function show($recurring)
    {
        $user = $this->user();

        $recurring = $user->recurrings
            ->find($recurring);

        return $recurring->toArray() + [
                'email' => $user->email,
                'product' => $recurring->model->front_end,
                'address' => $recurring->shipping,
                'payment' => $recurring->payments,
                'quantity' => $recurring->quantity,
                'first_delivery' => data_get($recurring, 'meta.first_delivery'),
            ];
    }

    public function create()
    {
        $request = request();
        $user = $this->user();

        $setting = RecurringSetting::find($request->setting_id);

        $model = getModel($request->type, $request->product_id);

        $recurring = $user->recurrings()->create([
            'quantity' => $request->quantity,
            'amount' => $setting->amount,
            'cycle' => $setting->cycle,
            'model_id' => $model->id,
            'model_type' => get_class($model),
            'payment_id' => $request->payment_id,
            'address_id' => $request->address_id,
            'meta->first_delivery' => $request->first_delivery,
            'shipping' => collect(Address::find($request->address_id)->toArray())
                ->only(
                    'id',
                    'address_line_1',
                    'address_line_2',
                    'city',
                    'state',
                    'country',
                    'name',
                    'phone',
                    'postal_code'
                )
                ->toArray(),
            'payments' => collect(Payment::find($request->payment_id)->toArray())
                ->only(
                    'id',
                    'address_line_1',
                    'address_line_2',
                    'city',
                    'country',
                    'created_at',
                    'expDate',
                    'last_four',
                    'name',
                    'postal_code',
                    'securityCode',
                    'state',
                    'type'
                )
                ->toArray()
        ]);

        return $recurring->id;
    }

    public function cancel()
    {
        $user = $this->user();

        $request = request();

        $user->recurrings()
            ->findOrFail($request->id)
            ->update(['status' => 'canceled']);

        return;
    }

    public function update()
    {
        $user = $this->user();

        $request = request();

        $user->recurrings()
            ->findOrFail($request->id)
            ->update([
                'quantity' => $request->quantity,
                'payment_id' => $request->payment_id,
                'address_id' => $request->address_id,
                'meta->first_delivery' => $request->first_delivery,
                'shipping' => collect(Address::find($request->address_id)->toArray())
                    ->only(
                        'id',
                        'address_line_1',
                        'address_line_2',
                        'city',
                        'state',
                        'country',
                        'name',
                        'phone',
                        'postal_code'
                    )
                    ->toArray(),
                'payments' => collect(Payment::find($request->payment_id)->toArray())
                    ->only(
                        'id',
                        'address_line_1',
                        'address_line_2',
                        'city',
                        'country',
                        'created_at',
                        'expDate',
                        'last_four',
                        'name',
                        'postal_code',
                        'securityCode',
                        'state',
                        'type'
                    )
                    ->toArray(),
            ]);

        return $request->id;
    }
}
