<?php

namespace App\Http\Controllers\Api;

use App\Emails;
use App\Emails\GiftCardNoticeEmail;
use App\Emails\MessageDetails;
use App\Http\Controllers\Controller;
use App\Jobs\SendEmail;
use App\Order;
use Throwable;

class EmailsController extends Controller
{
    public static function SendAbandonedCart($customer)
    {
        $data = [
            // 'customer' => $customer,
            // 'products' => $customer->abandonedBag(),
            'bag_ids' => $customer->abandonedBagIds()
        ];
        if (!$data['bag_ids']->count()) {
            return;
        }
        return SendEmail::dispatch(Emails\AbandonedCartEmail::class, $data, ['email' => data_get($customer, 'email')]);
    }

    public static function SendPaymentError($order)
    {
        if (self::sent($order, 'PaymentError')) {
            return;
        }
        $data = [
            'order' => $order
        ];
        if ($order->subscription) {
            SendEmail::dispatch(
                Emails\PaymentErrorSubscriptionEmail::class,
                $data,
                ['email' => data_get($order->customer, 'email')]
            );
        }
        //  elseif ($order->recurring_type == 'App\Recurring') {
        //     SendEmail::dispatch(Emails\RecurringPaymentErrorEmail::class, $data, ['email' => data_get($order->customer, 'email')]);
        // }
        else {
            SendEmail::dispatch(Emails\PaymentErrorEmail::class, $data, ['email' => data_get($order->customer, 'email')]
            );
        }
        self::SetSent($order, 'PaymentError');
    }

    public static function Sent($order, $type)
    {
        return collect(data_get($order, 'meta.sent'))->contains($type);
    }

    public static function SetSent($order, $type)
    {
        $array = data_get($order, 'meta.sent') ?? [];
        $array = array_merge($array, [$type]);
        Order::find($order->id)->update([
            'meta->sent' => $array
        ]);
    }

    public static function SendOrderCancelled($order)
    {
        collect(explode(',', settings()->getValue('order_notifications_emails')))->each(function ($admin) use ($order) {
            $data = [
                'order' => $order,
                'email' => $admin
            ];
            SendEmail::dispatch(Emails\CancelledOrderNotification::class, $data, ['email' => data_get($data, 'email')]);
        });
        if (self::sent($order, 'Cancelled')) {
            return;
        }
        $data = [
            'order' => $order
        ];

        SendEmail::dispatch(Emails\OrderCancelledEmail::class, $data, ['email' => data_get($order->customer, 'email')]);
        self::SetSent($order, 'Cancelled');
    }

    public static function SendOrderModified($order, $reversion, $message)
    {
        $data = [
            'order' => $order,
            'reversion' => $reversion,
            'message' => $message
        ];
        (new Emails\OrderModifiedEmail)->withData(['data' => $data])->sendTo(
            ['email' => data_get($order->customer, 'email')]
        );
    }

    public static function SendOrderConfirmation($order)
    {
        if (!$order->subscription && !data_get($order, 'shipping.shippingType.delivery') && $order->shippable) { 
            collect(explode(',', settings()->getValue('pickup_order_notifications_emails')))->each(function ($admin) use ($order) {
                $data = [
                    'order' => $order,
                    'email' => $admin
                ];

                SendEmail::dispatch(Emails\OrderConfirmationStorePickUpEmail::class, $data, ['email' => data_get($data, 'email')]);
            });
        }

        collect(explode(',', settings()->getValue('order_notifications_emails')))->each(function ($admin) use ($order) {
            $data = [
                'order' => $order,
                'email' => $admin
            ];

            SendEmail::dispatch(Emails\OrderNotificationEmail::class, $data, ['email' => data_get($data, 'email')]);
        });


        if (self::sent($order, 'Confirmation') || $order->subscription) {
            return;
        }
        $data = [
            'order' => $order
        ];

        collect($order->GiftCards)->each(function ($giftCard) {
            self::SendGiftCard($giftCard);
        });

        if (!$order->subscription && !data_get($order, 'shipping.shippingType.delivery') && $order->shippable) {
            SendEmail::dispatch(Emails\OrderConfirmationStorePickUpEmail::class, $data, ['email' => data_get($order->customer, 'email')]);
        } else {
            SendEmail::dispatch(Emails\OrderConfirmationEmail::class, $data, ['email' => data_get($order->customer, 'email')]);
        }
        self::SetSent($order, 'Confirmation');
        if (collect($order->products)->every(function ($product) {
            return data_get($product, 'type') == 'giftCard';
        })) {
            $order->markAsDelivered();
        }
    }

    public static function SendGiftCard($giftCard)
    {
        $data = [
            'gift_card' => $giftCard,
            'email' => $giftCard->to_email
        ];
        // SendEmail::dispatch(Emails\GiftCardNoticeEmail::class, $data, ['email' => data_get($data, 'email')]);

        $result = (new GiftCardNoticeEmail)->withData(['data' => $data])->sendTo(
            ['email' => data_get($data, 'email')]
        );
        $status_code = data_get(forceArray(json_encode($result)), 'http_status_code');
        $message_id = data_get(forceArray(json_encode($result)), 'response.0.MessageID');
        if ($status_code != '202') {
            $giftCard->update([
                'meta->email->reason' => data_get(forceArray(json_encode($result)), 'response.Message')
            ]);
            self::SendGiftCardError($giftCard);
        } else {
            $giftCard->update([
                'meta->sent_email' => true,
                'meta->email->message_id' => $message_id,
            ]);
        }
        dispatch(function () use ($giftCard) {
            EmailsController::checkStatus($giftCard);
        })
            ->delay(now()->addMinutes(2))
            ->onQueue('low');
    }

    public static function SendGiftCardError($giftCard)
    {
        $data = [
            'reason' => data_get(json_decode($giftCard->meta), 'email.reason'),
            'gift_card' => $giftCard,
            'email' => data_get($giftCard, 'order.customer.email') ?? settings()->getValue('email')
        ];
        SendEmail::dispatch(Emails\GiftCardErrorEmail::class, $data, ['email' => data_get($data, 'email')]);

        $giftCard->update([
            'meta->sent_error_email' => true,
        ]);
    }

    public static function checkStatus($giftCard)
    {
        $message_id = data_get(json_decode($giftCard->meta), 'email.message_id');
        $result = (new MessageDetails)->details($message_id);
        $status = data_get(forceArray($result), 'response.Status');
        $giftCard->update([
            'meta->email->status' => $status,
            'meta->email->reason' => $status
        ]);
        if ($status == 'Bounced') {
            self::SendGiftCardError($giftCard);
        }
    }

    public static function SendOrderShipped($order)
    {
        if (self::sent($order, 'Shipped')) {
            return;
        }
        $data = [
            'order' => $order
        ];

        if ($order->subscription) {
            SendEmail::dispatch(
                Emails\SubscriptionShippedEmail::class,
                $data,
                ['email' => data_get($order->customer, 'email')]
            );
        } elseif (!data_get($order, 'shipping.shippingType.delivery') && $order->shippable) {
            SendEmail::dispatch(
                Emails\ReadyForPickUpEmail::class,
                $data,
                ['email' => data_get($order->customer, 'email')]
            );
        } else {
            SendEmail::dispatch(Emails\OrderShippedEmail::class, $data, ['email' => data_get($order->customer, 'email')]
            );
        }
        self::SetSent($order, 'Shipped');
    }

    public static function SendOrderDelivered($order)
    {
        if (self::sent($order, 'Delivered')) {
            return;
        }

        try {
            $order['image_url'] = collect(
                forceArray($order->routes()->withPivot('web_hooks')->first()->pivot->web_hooks)
            )
                ->firstWhere('note_attachment_url')['note_attachment_url'];
        } catch (Throwable $th) {
            $order['image_url'] = null;
        }

        $data = [
            'order' => $order
        ];

        if ($order->subscription) {
            SendEmail::dispatch(
                Emails\SubscriptionDeliveredEmail::class,
                $data,
                ['email' => data_get($order->customer, 'email')]
            );
        } elseif (!data_get($order, 'shipping.shippingType.delivery') && $order->shippable) {
            SendEmail::dispatch(Emails\PickedUpEmail::class, $data, ['email' => data_get($order->customer, 'email')]);
        } else {
            SendEmail::dispatch(
                Emails\OrderDeliveredEmail::class,
                $data,
                ['email' => data_get($order->customer, 'email')]
            );
        }
        self::SetSent($order, 'Delivered');
    }

    public static function SendPasswordReset($data)
    {
        $data = [
            'data' => $data
        ];
        return (new Emails\PasswordResendEmail)->withData(['data' => $data])->sendTo(
            ['email' => data_get($data, 'data.email')]
        );
    }

    public static function SendFollowCreator($product, $following, $customer)
    {
        $data = [
            'product' => $product,
            'following' => $following
        ];

        return SendEmail::dispatch(Emails\FollowCreatorEmail::class, $data, ['email' => data_get($customer, 'email')]);
    }

    public static function SendReturnSummary($return)
    {
        $data = [
            'return' => $return
        ];
        SendEmail::dispatch(Emails\ReturnSummeryEmail::class, $data, ['email' => data_get($return->customer, 'email')]);
    }

    public static function SendReturnConfirmation($return)
    {
        $data = [
            'return' => $return
        ];
        SendEmail::dispatch(
            Emails\RefundConfirmationEmail::class,
            $data,
            ['email' => data_get($return->customer, 'email')]
        );
    }

    public static function SendSubscriptionConfirmation($subscription)
    {
        if (!$products = $subscription->combination->upcoming_products) {
            return;
        }
        $products = collect($products)
            ->map(function ($product) use ($subscription) {
                return ['quantity' => $subscription->quantity] + $product;
            });
        $data = [
            'subscription' => $subscription,
            'products' => $products
        ];
        return SendEmail::dispatch(
            Emails\SubscriptionConfirmationEmail::class,
            $data,
            ['email' => data_get($subscription->customer, 'email')]
        );
    }

    public static function SendSubscriptionReview($subscription, $products)
    {
        $data = [
            'subscription' => $subscription,
            'products' => $products
        ];
        return SendEmail::dispatch(
            Emails\SubscriptionReviewEmail::class,
            $data,
            ['email' => data_get($subscription->customer, 'email')]
        );
    }

    public static function SendRecurringConfirmation($subscription)
    {
        $data = [
            'subscription' => $subscription
        ];
        return SendEmail::dispatch(
            Emails\RecurringEmail::class,
            $data,
            ['email' => data_get($subscription->customer, 'email')]
        );
    }

    public static function SendRecurringReview($subscription)
    {
        $data = [
            'subscription' => $subscription
        ];
        return SendEmail::dispatch(
            Emails\RecurringReviewEmail::class,
            $data,
            ['email' => data_get($subscription->customer, 'email')]
        );
    }

    public static function SendRefundOrder($payments, $customer)
    {
        $data = [
            'payments' => $payments,
        ];
        return SendEmail::dispatch(Emails\RefundOrderEmail::class, $data, ['email' => data_get($customer, 'email')]);
    }

    public static function SendSurvey($order)
    {
        if (self::sent($order, 'Survey')) {
            return;
        }
        (new Emails\SurveyEmail)->sendTo(['email' => data_get($order->customer, 'email')]);
        self::SetSent($order, 'Survey');
    }
}
