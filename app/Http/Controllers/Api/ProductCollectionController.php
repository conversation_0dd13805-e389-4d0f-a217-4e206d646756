<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\ProductCollection;
use App\Traits\FacetTrait;
use Illuminate\Http\Request;
use Outl1ne\MenuBuilder\Models\MenuItem;

class ProductCollectionController extends Controller
{
    use FacetTrait;

    public function products(Request $request, $slug, ProductCollection $productCollection)
    {
        $request = $request->merge(['q' => ' ']); // Default search query

        // Get the product IDs from the collection in the correct order
        $productIds = $productCollection->products ?? [];

        if (empty($productIds)) {
            return [
                'products' => collect([]),
                'breadcrumbs' => $this->breadcrumbs($productCollection),
                'description' => $productCollection->seo_description,
                'title' => $productCollection->seo_title,
            ];
        }

        // Create forced options to pass product IDs to SearchController
        $forcedOptions = [
            'product_ids' => $productIds,
            'maintain_order' => $productIds, // Pass original order for sorting
        ];

        return collect(
            (new SearchController)->index($request, $forcedOptions)
        )->merge([
            'breadcrumbs' => $this->breadcrumbs($productCollection),
            'description' => $productCollection->seo_description,
            'title' => $productCollection->seo_title,
        ]);
    }

    public function breadcrumbs($collection)
    {
        $array = [];
        if ($item = MenuItem::where('value', $collection->id)->where('class', 'App\Classes\ProductCollections')->first()) {
            $array[] = ['name' => $item->name, 'path' => $item->customValue];
            $this->addParent($item, $array);
        }

        return array_reverse($array);
    }

    private function addParent($item, &$array)
    {
        if ($item->parent_id) {
            if ($parent = MenuItem::find($item->parent_id)) {
                $array[] = ['name' => $parent->name, 'path' => $parent->customValue];
                $this->addParent($parent, $array);
            }
        }
    }
}
