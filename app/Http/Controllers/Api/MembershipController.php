<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Membership;

class MembershipController extends Controller
{
    public function create()
    {
        if ($user = auth()->user()) {
            return Membership::create(['customer_id' => $user->id]);
        } else {
            abort(403);
        }
    }

    public function switchActive()
    {
        if (($user = auth()->user()) && ($membership = $user->membership)) {
            $membership = $user->membership;
            $membership->active = !$membership->active;
            if (!$membership->active) {
                $membership->status = 'Blue';
                $membership->status_start = null;
                $membership->total_points = 0;
            } else {
                $membership->created_at = now();
            }
            $membership->save();
            return $membership;
        } else {
            abort(403);
        }
    }
}
