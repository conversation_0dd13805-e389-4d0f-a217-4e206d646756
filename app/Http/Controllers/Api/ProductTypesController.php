<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Product;
use App\ProductType;

class ProductTypesController extends Controller
{
    public function creators($id)
    {
        $type = ProductType::find($id);
        if (!$type->creator_type) {
            return ['hide' => true];
        }
        if ($id = request()->product_id) {
            $query = Product::find($id)->creators;

            if (in_array(strtolower($type->type), ['author', 'illustrator'])) {
                $query->where('type', 'author')
                    ->orWhere('type', 'illustrator');
            } else {
                $query->where('type', $type->creator_type);
            }

            return $query
                ->map(function ($item) {
                    return ['value' => $item->id, 'display' => $item->name];
                });
        }
        return [];
    }
}
