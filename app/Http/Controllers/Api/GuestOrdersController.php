<?php

namespace App\Http\Controllers\Api;

use App\GiftRecord;
use App\Http\Controllers\ChargeController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\PosController;
use App\Http\Controllers\PosOnlineController;
use App\Http\Controllers\TransactionController;
use App\Jobs\ReturnCancelCreate;
use App\Membership;
use App\Order;
use Illuminate\Http\Request;

class GuestOrdersController extends Controller
{
    private $order;

    public function show(Request $request, $id)
    {
        $this->authorizeRequest($request, $id);

        !$this->isAuthorized($id) ? abort(404) : null;

        return $this->order->front_end;
    }

    private function authorizeRequest($request, $id)
    {
        $this->order = Order::find($id);
        if ($this->order && $this->order->customer->email == $request->email) {
            session()->put('authorized_order_' . $this->order->id, true);
        }
    }

    private function isAuthorized($id)
    {
        return !!session()->get('authorized_order_' . $id);
    }

    public function cancel(Request $request, $id)
    {
        abort_unless($this->isAuthorized($id), 404);

        $order = Order::find($id);

        $products = collect($order->products)->map(function ($product) {
            $type = $product['item_type'];
            if ($type == 'physical' && !data_get($product, 'personalization')) {
                $product['status'] = 'cancelled';
            }
            return $product;
        });

        $order->update([
            'status' => 'cancelled',
            'products' => $products
        ]);

        ReturnCancelCreate::dispatch(
            ChargeController::class,
            'ReturnCancelOrderPayments',
            $order
        );

        $hasNonCancellableItems = $order->HasNonCancellableItems();

        ReturnCancelCreate::dispatch(
            TransactionController::class,
            'DeleteTransaction',
            $order
        )->onQueue('low');

        if ($hasNonCancellableItems) {
            ReturnCancelCreate::dispatch(
                TransactionController::class,
                'AddTransaction',
                $order
            )->onQueue('low');

            ReturnCancelCreate::dispatch(PosController::class, 'CreateOrder', $order)->onQueue(
                'low'
            );
            ReturnCancelCreate::dispatch(
                PosOnlineController::class,
                'CreateOrder',
                $order
            )->onQueue('low');
        } else {
            ReturnCancelCreate::dispatch(PosController::class, 'CancelOrder', $order)->onQueue(
                'low'
            );
            ReturnCancelCreate::dispatch(
                PosOnlineController::class,
                'CancelOrder',
                $order
            )->onQueue('low');
        }
        ReturnCancelCreate::dispatch(Membership::class, 'removePoints', $order);

        ReturnCancelCreate::dispatch(GiftRecord::class, 'removeFromOrder', $order);

        ReturnCancelCreate::dispatch(
            EmailsController::class,
            'SendOrderCancelled',
            $order
        )->onQueue('high');

        ReturnCancelCreate::dispatch(
            InventoryController::class,
            'AddToInventory',
            collect($order->products)->where('item_type', 'physical')
        );
    }
}
