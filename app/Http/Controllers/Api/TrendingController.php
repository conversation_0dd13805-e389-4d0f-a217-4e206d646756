<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Order;
use App\Product;
use App\ZipCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class TrendingController extends Controller
{
    public static function minimum_customers()
    {
        return settings()->getValue('trending_minimum_customers');
    }

    public function getTrending(Request $request)
    {
        $date = $this->startDate();
        $local = $this->localTrending($date);

        if ($local->isNotEmpty()) {
            return [
                'local' => true,
                'state' => $this->getLocation()[1],
                'trends' => $local,
            ];
        }

        return ['local' => false, 'trends' => $this->generalTrending($date)];
    }

    public function startDate()
    {
        $time_duration = explode(',', settings()->getValue('trending_time_duration'));

        return now()->add(-($time_duration[0]), ($time_duration[1]));
    }

    public function localTrending($date)
    {
        $locationArray = $this->getLocation();
        return Cache::remember(
            'trending_' . $date->toDateString() . '_' . implode(',', $locationArray),
            60 * 60 * 24,
            function () use ($date, $locationArray) {
                $trends = Order::where('created_at', '>=', $date)
                    ->whereJsonContains("shipping->shippingInfo->$locationArray[0]", $locationArray[1])
                    ->whereJsonLength('product_ids', '>', '0')->active()
                    ->get()
                    ->map(function ($order) {
                        return collect($order->product_ids)->map(function ($product) use ($order) {
                            return ['customer' => $order->customer_id, 'product' => $product];
                        });
                    })
                    ->flatten(1)
                    ->groupBy('product')
                    ->filter(function ($products) {
                        return $products->count() >= self::minimum_items() && $products->pluck('customer')->unique(
                            )->count() >= self::minimum_items();
                    })
                    ->sortByDesc(function ($products) {
                        return $products->count();
                    })
                    ->take(10)
                    ->keys();
                if ($trends) {
                    return Product::find(json_decode($trends))->map(function ($product) {
                        return $product->front_end;
                    })->sortBy(function ($product) use ($trends) {
                        return array_search($product['id'], $trends->toArray());
                    })->values();
                }
            }
        );
    }

    public function getLocation()
    {
        $location = session()->get('zip_code');
        if ($location['country'] == 'US') {
            $zip = $location['zip'];
            $state = ZipCode::getCode(substr($zip, 0, 3))->state;
            return ['state', $state];
        } else {
            return ['country', $location['country']];
        }
    }

    public static function minimum_items()
    {
        return settings()->getValue('trending_minimum_items');
    }

    public function generalTrending($date)
    {
        return Cache::remember('trending_' . $date->toDateString(), 60 * 60 * 24, function () use ($date) {
            $trends = Order::where('created_at', '>=', $date)->whereJsonLength('product_ids', '>', '0')->active()->get(
            )->map(function ($order) {
                return collect($order->product_ids)
                    ->map(function ($product) use ($order) {
                        return ['customer' => $order->customer_id, 'product' => $product];
                    });
            })
                ->flatten(1)
                ->groupBy('product')
                ->filter(function ($products) {
                    return $products->count() >= self::minimum_items() && $products->pluck('customer')->unique()->count(
                        ) >= self::minimum_items();
                })
                ->sortByDesc(function ($products) {
                    return $products->count();
                })
                ->take(10)
                ->keys();
            if ($trends) {
                return Product::find(json_decode($trends))->map(function ($product) {
                    return $product->front_end;
                })->sortBy(function ($product) use ($trends) {
                    return array_search($product['id'], $trends->toArray());
                })->values();
            }
        });
    }
}
