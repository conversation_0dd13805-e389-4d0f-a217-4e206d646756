<?php

namespace App\Http\Controllers\Api;

use App\Bundle;
use App\Http\Controllers\Controller;
use App\Product;

class RecentController extends Controller
{
    public function index()
    {
        $collection = session('recent', collect())
            ->concat(optional(auth()->user())->recent ?? [])
            ->groupBy(['type', 'id'])
            ->map(function ($type) {
                return $type->map(function ($item) {
                    return $item->sortByDesc('time')->first();
                });
            })
            ->flatten(1)
            ->sortByDesc('time');

        $bundles = Bundle::find($collection->where('type', 'bundle')->pluck('id'))
            ->map(function ($bundle) {
                if (request()->bundle_id != $bundle->id) {
                    return $bundle->front_end;
                }
            });

        $products = Product::find($collection->where('type', 'product')->pluck('id'))
            ->map(function ($product) {
                if (request()->product_id != $product->id) {
                    return $product->front_end;
                }
            });

        return $collection->map(function ($item) use ($bundles, $products) {
            if ($item['type'] == 'bundle') {
                return $bundles->where('id', $item['id'])->first();
            }
            if ($item['type'] == 'product') {
                return $products->where('id', $item['id'])->first();
            }
        })->filter()->values();
    }
}
