<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Product;

class GuestsController extends Controller
{
    public function favorites($ids = [])
    {
        $products = Product::find(explode(',', $ids));
        if ($products) {
            return $products->map(function ($product) {
                return $product->front_end;
            });
        }
        return [];
    }
}
