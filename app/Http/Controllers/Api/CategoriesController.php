<?php

namespace App\Http\Controllers\Api;

use App\Category;
use App\Http\Controllers\Controller;
use App\Traits\FacetTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Outl1ne\MenuBuilder\Models\Menu;
use Outl1ne\MenuBuilder\Models\MenuItem;

class CategoriesController extends Controller
{
    use FacetTrait;

    public function products(Request $request, $slug, Category $category)
    {
        if (!$request->q) {
            $request = $request->merge(['q' => ' ']);
        }
        $request = $request->merge(['sort' => $request->sort ?? $category->sort_by]);

        $request->merge(['all_categories' => urlencode($category->name)]);
        return collect((new SearchController)->index($request, collect(['all_categories' => urlencode($category->name)])))->merge([
            'breadcrumbs' => $this->breadcrumbs($category),
            'description' => $category->seo_desc ?? str_replace(
                    '{name}',
                    ' ' . $category->name . ' ',
                    settings()->getValue('caregory_seo_title')
                ),
            'title' => $category->seo_title
        ]);
        // return $this->ProductWithFacets($category->products());
    }

    public function index()
    {
        return Cache::tags(['categories'])->remember('menu', 60 * 60 * 24 * 7, function () {
            $menu = Menu::whereSlug('departments')->first();
            return collect($menu->rootMenuItems)->map(function ($item) {
                return $this->formatMenuItem($item);
            })->filter()->values();
        });
    }

    public function formatMenuItem($menuItem)
    {
        if ($menuItem->enabled) {
            return [
                'id' => $menuItem->id,
                'name' => $menuItem->name,
                'type' => $menuItem->type,
                'value' => $menuItem->customValue,
                'target' => $menuItem->target,
                'children' => empty($menuItem->children) ? [] : $menuItem->children->map(function ($item) {
                    return $this->formatMenuItem($item);
                })->filter()->values(),
            ];
        }
    }

    public function breadcrumbs($category)
    {
        $array = [];
        if ($item = MenuItem::where('value', $category->id)->where('class', 'App\Classes\Categories')->first()) {
            $array[] = ['name' => $item->name, 'path' => $item->customValue];
            $this->addParent($item, $array);
        } else {
            $array[] = ['name' => $category->name, 'path' => $category->path];
        }
        return array_reverse($array);
    }

    private function addParent($item, &$array)
    {
        if ($item->parent_id) {
            if ($parent = MenuItem::find($item->parent_id)) {
                $array[] = ['name' => $parent->name, 'path' => $parent->customValue];
                $this->addParent($parent, $array);
            }
        }
    }

    private function traverse($data, $parent = null)
    {
        return $data->map(function ($item) use ($parent) {
            $array = [
                'id' => $item->id,
                'name' => $item->name,
                'path' => $item->path,
                'parent' => isset($parent) ? $parent : null,
            ];

            count($item->children) > 0
                ? $array['children'] = $this->traverse($item->children, $item)
                : null;
            count($item->filters) > 0
                ? $array['filters'] = $item->filters
                : null;

            return $array;
        });
    }
}
