<?php

namespace App\Http\Controllers\Api;

use App\FrequentlyBought;
use App\Http\Controllers\Controller;
use App\Product;

class FrequentlyBoughtController extends Controller
{
    public static function updateFrequentBundles()
    {
        return FrequentlyBought::calculateAllBundles();
    }

    public function getBundle($productId)
    {
        $bundle = optional(FrequentlyBought::where('product_id', $productId)->first())->bundle;
        if ($bundle) {
            return Product::find(collect(forceArray($bundle)))->map(function ($product) {
                return $product->front_end;
            });
        } else {
            return null;
        }
    }
}
