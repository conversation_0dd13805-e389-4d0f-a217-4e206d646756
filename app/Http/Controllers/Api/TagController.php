<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\Tags\Tag;

class TagController extends Controller
{
    public function products(Request $request, $slug, Tag $tag)
    {
        if (!$request->q) {
            $request = $request->merge(['q' => ' ']);
        }
        $request->merge(['all_tags' => $tag->name]);

        return collect((new SearchController)->index($request, collect(['and_tags' => [$tag->name]])))->merge([
            'breadcrumbs' => [
                ['name' => 'Home', 'path' => '/'],
                ['name' => $tag->name],
            ]
        ]);
    }
}
