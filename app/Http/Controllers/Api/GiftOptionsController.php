<?php

namespace App\Http\Controllers\Api;

use App\GiftNotesSetting;
use App\GiftOption;
use App\Http\Controllers\Controller;

class GiftOptionsController extends Controller
{
    public function index()
    {
        $giftOptions = GiftOption::all()->map(function ($item) {
            return [
                'id' => $item->id,
                'name' => $item->name,
                'description' => $item->description,
                'price' => $item->price,
                'media' => $item->getFirstMediaUrl('media'),
            ];
        });

        return [
            'giftOptions' => $giftOptions,
            'giftNotes' => GiftNotesSetting::all()
        ];
    }
}
