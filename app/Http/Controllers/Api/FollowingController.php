<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class FollowingController extends Controller
{
    public function add(Request $request)
    {
        $user = auth()->user() ?? abort(404);

        switch ($request->following['type']) {
            case 'vendor':
                $class = 'App\\Vendor';
                break;

            case 'creator':
                $class = 'App\\Creator';
                break;
        }

        $user->following()->firstOrCreate([
            'model_id' => $request->following['id'],
            'model_type' => $class,
        ]);

        return $this->index();
    }

    public function index()
    {
        $user = auth()->user() ?? abort(404);
        return $user->front_end_following;
    }

    public function remove(Request $request)
    {
        $user = auth()->user() ?? abort(404);
        switch ($request->following['type']) {
            case 'vendor':
                $class = 'App\\Vendor';
                break;

            case 'creator':
                $class = 'App\\Creator';
                break;
        }

        $user->following()->where([
            'model_id' => $request->following['id'],
            'model_type' => $class,
        ])->delete();

        return $this->index();
    }
}
