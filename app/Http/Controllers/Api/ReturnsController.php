<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Controllers\ReturnLabelController;
use App\Jobs\ReturnCancelCreate;
use App\Order;
use App\Returns;
use Illuminate\Http\Request;

class ReturnsController extends Controller
{
    public function show($id)
    {
        $return = Returns::findOrFail($id);

        $order = $return->order;
        return [
            'dropoff' => $return->dropoff,
            'products' => $return->products,
            'payments' => $return->payments,
            'return_by' => $order->return_by,
            'sub_total' => $return->sub_total,
            'tax_amount' => $return->tax_amount,
            'grand_total' => $return->grand_total,
            'shipping_amount' => $return->shipping_amount,
            'discount_amount' => $return->discount_amount,
            'label' => data_get($return, 'shipping.tracking.label_pdf'),
        ];
    }

    public function create(Request $request)
    {
        $source = request()->is('nova-api/*') ? 'Admin' : 'Customer';
        $order = Order::findOrFail($request->order_id);
        $return_items = [];
        $non_return_items = [];

        collect($request->products)
            ->each(function ($product) use ($order, &$return_items, &$non_return_items) {
                $this->isReturnable($order, $product)
                    ? $return_items[] = $product
                    : $non_return_items[] = $product;
            });
        $breakDown = (new CalculateRefundController)->GetBreakDown(request()->merge([
            'products' => $return_items,
            'order_id' => $order->id,
            'final' => true,
            'eGift' => $request->eGift
        ]));

        if (collect(data_get($breakDown, 'products'))->count() > 0) {
            $return = $order->returns()->create([
                'meta->source' => $source,
                'dropoff' => $request->dropoff,
                'products' => $breakDown['products'],
                'sub_total' => $breakDown['sub_total'],
                'tax_amount' => $breakDown['tax_amount'],
                'payments' => data_get($breakDown, 'payments'),
                'grand_total' => max($breakDown['grand_total'], 0),
                'shipping_amount' => $breakDown['shipping_amount'],
                'discount_amount' => $breakDown['discount_amount'],
            ]);

            if (!$return->dropoff && data_get($return, 'shippingInfo.country') == 'US') {
                ReturnLabelController::CreateReturnLabel($return);
            }


            ReturnCancelCreate::dispatch(
                EmailsController::class,
                'SendReturnSummary',
                $return
            )->onQueue('high');


            //Add ReturnLabel And ReturnsLeft To Every Product

            // $products = collect($order->products)->map(function ($product) use ($return) {
            //     $return_product = collect($return->products)->filter(function ($p) use ($product) {
            //         return $p['type'] == $product['type'] && $p['id'] == $product['id'];
            //     })->first();

            //     if ($return_product) {
            //         $product['returns_left'] = $product['quantity'] - $return_product['quantity'] - data_get($product, 'returns_left');
            //         $product['return_label'] = data_get($return, 'shipping.tracking.label_pdf');
            //     }

            //     return $product;
            // });

            // $order->update([
            //     'products' => $products
            // ]);

            return ['id' => $return->id];
        }
        return null;
        return compact('return_items', 'non_return_items');
    }

    public function isReturnable($order, $product)
    {
        $single = collect($order->products)->filter(function ($item) use ($product) {
            return $product['id'] == $item['id'] && $product['type'] == $item['type'] && data_get(
                    $product,
                    'status'
                ) != 'canceled';
        })->first();
        $returns = $order->returns->pluck('products')->flatten(1)->filter(function ($item) use ($single) {
            return $single['id'] == $item['id'] && $single['type'] == $item['type'];
        });
        $return_sum = $returns->pluck('quantity')->sum();
        return $return_sum <= $single['quantity']
            && $return_sum <= ($single['quantity'] - $product['quantity']);
    }
}
