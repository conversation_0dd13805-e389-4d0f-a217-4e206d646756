<?php

namespace App\Http\Controllers\Api;

use App\Discount;
use App\Http\Controllers\Controller;
use App\Order;
use App\Product;
use Illuminate\Http\Request;

class CalculateRefundController extends Controller
{
    public function getBreakDown(Request $request)
    {
        $order = Order::find($request->order_id);
        $return_products = $request->products;
        $eGiftRefund = $request->eGift;
        $final = $request->final;

        if (collect($return_products)->isEmpty()) {
            abort(404);
        }

        $products = $order->products;

        $products = collect($products)->filter(function ($product) use ($return_products) {
            return collect($return_products)->contains(function ($p) use ($product) {
                return $p['type'] == $product['type']
                    &&
                    $p['id'] == $product['id']
                    &&
                    data_get($p, 'item_key') == data_get($product, 'item_key');
            });
        });
        $final_products = collect();


        collect($products)->each(function ($product) use ($final, $return_products, &$final_products) {
            $prodcts = collect($return_products)->filter(function ($p) use ($product) {
                return
                    $p['type'] == $product['type']
                    &&
                    $p['id'] == $product['id']
                    &&
                    data_get($p, 'item_key') == data_get($product, 'item_key');
            });

            if (!$prodcts) {
                return;
            }

            collect($prodcts)->each(function ($prodct) use ($final, $product, &$final_products) {
                $new_product = $product;

                $new_product['our_responsibility'] = data_get($prodct, 'our_responsibility') ?? true;

                $original_quantity = $product['quantity'];

                $new_product['quantity'] = $prodct['quantity'];

                $new_product['status'] = 'pending';

                $new_product['reason'] = data_get($prodct, 'reason');

                if (data_get($product, 'refund')) {
                    $new_product['total_amount'] = $prodct['total'];
                } else {
                    $new_product['total_amount'] = $prodct['price'] * $prodct['quantity'];
                }


                $tax_per = $product['tax_amount'] / $original_quantity;

                $new_product['tax_amount'] = $prodct['quantity'] * $tax_per;

                $discount_per = $product['discount_amount'] / $original_quantity;

                $new_product['discount_amount'] = -($prodct['quantity'] * $discount_per);

                $new_product['grand_total'] = $new_product['total_amount'] + $new_product['tax_amount'] - $new_product['discount_amount'];

                $new_product['total'] = $new_product['total_amount'];

                if (data_get($prodct, 'no_refund')) {
                    $new_product['total_amount'] = 0;
                    $new_product['tax_amount'] = 0;
                    $new_product['discount_amount'] = 0;
                    $new_product['grand_total'] = 0;
                    $new_product['total'] = 0;
                }

                $final_products->push($new_product);
            });
        })->filter()->values();

        $products = $final_products;

        $sub_total = collect($products)->pluck('total_amount')->sum();
        $tax_amount = collect($products)->pluck('tax_amount')->sum();
        $discount_amount = collect($products)->pluck('discount_amount')->sum();
        $shipping_refund = 0.00;

        $message = '';

        $discount = Discount::find($order->discount_id);

        if (!$discount || data_get($order, 'discount.lost') == true) {
            $discount_amount = 0.00;
        } else {
            if ($discount->min > ($order->sub_total_left - $sub_total)) {
                $message = "You are losing your $" . number_format($order->discount_amount, 2) . " discount";
                $discount_amount = -$order->discount_amount;
            } elseif ($discount->type == 'percentage') {
                $discount_amount = collect($products)->pluck('discount_amount')->sum();
            } else {
                $discount_amount = 0.00;
            }
        }

        if (collect($products)->map(function ($product) {
            return $product['our_responsibility'];
        })->contains(true)) {
            $shipping_refund = $order->shipping_amount;
            $tax_amount += data_get($order, 'meta.shipping_tax');
        }

        if (collect($products)->map(function ($product) {
            return optional(Product::find($product['product_id']))->exclude_free_shipping;
        })->contains(true)) {
            $shipping_refund = -$order->shipping_amount;
            $tax_amount += data_get($order, 'meta.shipping_tax');
        }

        $grand_total = $sub_total + $tax_amount + $shipping_refund + $discount_amount;

        $creditInfo = [];

        if ($order->chargeLeft) {
            $creditInfo = [
                'type' => data_get($order, 'payments.creditInfo.type'),
                'amount' => min($order->chargeLeft, $grand_total),
                'last_four' => data_get($order, 'payments.creditInfo.last_four'),
                'payment_type' => data_get($order, 'payments.creditInfo.payment_type'),
                'payment_id' => $order->payment_id
            ];
        }

        $giftCards = [];

        if ($grand_total > $order->chargeLeft) {
            $total_left = $grand_total - $order->chargeLeft;

            $giftCards = collect(data_get($order, 'payments.giftCard'))->map(
                function ($card) use (&$total_left, $order) {
                    // $amount = $card['amount'] - min($total_left, $order->TotalRefundedForGiftCard($card['id']));
                    $amount = min($card['amount'] - $order->TotalRefundedForGiftCard($card['id']), $total_left);

                    if ($total_left > 0 && $amount > 0) {
                        $total_left -= $card['amount'];
                        return [
                            'id' => $card['id'],
                            'code' => $card['code'],
                            'amount' => $amount
                        ];
                    }
                }
            )->filter(function ($g) {
                return $g != null;
            })->values();
        }

        $payments = [
            'creditInfo' => $creditInfo,
            'giftCard' => $giftCards
        ];

        if ($eGiftRefund && $final) {
            $payments = [
                'giftCard' => [
                    'eGift' => $eGiftRefund
                ]
            ];
        }


        return [
            'sub_total' => +$sub_total,
            'tax_amount' => +$tax_amount,
            'shipping_amount' => +$shipping_refund,
            'discount_amount' => +$discount_amount,
            'grand_total' => +max($grand_total, 0),
            'message' => $message,
            'payments' => $payments,
            'products' => $products
        ];
    }
}
