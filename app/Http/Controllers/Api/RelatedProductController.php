<?php

namespace App\Http\Controllers\Api;

use App\Bundle;
use App\FilterItem;
use App\Http\Controllers\Controller;
use App\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class RelatedProductController extends Controller
{
    public static $number = 20;

    public function getBundleRelatedProducts(Request $request, $bundleId)
    {
        return Cache::tags(['search'])->remember(
            "related_bundle_{$bundleId}",
            60 * 60 * 24 * 7,
            function () use ($bundleId) {
                return $this->getRelatedProducts(
                    request(),
                    Bundle::find($bundleId)->bundleItems->first()->model->product_id
                )->first();
            }
        );
    }

    public function getRelatedProducts(Request $request, $productId)
    {
        if (!(int)$productId) {
            return collect();
        }
        return Cache::tags(['search'])->remember(
            "related_{$productId}",
            60 * 60 * 24 * 7,
            function () use ($productId) {
                $number = self::$number * 2;
                $this->productId = $productId;
                $this->product = Product::find($productId);
                $relatedProducts = $this->getByTags()->take($number);
                if ($relatedProducts->count() < $number) {
                    $productsFromFilters = $this->getByFilters();
                    $relatedProducts = $relatedProducts->merge($productsFromFilters['filterProducts'])->unique()->take(
                        $number
                    );
                    if ($relatedProducts->count() < $number) {
                        $relatedProducts = $relatedProducts->merge($this->getByCreators())->unique()->take($number);
                        if ($relatedProducts->count() < $number) {
                            $relatedProducts = $relatedProducts->merge(
                                $productsFromFilters['categoryProducts']
                            )->unique()->take($number);
                        }
                    }
                }
                return Product::whereIn('id', $relatedProducts)->isSearchable()->get()->sortBy(
                    function ($product) use ($relatedProducts) {
                        return array_search($product->id, $relatedProducts->toArray());
                    }
                )->take(self::$number)->map(function ($product) {
                    return $product->front_end;
                })->values();
            }
        );
    }

    public function getByTags()
    {
        return DB::table('taggables')->where('taggable_type', '=', 'App\\Product')
            ->whereIn('tag_id', $this->product->tags->pluck('id')->toArray())->pluck('taggable_id')->diff(
                $this->productId
            )
            ->countBy()->sort()->reverse()->keys();
    }

    public function getByFilters()
    {
        $categoryProducts = $this->getByCategories();
        $filterItems = $this->product->filters->pluck('id');
        $filterProducts = DB::table('filter_item_product')
            ->whereIn('filter_item_id', FilterItem::find($filterItems)->pluck('id')->toArray())->get()
            ->groupBy('product_id')->filter(function ($group) use ($filterItems) {
                return $group->count() == $filterItems->count();
            })->pluck('product_id')->diff($this->productId)->intersect($categoryProducts);
        return ['categoryProducts' => $categoryProducts, 'filterProducts' => $filterProducts];
    }

    public function getByCategories()
    {
        return DB::table('category_product')
            ->whereIn('category_id', $this->product->categories->pluck('id'))->pluck('product_id')->diff(
                $this->productId
            )
            ->countBy()->sort()->reverse()->keys()->unique();
    }

    public function getByCreators()
    {
        return DB::table('creator_product')
            ->whereIn('creator_id', $this->product->creators->pluck('id'))->pluck('product_id')->diff($this->productId)
            ->countBy()->sort()->reverse()->keys();
    }
}
