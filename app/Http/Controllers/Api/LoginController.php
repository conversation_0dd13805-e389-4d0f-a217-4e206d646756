<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class LoginController extends Controller
{
    public function login(Request $request)
    {
        if (auth()->attempt($request->only('email', 'password'), true)) {
            return auth()->user()->load([
                'orders',
                'addresses',
                'payments',
            ]);
        }

        abort(403);
    }

    public function logout(Request $request)
    {
        session()->forget('zip_code');
        auth()->logout();
        return response()->json(['message' => 'Successfully logged out.']);
    }
}
