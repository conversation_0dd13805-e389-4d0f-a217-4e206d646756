<?php

namespace App\Http\Controllers\Api;

use App\GiftRecord;
use App\Http\Controllers\ChargeController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\PosController;
use App\Http\Controllers\PosOnlineController;
use App\Http\Controllers\TransactionController;
use App\Jobs\ReturnCancelCreate;
use App\Membership;
use App\Order;

class CancelOrdersController extends Controller
{
    public function whole($order)
    {
        if (auth()->guard('admin')->check()) {
            $order = Order::find($order);
        } else {
            $order = customer()->orders()->findOrFail($order);
        }
        if ($order->status == 'shipped' || $order->status == 'delivered' || $order->status == 'cancelled') {
            abort(400, 'You cannot cancel this order..');
        }

        $products = collect($order->products)->map(function ($product) {
            $type = $product['item_type'];
            if ($type == 'physical' && !data_get($product, 'personalization')) {
                $product['status'] = 'cancelled';
            }
            return $product;
        });

        $order->update([
            'status' => 'cancelled',
            'products' => $products
        ]);

        ReturnCancelCreate::dispatch(
            ChargeController::class,
            'ReturnCancelOrderPayments',
            $order
        );

        $hasNonCancellableItems = $order->HasNonCancellableItems();

        ReturnCancelCreate::dispatch(
            TransactionController::class,
            'DeleteTransaction',
            $order
        )->onQueue('low');

        if ($hasNonCancellableItems) {
            ReturnCancelCreate::dispatch(
                TransactionController::class,
                'AddTransaction',
                $order
            )->onQueue('low');

            ReturnCancelCreate::dispatch(PosController::class, 'CreateOrder', $order)->onQueue(
                'low'
            );
            ReturnCancelCreate::dispatch(
                PosOnlineController::class,
                'CreateOrder',
                $order
            )->onQueue('low');
        } else {
            ReturnCancelCreate::dispatch(PosController::class, 'CancelOrder', $order)->onQueue(
                'low'
            );
            ReturnCancelCreate::dispatch(
                PosOnlineController::class,
                'CancelOrder',
                $order
            )->onQueue('low');
        }
        ReturnCancelCreate::dispatch(Membership::class, 'removePoints', $order);

        ReturnCancelCreate::dispatch(GiftRecord::class, 'removeFromOrder', $order);

        ReturnCancelCreate::dispatch(
            EmailsController::class,
            'SendOrderCancelled',
            $order
        )->onQueue('high');

        ReturnCancelCreate::dispatch(
            InventoryController::class,
            'AddToInventory',
            collect($order->products)->where('item_type', 'physical')
        );
    }
}
