<?php

namespace App\Http\Controllers\Api;

use App\Address;
use App\Http\Controllers\Controller;
use App\Payment;
use App\Subscription;

class SubscriptionController extends Controller
{
    public function index()
    {
        $user = $this->user();
        return $user->subscriptions->map(function ($subscription) {
            if (!$subscription->first_products && $subscription->subscriptionType->done()) {
                return;
            }
            return [
                'id' => $subscription->id,
                'string' => $subscription->string,
                'shipping' => $subscription->shipping,
                'payments' => $subscription->payments,
                'first_products' => $subscription->first_products ?? $subscription->any_product,
                'subscription_group' => [
                    'name' => $subscription->subscriptionGroup->name,
                ],
                'from' => $subscription->from,
            ];
        })->filter()->values();
    }

    private function user()
    {
        return customer();
    }

    public function show($subscription)
    {
        $user = $this->user();

        $subscription = $user->subscriptions
            ->find($subscription);//->load(['address', 'payment']);

        $subscription['email'] = $user->email;

        return $subscription;
    }

    public function create()
    {
        $user = $this->user();

        $subscription = $user->subscriptions()->create([
            'status' => 'active',
            'shipping' => collect(Address::find(request()->address_id)->toArray())
                ->only(
                    'id',
                    'address_line_1',
                    'address_line_2',
                    'city',
                    'state',
                    'country',
                    'name',
                    'phone',
                    'postal_code'
                )
                ->toArray(),
            'payments' => collect(Payment::find(request()->payment_id)->toArray())
                ->only(
                    'id',
                    'address_line_1',
                    'address_line_2',
                    'city',
                    'country',
                    'created_at',
                    'expDate',
                    'last_four',
                    'name',
                    'postal_code',
                    'securityCode',
                    'state',
                    'type'
                )
                ->toArray(),
            'subscription_type_id' => request()->subscription_type_id,
            'subscription_group_id' => request()->subscription_group_id,
            'quantity' => request()->quantity,
            'payment_id' => request()->payment_id,
            'address_id' => request()->address_id,
            'status' => request()->status,
        ]);

        return $subscription->id;
    }

    public function cancel()
    {
        $user = $this->user();

        $request = request();

        $user->subscriptions()
            ->findOrFail($request->id)
            ->update(['status' => 'canceled']);

        return;
    }

    public function update()
    {
        $user = $this->user();

        $request = request();

        $subscription = $user->subscriptions()
            ->findOrFail($request->id);

        $combination = $subscription->combination;
        
        $subscription->update([
            'quantity' => $request->quantity,
            'payment_id' => $request->payment_id,
            'address_id' => $request->address_id,
            'shipping' => collect(\App\Address::find(request()->address_id)->toArray())
                ->only('id', 'address_line_1', 'address_line_2', 'city', 'state', 'country', 'name', 'phone', 'postal_code')
                ->toArray(),
            'payments' => collect(\App\Payment::find($request->payment_id)->toArray())
                ->only('id', 'address_line_1', 'address_line_2', 'city', 'country', 'created_at', 'expDate', 'last_four', 'name', 'postal_code', 'securityCode', 'state', 'type')
                ->toArray(),
        ]);

        if ($combination->upcoming_date->isBefore(today()->addDays($combination::$createOrderDays))) {
            // create the order, it won't be created automatically
            // ${\App\Combination::$createOrderDays} days before $upcoming_date
            $subscription->fresh()->createOrder($combination->upcoming_date);
        }

        return $request->id;
    }

    public function lookup($type_id, $group_id)
    {
        $data = Subscription::make([
            'subscription_type_id' => $type_id,
            'subscription_group_id' => $group_id,
        ])->toArray();
        $data['subscription_group'] = [
            'name' => data_get($data, 'subscription_group.name')
        ];
        $data['subscriptiontype'] = [
            'name' => data_get($data, 'subscription_type.name')
        ];
        return $data;
    }
}
