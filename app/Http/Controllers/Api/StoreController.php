<?php

namespace App\Http\Controllers\Api;

use App\ClosedDay;
use App\FulfillmentSchematic;
use App\Http\Controllers\Controller;

class StoreController extends Controller
{
    public $store;
    public $now;
    public $next_closed;

    public function index()
    {
        $this->now = now();
        $time = ($this->now->format('H') + $this->now->format('i') / 60);
        $day = strtolower($this->now->format('l'));
        $day_open = $day . '_open';
        $day_close = $day . '_close';

        $this->store = FulfillmentSchematic::where('delivery', false)->first();
        $this->next_closed = ClosedDay::orderBy('date')->whereDate('date', '>=', $this->now->startOfDay())->limit(12);
        $first_date = $this->next_closed->get()->first();

        if (!$first_date) {
            $open = $time > $this->store->$day_open && $time < $this->store->$day_close;
        } else {
            $open = $first_date->date->isToday()
                ? $first_date->open && ($time > $first_date->open && $time < $first_date->close)
                : $time > $this->store->$day_open && $time < $this->store->$day_close;
        }

        return [
            'Regular' => [
                ['day' => 'Sunday', 'time' => $this->getTime('sunday_')],
                ['day' => 'Monday', 'time' => $this->getTime('monday_')],
                ['day' => 'Tuesday', 'time' => $this->getTime('tuesday_')],
                ['day' => 'Wednesday', 'time' => $this->getTime('wednesday_')],
                ['day' => 'Thursday', 'time' => $this->getTime('thursday_')],
                ['day' => 'Friday', 'time' => $this->getTime('friday_')],
                ['day' => 'Saturday', 'time' => $this->getTime('saturday_')],
            ],
            'Holidays' => $this->next_closed->get()->map(function ($day) {
                return [
                    'time' => $this->getTime('', $day),
                    'date' => $day->date->format('F j'),
                    'day' => $day->name,
                ];
            }),
            'Current' => [
                'open' => $open,
                'next_time' => $open ? $this->next_close_time() : $this->next_open_time(),
            ]
        ];
    }

    public function getTime($column, $table = null)
    {
        if (!$table) {
            $table = $this->store;
        }

        $open = $column . 'open';
        $close = $column . 'close';

        if (!$table->$open) {
            return 'Closed';
        }

        $string = '';

        $hour = floor($table->$open);
        $minutes = ($table->$open - $hour) * 60;

        $format = $minutes ? 'g:iA' : 'gA';
        $string .= now()
            ->setHours($hour)
            ->setMinute($minutes)
            ->format($format);

        $string .= ' - ';

        $hour = floor($table->$close);
        $minutes = ($table->$close - $hour) * 60;

        $format = $minutes ? 'g:iA' : 'gA';
        $string .= now()
            ->setHours($hour)
            ->setMinute($minutes)
            ->format($format);

        return $string;
    }

    public function next_close_time()
    {
        if ($this->next_closed->first() && $this->next_closed->first()->date->isToday()) {
            $hour = floor($this->next_closed->first()->close);
            $minutes = ($this->next_closed->first()->close - $hour) * 60;

            $format = $minutes ? 'g:iA' : 'gA';
            return now()
                ->setHours($hour)
                ->setMinute($minutes)
                ->format($format);
        } else {
            $close = strtolower($this->now->format('l')) . '_close';

            $hour = floor($this->store->$close);
            $minutes = ($this->store->$close - $hour) * 60;

            $format = $minutes ? 'g:iA' : 'gA';
            return now()
                ->setHours($hour)
                ->setMinute($minutes)
                ->format($format);
        }
    }

    public function next_open_time()
    {
        $a = 1;
        $temp = now();
        while ($a <= 10) {
            $day = strtolower($temp->format('l')) . '_open';
            $closed_day = ClosedDay::whereDate('date', $temp)->first();
            if (
                ($closed_day && !$closed_day->open)
                || !$this->store->$day
            ) {
                $temp = $temp->addDay();
                $a++;
            } else {
                break;
            }
        }
        if ($closed_day) {
            $hour = floor($closed_day->open);
            $minutes = ($closed_day->open - $hour) * 60;
            return $temp
                ->setHours($hour)
                ->setMinute($minutes)
                ->calendar();
        } else {
            $day = strtolower($this->now->format('l'));
            $day_open = $day . '_open';

            $hour = floor($this->store->$day_open);
            $minutes = ($this->store->$day_open - $hour) * 60;
            return $temp
                ->setHours($hour)
                ->setMinute($minutes)
                ->calendar();
        }
    }

    public function today()
    {
        if ($this->next_closed->first()->date->isToday()) {
            return [
                'open' => $this->next_closed->first()->open,
                'close' => $this->next_closed->first()->close,
            ];
        } else {
            $day = strtolower($this->now->format('l'));
            $day_open = $day . '_open';
            $day_close = $day . '_close';
            return [
                'open' => $this->store->$day_open,
                'close' => $this->store->$day_close,
            ];
        }
    }
}
