<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Traits\FacetTrait;
use App\Vendor;
use Illuminate\Http\Request;

class VendorController extends Controller
{
    use FacetTrait;

    // public function products($slug, Vendor $vendor)
    // {
    //     return $this->ProductWithFacets($vendor->products());
    // }
    public function products(Request $request, $slug, Vendor $vendor)
    {
        if (!$request->q) {
            $request = $request->merge(['q' => ' ']);
        }
        $request->merge(['all_vendors' => $vendor->name,]);
        return collect((new SearchController)->index($request, collect(['and_vendors' => [$vendor->name]])))->merge([
            'breadcrumbs' => [
                ['name' => 'Home', 'path' => '/'],
                ['name' => $vendor->name],
            ]
        ]);
    }

    public function multi($ids)
    {
        return Vendor::find(explode(',', $ids));
    }
}
