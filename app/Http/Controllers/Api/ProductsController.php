<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Order;
use App\Product;
use Carbon\Carbon;
use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class ProductsController extends Controller
{
    protected $collection;

    // public function index(Request $request)
    // {
    //     $products = Product::active()->with([
    //         'variations',
    //         'variationInfos.product',
    //         'vendor',
    //         'filters.filter',
    //         'categories'
    //     ]);

    //     $products = $this->queryFilters($products, $request);
    //     $products = $this->queryCategories($products, $request);
    //     $products = $this->queryVendors($products, $request);
    //     $products = $this->queryVariations($products, $request);

    //     return [
    //         'products' => $products->paginate(40),
    //         'filters' => $this->getFilters($request),
    //         'categories' => $this->getCategories($request),
    //         'vendors' => $this->getVendors($request),
    //         'variations' => $this->getVariations($request),
    //     ];
    // }

    public static function getMax(Request $request)
    {
        return GetFromFrontEnd($request->products)->map(function ($product) {
            return $product->getFrontEndAttribute($product->quantity);
        });
    }

    public function show($slug, Product $product)
    {
        $this->addToRecent($product);

        if ($product->slug != $slug && !request()->header('app')) {
            abort(302, $product->path);
        }

        return Cache::remember("product_{$product->id}", 60 * 60 * 24 * 7, function () use ($product) {
            $data = $product->append([
                'links',
                'media_urls',
                'breadcrumbs',
                'subscriptions',
                'recurring_array',
                'front_end_add_ons',
            ])->toArray();

            if ($data['vendor'] && $data['vendor']['enable_sell_out_of_stock']) {
                $data['enable_sell_out_of_stock'] = true;
                $data['out_of_stock_delivery_days'] = $data['vendor']['out_of_stock_delivery_days'] ?? 1;
            } else {
                $data['enable_sell_out_of_stock'] = false;
                $data['out_of_stock_delivery_days'] = null;
            }

            if ($data['enable_sell_out_of_stock'] && $data['out_of_stock'] && $data['allow_sell_out_of_stock']) {
                $data['preorder_date'] = Carbon::now()->addDays($data['out_of_stock_delivery_days']);
            }

            $images = $product->getMedia('variations');

            $data['variation_infos'] = $product->variationInfos
                ->where('visibility', true)
                ->map(function ($item) use ($images, $data) {
                    $image = $images->first(function ($i) use ($item) {
                        return $i->getCustomProperty('_id') == $item->image;
                    });

                    $result = $item->getFrontEndAttribute() + [
                            'picture' => !$image ? null : [
                                'grid' => $image->getUrl('grid'),
                                'large' => $image->getUrl('large'),
                                'lightbox' => $image->getUrl('lightbox'),
                                'thumbnail' => $image->getUrl('thumbnail'),
                            ],
                        ];

                    if ($data['enable_sell_out_of_stock'] && $result['out_of_stock'] && $data['allow_sell_out_of_stock']) {
                        $result['preorder_date'] = Carbon::now()->addDays($data['out_of_stock_delivery_days']);
                    }
                    return $result;
                })->values();
            $data['variations'] = collect($product->variations)->map(function ($variation) use ($images) {
                $variation->images = collect($variation->images)->map(function ($item) use ($images) {
                    return [
                        'value' => $item['name'],
                        'url' => optional(
                            $images->filter(function ($value) use ($item) {
                                return data_get($value['custom_properties'], '_id') == $item['id'];
                            })->first()
                        )->getUrl(),
                    ];
                })->filter(function ($item) {
                    return $item['value'] && $item['url'];
                })->values();

                return [
                    'id' => $variation->id,
                    'name' => $variation->name,
                    'info' => $variation->info,
                    'values' => $variation->values,
                    'images' => collect($variation->images)->count() == collect($variation->values)->count()
                        ? $variation->images
                        : [],
                ];
            });

            $data['type'] = 'product';
            $data['label'] = $this->getLabel($product);
            if ($product->getExtendedDuration()) {
                $data['rates'] = (new ShippingController)->productRate($product);
            }
            if (!empty($data['vendor']) && !$data['vendor']['is_visible']){
                $data['vendor'] = null;
            }

            return $data;
        });
    }

    public function addToRecent($product)
    {
        $collection = session('recent', collect([]))->prepend([
            'type' => 'product',
            'id' => $product->id,
            'time' => time(),
        ]);

        $collection = $collection->groupBy(['type', 'id'])
            ->map(function ($type) {
                return $type->map(function ($item) {
                    return $item->sortByDesc('time')->first();
                });
            })
            ->flatten(1)
            ->sortByDesc('time')
            ->slice(0, 30)
            ->values();

        session(['recent' => $collection]);
        session()->save();


        if ($user = auth()->user()) {
            $user->update(['recent' => $collection]);
        }
    }

    public function getLabel($product)
    {
        if ($product->getLabel()) {
            return $product->getLabel();
        } elseif (!!$product->release_date && now()->isBefore($product->release_date)) {
            return [
                'name' => 'PRE-ORDER',
                'color' => '#BB1E1E',
            ];
        } elseif ($product->release_date && $product->release_date->isAfter(now()->subDays(30 * 5))) {
            return [
                'name' => 'New' . ($product->creators->count() > 0 ? ' Release' : ''),
                'color' => '#0C9E32',
            ];
        }
    }

    public function download($slug, Product $product)
    {
        if ($user = auth()->user()) {
            $download = $user->digitals->contains(function ($key, $like) use ($product, &$download) {
                return $key->model->id == $product->id;
                if ($key->model->id == $product->id) {
                    return $this->downloadFile($product);
                    return $product->getMedia('digital')->first();
                }
            });
            if ($download) {
                $mediaItem = $product->getMedia('digital')->first();
                return redirect($mediaItem->getTemporaryUrl(now()->addMinutes(1)));
            }
        } else {
            try {
                $decrypted = decrypt(request()->token);
            } catch (DecryptException $e) {
                abort(404);
            }

            $download = false;
            $order = Order::findOrFail($decrypted);
            $products = collect($order->products); //->where('id', $product->id);

            $products = $products->map(function ($item) use ($product, &$download) {
                if (
                    ($item['item_type'] == 'digital' || $item['item_type'] == 'both')
                    && $item['product_id'] == $product->id
                ) {
                    if ($item['times_left'] < 1) {
                        abort(403, 'no more downloads left.');
                    }
                    $download = true;
                    $item['times_left'] = $item['times_left'] - 1;
                    return $item;
                }
                return $item;
            });
            $order->update(['products' => $products]);
            if ($download) {
                $mediaItem = $product->getMedia('digital')->first();
                return redirect($mediaItem->getTemporaryUrl(now()->addMinutes(1)));
            }
            abort(403, 'no more downloads left.');
        }
    }

    public function downloadFile($product)
    {
        $file = $product->getMedia('digital')->first();
        $path = $file->getUrl();

        header("Cache-Control: public");
        header("Content-Description: File Transfer");
        header("Content-Disposition: attachment; filename=" . basename($path));
        header("Content-Type: " . $file->mime_type);

        return readfile($path);
    }

    public function getPersonalization($slug, Product $product)
    {
        return $product->personalizations;
    }

    // public function QueryFilters($products, $request)
    // {
    //     if ($request->filters) {
    //         $products = $products->whereHas('filters', function ($query) use ($request){
    //             $query->whereIn('filter_item_id', explode(',', $request->filters));
    //         });
    //     }
    //     return $products;
    // }
    // public function QueryCategories($products, $request)
    // {
    //     if ($request->categories) {
    //         $products = $products->whereHas('categories', function ($query) use ($request) {
    //             $query->whereIn('category_id', explode(',', $request->categories));
    //         });
    //     }
    //     return $products;
    // }
    // public function QueryVendors($products, $request)
    // {
    //     if ($request->vendors) {
    //         $products = $products->whereIn('vendor_id', explode(',', $request->vendors));
    //     }
    //     return $products;
    // }

    // public function QueryVariations($products, $request)
    // {
    //     if ($request->variations) {
    //         foreach ($request->variations as $key => $variation) {
    //             if($variation) {
    //                 $products = $products->whereHas('variations', function ($query) use ($key, $variation) {
    //                     $query->where('name', $key)->where(function ($query) use ($variation) {
    //                         foreach (explode(',', $variation) as $key => $value) {
    //                             $query->orWhereJsonContains('values', $value);
    //                         }
    //                     });
    //                 });
    //             }
    //         }
    //     }
    //     return $products;
    // }
    // public function getFilters($request)
    // {
    //     $products = Product::active();
    //     $products = $this->queryCategories($products, $request);
    //     $products = $this->queryVendors($products, $request);
    //     $products = $this->queryVariations($products, $request);

    //     return collect(
    //         $products
    //         ->active()
    //         ->get()
    //         ->pluck('filters')
    //         ->flatten()
    //         ->unique('id')
    //         ->values()
    //         ->toArray()
    //     )->groupBy('parent')
    //     ->map(function ($item) {
    //         return [
    //             'name' => $item[0]['parent'],
    //             'information' => \App\Filter::find($item[0]['filter_id'])->info,
    //             'items' => $item
    //         ];
    //     });
    // }
    // public function getVendors($request)
    // {
    //     $products = Product::active();
    //     $products = $this->queryFilters($products, $request);
    //     $products = $this->queryCategories($products, $request);
    //     $products = $this->queryVariations($products, $request);

    //     return $products->get()->pluck('vendor')->flatten()->unique('id')->values()->filter();
    // }
    // public function getCategories($request)
    // {
    //     $products = Product::active();
    //     $products = $this->queryFilters($products, $request);
    //     $products = $this->queryVendors($products, $request);
    //     $products = $this->queryVariations($products, $request);

    //     return $products->get()->pluck('categories')->flatten()->unique('id')->values();
    // }

    // public function getVariations($request)
    // {
    //     $products = Product::active();
    //     $products = $this->queryFilters($products, $request);
    //     $products = $this->queryCategories($products, $request);
    //     $products = $this->queryVendors($products, $request);

    //     return $products->get()->pluck('variations')
    //         ->flatten()
    //         ->unique('id')
    //         ->values()
    //         ->groupBy('name')
    //         ->map(function ($item) {
    //             return $item->pluck('values')->flatten()->unique()->values();
    //         });
    // }
}
