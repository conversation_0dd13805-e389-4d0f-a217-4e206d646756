<?php

namespace App\Http\Controllers\Api;

use App\Bundle;
use App\Http\Controllers\Controller;

class BundlesController extends Controller
{
    public function show($slug, Bundle $bundle)
    {
        $this->addToRecent($bundle);

        $array = $bundle->toArray();

        if (!$bundle->show_products) {
            $array['front_end_products'] = [];
        }

        unset($array['bundle_items']);
        unset($array['media']);
        unset($array['categories']);
        unset($array['filters']);
        $array['type'] = 'bundle';

        return $array;
    }

    public function addToRecent($bundle)
    {
        $collection = session('recent', collect([]))->prepend([
            'type' => 'bundle',
            'id' => $bundle->id,
            'time' => time(),
        ]);

        $collection = $collection->groupBy(['type', 'id'])
            ->map(function ($type) {
                return $type->map(function ($item) {
                    return $item->sortByDesc('time')->first();
                });
            })
            ->flatten(1)
            ->sortByDesc('time')
            ->slice(0, 30)
            ->values();

        session(['recent' => $collection]);

        if ($user = auth()->user()) {
            $user->update(['recent' => $collection]);
        }
    }

    public function related($slug, Bundle $bundle)
    {
        return [];

        return self::active()->inRandomOrder()->limit(12)->get();
    }
}
