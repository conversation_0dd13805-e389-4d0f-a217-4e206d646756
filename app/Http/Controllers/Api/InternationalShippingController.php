<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Setting;
use GuzzleHttp\Client;

class InternationalShippingController extends Controller
{
    public function GetCarriers()
    {
        $client = new Client;
        $response = $client->get("https://api.shipengine.com/v1/carriers", [///se-123040/services", [
            'headers' => [
                'API-Key' => env('SHIP_ENGINE_API'),
            ],
        ]);

        $result = $response->getBody()->getContents();
        return collect(json_decode($result)->carriers)->map(function ($r) {
            return [
                'Carrier_Name' => $r->friendly_name,
                'Services' => collect($r->services)->map(function ($s) {
                    return [
                        'Service' => $s->name,
                        'Code' => $s->service_code,
                        'Domestic' => $s->domestic,
                        'International' => $s->international
                    ];
                })
            ];
        });
    }

    public function AddServicesByCountry()
    {
        $methods = Setting::where('key', 'methods')->first();

        return $methods->meta;
    }

    public function RemoveMethods()
    {
    }

    public function GetServicesByCountry()
    {
    }

    public function GetEstimate()
    {
    }

    public function GetAccurate()
    {
    }


}
