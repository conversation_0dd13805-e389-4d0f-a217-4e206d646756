<?php

namespace App\Http\Controllers\Api;

use App\Discount;
use App\Http\Controllers\Controller;
use App\InternationalShippingZone;
use App\ShippingOption;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class InternationalRatesController extends Controller
{
    public static function GetAllCarriers()
    {
        $client = new Client;
        $response = $client->get("https://api.shipengine.com/v1/carriers", [
            'headers' => [
                'API-Key' => env('SHIP_ENGINE_API'),
            ],
        ]);

        $result = $response->getBody()->getContents();
        $result = forceArray($result);

        return collect($result['carriers'])->map(function ($carrier) {
            return [
                'carrier_id' => $carrier['carrier_id'],
                'carrier_code' => $carrier['carrier_code'],
                'carrier_name' => $carrier['friendly_name'],
                'services' => collect($carrier['services'])->where('international')->map(function ($service) {
                    return [
                        'service_code' => $service['service_code'],
                        'service_name' => $service['name']
                    ];
                })->values()
            ];
        });
    }

    public static function price($shippingId, $products, $country, $zip, $freeShipping, $discountAmount = 0)
    {
        if (!$shippingId) {
            return [
                'shippingAmount' => 0,
                'original_shipping' => 0
            ];
        }
        $request = request()->merge([
            'products' => makeForFronEnd($products),
            'country' => $country,
            'zip' => $zip,
        ]);

        $shipping = ShippingOption::find($shippingId)->internationalShippingZones->filter(
            function ($zone) use ($country) {
                return Str::contains($zone->countries_text, $country);
            }
        )->first()->options->where('shipping_option_id', $shippingId)->first();

        $option = collect(self::rates($request))->where('id', $shipping->shipping_option_id)->first();

        if (($option['free_shipping_above'] && $option['free_shipping_above'] <= ($option['price'] - $discountAmount)) || $freeShipping) {
            $products = $products->filter(function ($product) {
                return $product->model->exclude_free_shipping;
            });

            if ($products->isEmpty()) {
                return [
                    'shippingAmount' => 0,
                    'original_shipping' => $freeShipping ? $option['price'] : 0
                ];
            }
        }

        return [
            'shippingAmount' => $option['price'],
            'original_shipping' => 0
        ];
    }

    public static function rates(Request $request)
    {
        $country = $request->country;
        $zip = $request->zip;

        $discountId = $request->discountId;
        $discount = Discount::find($discountId);

        $location = [
            'country' => $country,
            'postal_code' => $zip
        ];

        $products = GetFromFrontEnd($request->products);

        if (!$products->map(function ($product) {
            return $product->shippable;
        })->contains(true)) {
            return [
                'price' => 0,
            ];
        }

        if ($discount) {
            $discount = $discount->GetSavings($products, $location);
        }

        $products_excluded = collect($products)->filter(function ($product) {
            return $product->model->exclude_free_shipping;
        });

        $weight = $products->map(function ($item) {
            return $item->weight;
        })->sum();

        $weight = $weight > 0 ? $weight : 1;

        $total = $products->map(function ($item) {
            return $item->price;
        })->sum();

        $shipping_zone = InternationalShippingZone::get()->filter(function ($zone) use ($country) {
            return Str::contains($zone->countries_text, $country);
        })->first();

        if (!$shipping_zone) {
            abort(404);
        }
        $shiping_options = $shipping_zone->options->where('active');

        $carriers = $shiping_options->map(function ($option) {
            $option = json_decode($option->carrier_info);
            return data_get($option, 'value.carrier_id');
        })->values();

        $services = $shiping_options->map(function ($option) {
            $service = json_decode($option->service_info);
            return [
                'service' => data_get($service, 'value.service_code'),
                'option_id' => $option->id
            ];
        });

        $prices = self::GetRateByService($country, $zip, $services, $carriers, $weight);

        $final = collect($prices)->map(
            function ($p) use ($shiping_options, $products_excluded, $total, $discount, $products) {
                $option = $shiping_options->where('id', $p['shipping_option_id'])->first();

                if ($option->free_shipping_above && $option->free_shipping_above <= $total && collect(
                        $products_excluded
                    )->isEmpty()) {
                    $price = 0.00;
                } else {
                    $price = $p['price'] + $option->base_rate;
                }

                if ($discount) {
                    $free = data_get($discount, 'shipping_options');

                    if ($free && collect($free)->pluck('id')->contains($option->shipping_option_id)) {
                        $price = 0;
                    }
                }

                $personalDuration = (new BagController)->getPersonalTotals($products)['duration'] ?? 0;

                return [
                    'base_rate' => $option->base_rate,
                    'id' => $option->shippingOption->id,
                    'option_id' => $option->shipping_option_id,
                    'estimated_arrival' => $p['delivery_days'] != null ? 'Get it in ' . ($p['delivery_days'] + $personalDuration) . ' - ' . (+$p['delivery_days'] + 2 + $personalDuration) . ' Bussines days' : '',
                    'estimated_arrival' => now()->parse($p['estimated_delivery_date'])->addDays(
                        $personalDuration
                    )->toDateTimeString(),
                    'days' => ($p['delivery_days'] + $personalDuration),
                    'international' => true,
                    'delivery' => true,
                    'free_shipping_above' => $option->free_shipping_above,
                    'name' => $option->shippingOption->visible_name,
                    'price' => $price
                ];
            }
        );

        return $final;
    }

    public static function GetRateByService($country, $zip, $services, $carriers, $weight)
    {
        $client = new Client;

        $response = $client->post('https://api.shipengine.com/v1/rates/estimate', [
            'headers' => [
                'Content-Type' => 'application/json',
                'API-Key' => env('SHIP_ENGINE_API'),
            ],
            'json' => [
                "carrier_ids" => $carriers,
                "from_country_code" => settings()->getValue('shipping_country'),
                "from_postal_code" => settings()->getValue('shipping_postal_code'),
                "to_country_code" => $country,
                "to_postal_code" => $zip,
                "weight" => ["value" => $weight, "unit" => "pound"],
            ]
        ]);

        $result = $response->getBody()->getContents();
        $result = forceArray($result);

        $rates = collect($services)->map(function ($service) use ($result) {
            return collect($result)->where('service_code', $service['service'])->map(function ($rate) use ($service) {
                return [
                    'price' => $rate['shipping_amount']['amount'],
                    'delivery_days' => $rate['delivery_days'],
                    'estimated_delivery_date' => $rate['estimated_delivery_date'],
                    'shipping_option_id' => $service['option_id']
                ];
            })->values()->sortBy('price')->first();
        });


        return collect($rates)->filter()->sortBy('price')->values();
    }
}
