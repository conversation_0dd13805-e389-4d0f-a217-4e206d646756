<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Redirect;
use Illuminate\Http\Request;

class RedirectController extends Controller
{
    public function index(Request $request)
    {
        $url = $request->path;

        $redirect = Redirect::fromUrls()
            ->where('from', env('APP_URL') . $url)
            ->first();
        if ($redirect) {
            if (data_get($redirect, 'to_type') == 'url') {
                abort(302, '/' . data_get($redirect, 'to'));
            } else {
                abort(302, '/search?q=' . data_get($redirect, 'to'));
            }
        } else {
            abort(404);
        }
    }
}
