<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\PaypalPayment;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Redirect;

class PayPalController extends Controller
{
    //https://developer.paypal.com/docs/api/payments/v2/
    public static function Void($id)
    {
        $pay_pal = PaypalPayment::find($id);
        $auth_id = forceArray($pay_pal->authorization)['id'];
        $client = new Client;
        $response = $client->post("https://api.paypal.com/v2/payments/authorizations/{$auth_id}/void", [
            'auth' => [
                env('PAY_PAL_CLIENT_ID'),
                env('PAY_PAL_SECRET')
            ],
            'headers' => [
                'Content-Type' => 'application/json',
            ]
        ]);
        $result = $response->getBody()->getContents();
        $pay_pal->update([
            'status' => 'VOIDED',
        ]);
        return $result;
    }

    public static function AuthorizePP($order, $amount)
    {
        $pay_pal = PaypalPayment::find($order->payment_id);
        $amount = number_format($amount, 2);
        if ($pay_pal->authorization != null) {
            return;
        }
        try {
            $client = new Client;
            $response = $client->post("https://api.paypal.com/v1/payments/orders/{$pay_pal->o_id}/authorize", [
                'auth' => [
                    env('PAY_PAL_CLIENT_ID'),
                    env('PAY_PAL_SECRET')
                ],
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    "amount" => [
                        "total" => $amount,
                        "currency" => "USD"
                    ],
                ]
            ]);
            $result = $response->getBody()->getContents();
            $pay_pal->update([
                'status' => forceArray($result)['state'],
                'authorization' => [
                    'id' => forceArray($result)['id'],
                    'amount' => forceArray($result)['amount'],
                    'date' => now(),
                    'valid_until' => forceArray($result)['valid_until']
                ],
                'authorization->amount->total_left' => forceArray($result)['amount']['total'],
                'amount->total' => forceArray($result)['amount']['total']
            ]);
            $order->update([
                'status' => 'paid',
                'payment_status' => 'authorized'
            ]);
        } catch (Exception $ex) {
            $order->update([
                'status' => 'cancelled',
                'payment_status' => 'declined'
            ]);
            EmailsController::SendPaymentError($order);
            abort(400);
        }
    }

    public static function Capture($order, $amount)
    {
        $pay_pal = PaypalPayment::find($order->payment_id);
        $amount = number_format($amount, 2);
        $auth_id = forceArray($pay_pal->authorization)['id'];
        try {
            $client = new Client;
            $response = $client->post("https://api.paypal.com/v1/payments/orders/{$auth_id}/capture", [
                'auth' => [
                    env('PAY_PAL_CLIENT_ID'),
                    env('PAY_PAL_SECRET')
                ],
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    "amount" => [
                        "currency" => "USD",
                        "total" => $amount,
                    ],
                ]
            ]);
            $result = $response->getBody()->getContents();
            $captures = $pay_pal->captures['captures'] ?? [];
            array_push($captures, [
                'id' => forceArray($result)['id'],
                'amount' => forceArray($result)['amount'],
                'fees' => forceArray($result)['transaction_fee'],
                'date' => now(),
            ]);
            $pay_pal->update([
                'captures' => [
                    'captureId' => forceArray($result)['id'],
                    'captures' => $captures
                ],
                'authorization->amount->total_left' => data_get($pay_pal, 'authorization.amount.total_left') - $amount,
            ]);
            $order->update([
                'status' => 'paid',
                'payment_status' => 'paid'
            ]);
        } catch (Exception $ex) {
            $order->update([
                'status' => 'cancelled',
                'payment_status' => 'declined'
            ]);
            EmailsController::SendPaymentError($order);
            abort(400);
        }
    }

    public static function Refund($id, $capture_id, $amount)
    {
        $pay_pal = PaypalPayment::find($id);
        $amount = number_format($amount, 2);

        $pay_pal->update([
            'authorization->amount->total_left' => data_get($pay_pal, 'authorization.amount.total_left') - $amount,
        ]);

        if (!$capture_id) {
            return;
        }

        $client = new Client;
        $response = $client->post("https://api.paypal.com/v1/payments/capture/{$capture_id}/refund", [
            'auth' => [
                env('PAY_PAL_CLIENT_ID'),
                env('PAY_PAL_SECRET')
            ],
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'json' => [
                "amount" => [
                    "currency" => "USD",
                    "total" => $amount,
                ],
            ]
        ]);
        $result = $response->getBody()->getContents();
        $refunds = $pay_pal->refunds ?? [];
        array_push($refunds, [
            'id' => forceArray($result)['id'],
            'amount' => forceArray($result)['amount'],
            'date' => now(),
        ]);
        $pay_pal->update([
            'refunds' => $refunds,
        ]);
        return $result;
    }

    public function StartPayPal(Request $request)
    {
        $breakdown = $request->breakdown;
        $pay_pal = PaypalPayment::create();
        $sub_total = $breakdown['sub_total'] - data_get($breakdown, 'discount');
        $total = ($sub_total + $breakdown['tax'] + $breakdown['shipping']);

        $client = new Client;
        $response = $client->post('https://api.paypal.com/v1/payments/payment', [
            'auth' => [
                env('PAY_PAL_CLIENT_ID'),
                env('PAY_PAL_SECRET')
            ],
            'headers' => [
                'Prefer' => 'return=representation',
                'Content-Type' => 'application/json',
            ],
            'json' => [
                "intent" => "order",
                "payer" => [
                    "payment_method" => "paypal"
                ],
                "transactions" => [
                    [
                        "amount" => [
                            "total" => number_format($total, 2),
                            "currency" => "USD",
                            "details" => [
                                "subtotal" => $sub_total,
                                "tax" => $breakdown['tax'],
                                "shipping" => $breakdown['shipping'],
                            ]
                        ],
                        "invoice_number" => $pay_pal->id,
                    ]
                ],
                "redirect_urls" => [
                    // "return_url" => env('APP_URL') . "/pp-approve?pp_id={$pay_pal->id}&return_url={$returnUrl}",
                    // "cancel_url" => env('APP_URL') . "/pp-cancel?pp_id={$pay_pal->id}&return_url={$returnUrl}"
                    "return_url"=> env('APP_URL')."/pp-approve?pp_id={$pay_pal->id}&return_url={$request->returnUrl}",
                    "cancel_url"=> env('APP_URL')."/pp-cancel?pp_id={$pay_pal->id}&return_url={$request->returnUrl}"
                ]
            ]
        ]);
        $result = $response->getBody()->getContents();
        $pay_pal->update([
            'payment_id' => forceArray($result)['id'],
            'status' => forceArray($result)['state']
        ]);
        return collect(forceArray($result)['links'])->where('method', 'REDIRECT')->first()['href'];
    }

    public function Approve(Request $request)
    {
        $client = new Client;
        $response = $client->post("https://api.paypal.com/v1/payments/payment/{$request->paymentId}/execute", [
            'auth' => [
                env('PAY_PAL_CLIENT_ID'),
                env('PAY_PAL_SECRET')
            ],
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'json' => [
                "payer_id" => $request->PayerID,
            ]
        ]);
        $result = $response->getBody()->getContents();
        $pay_pal = PaypalPayment::find($request->pp_id);
        $pay_pal->update([
            'payment_id' => $request->paymentId,
            'token' => $request->token,
            'payer_id' => $request->PayerID,
            'status' => forceArray($result)['state'],
            'payer_info' => forceArray($result)['payer']['payer_info'],
            'amount' => forceArray($result)['transactions'][0]['amount'],
            'o_id' => forceArray($result)['transactions'][0]['related_resources'][0]['order']['id']
        ]);
        if ($request->returnUrl) {
            return Redirect::to($request->returnUrl . "?payer_id={$pay_pal->payer_id}&token={$pay_pal->token}");
        }
        return redirect(env('APP_URL') . "/review?payer_id={$pay_pal->payer_id}&token={$pay_pal->token}");
    }

    public function Cancel(Request $request)
    {
        $pay_pal = PaypalPayment::find($request->pp_id);
        $pay_pal->update([
            'token' => $request->token,
            'status' => 'cancelled'
        ]);
        if ($request->returnUrl) {
            return Redirect::to($request->returnUrl);
        }
        return redirect(env('APP_URL') . "/checkout");
    }
}
