<?php

namespace App\Http\Controllers\Api;

use App\Discount;
use App\Http\Controllers\Controller;
use App\ZipCode;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use TaxJar\Client;

class BagController extends Controller
{
    public function getBreakDown(Request $request)
    {
        $bags = GetFromFrontEnd($request->products);
        $address = $request->address;
        $discountId = $request->discountId ?? null;
        $shippingId = $request->shippingId;
        $shipping_amount = $request->shippingAmount ?? null;
        $final = $request->final ?? true;

        $this->discount = Discount::find($discountId);
        $freeShipping = false;

        if ($this->discount && (!$this->discount->confirmValidity(
                (new DiscountController)->fullPrice($bags),
                $address
            ))) {
            $this->discount = null;
        }

        if ($this->discount && $this->discount->type == 'freeShipping') {
            $freeShipping = $this->discount->shipping_options->pluck('id')->intersect($shippingId)->count();
            $this->discount = null;
        }

        $personalTotals = $this->getPersonalTotals($bags);

        $personalTotal = data_get($personalTotals, 'total');
        $personalDuration = data_get($personalTotals, 'duration');

        if ($address) {
            $address = is_array($address) ? (object)$address : $address;
        }

        $client = Client::withApiKey('77c00bc6efef4dc5ed0d6934012f65f6');
        $client->setApiConfig('headers', [
            'x-api-version' => '2022-01-24'
        ]);

        $tax_order = [
            'from_country' => settings()->getValue('store_country'),
            'from_zip' => settings()->getValue('store_postal_code'),
            'from_state' => settings()->getValue('store_state'),
            'from_city' => settings()->getValue('store_city'),

            'to_country' => $address->country ?? '',
            'to_zip' => $address->postal_code ?? '',
            'to_state' => $address->state ?? '',
            'to_city' => $address->city ?? '',
            'to_street' => $address->address_line_1 ?? '',

            'customer_id' => Auth()->User() ? Auth()->User()->id : '',
        ];

        $giftOptionsTotal = $bags->pluck('meta.gift_options.price')->sum();

        $collection = $this->GetTaxCollection($bags, $discountId, $address);

        $tax_collection = $collection['tax_collection'];

        $bags = $collection['bags'];

        $sub_total = number_format(collect($bags)->pluck('meta.total_amount')->sum(), 2, '.', '');

        // $discountAmount = collect($bags)->pluck('meta.discount_amount')->sum();
        // if the discount is more th $1,000 we have to remove the commma
        $discountAmount = collect($bags)->pluck('meta.discount_amount')
            ->map(function ($amount) {
                $switched = str_replace(',', '', $amount);
                return floatval($switched);
            })
            ->sum();

        $savings = $tax_collection->pluck('discount')->sum();

        $shippingAmounts = [];

        if (optional($address)->country && optional($address)->country != 'US') {
            $shippingAmounts = InternationalRatesController::price(
                $shippingId,
                $bags,
                $address->country,
                $address->postal_code,
                $freeShipping,
                $discountAmount
            );
        } else {
            $shippingAmounts = (new ShippingController)->price(
                $shippingId,
                $bags,
                optional($address)->postal_code,
                $freeShipping,
                $discountAmount
            );
        }

        $shippingAmount = data_get($shippingAmounts, 'shippingAmount');
        $original_shipping = data_get($shippingAmounts, 'original_shipping');

        if ($shipping_amount) {
            $shippingAmount = $shipping_amount;
        }

        if ($freeShipping) {
            $tax_order['shipping'] = 0;
        } else {
            $tax_order['shipping'] = $shippingAmount;
        }

        // $bags = $this->AddDiscountToBag($bags, $address);


        $tax_order['amount'] = collect($bags)->pluck('tax_total')->sum(); //$sub_total - $savings + $giftOptionsTotal;


        $tax_order['line_items'] = $tax_collection;

        $taxes = $this->GetTaxAmount($client, $tax_order, $address, $final);

        $bags = $this->AddTaxForBag($bags, $taxes, $address);

        $amount_to_collect = is_array($taxes) ? $taxes['amount_to_collect'] : $taxes->amount_to_collect;

        $total = ($sub_total + $giftOptionsTotal + $personalTotal + $amount_to_collect + $tax_order['shipping']) - $discountAmount;

        return [
            'sub_total' => $sub_total,
            'tax' => +number_format($amount_to_collect, 2, '.', ''),
            'shipping' => $tax_order['shipping'],
            'original_shipping' => $original_shipping,
            'giftOptions' => $giftOptionsTotal,
            'personalTotal' => $personalTotal,
            'discount' => $discountAmount,
            'total' => $total,
            'bag' => makeForFronEnd($bags)->map(function ($product) {
                if (data_get($product, 'item_type') == 'digital' || data_get($product, 'item_type') == 'both') {
                    $product['times_left'] = 3;
                }
                return $product;
            }),
        ];
    }

    public function getPersonalTotals($bags)
    {
        $extendedDuration = $bags->map(function ($bag) {
            return $bag->getExtendedDuration();
        })->max();

        $result = $bags->map(function ($bag) {
            if ($bag->model && $bag->model->personalizes && $bag->meta['personalization']) {
                return $bag->model->personalizes->getInfo($bag->meta['personalization'], $bag->quantity);
            }
        })->filter();

        return [
            'total' => collect($result)->pluck('total')->sum(),
            'duration' => collect($result)->pluck('duration')->max() + $extendedDuration
        ];
    }

    private function GetTaxCollection($bags, $discountId, $location)
    {
        $discount = $this->discount;

        $tax_collection = collect([]);

        $bags->map(function ($item) use ($discount, &$tax_collection, $location, $bags) {
            $savings_per = 0;

            $savings = null;
            if ($discount) {
                $result = $this->discount->getSavings($bags, $location);
                $savings = data_get($result, 'savings');

                $hasSavings = $this->GetDiscount($discount, $item, $location);

                if ($hasSavings) {
                    // $products_price_count = collect($bags)->map(function ($bag) use ($hasSavings) {
                    //     return $bag->quantity * (data_get($bag->model, 'price') ?? data_get($bag->meta, 'price'));
                    // })->sum();

                    // $savings_per = $savings / $products_price_count;


                    $products_price_count = collect($bags)->map(function ($bag) use ($discount, $location) {
                        if ($this->GetDiscount($discount, $bag, $location)) {
                            return $bag->quantity * (data_get($bag->model, 'price') ?? data_get($bag->meta, 'price'));
                        }
                        return 0;
                    })->sum();

                    $savings_per = $savings / $products_price_count;
                }
            }

            $item['meta->total_amount'] = $item->model_type == 'App\GiftCard' ? $item->meta['price'] * $item->quantity : $item->model->price * $item->quantity;
            $item['tax_total'] = $item->model_type == 'App\GiftCard' || $item->model->tax_code == '22' ? 0 : $item->model->price * $item->quantity;

            if ($item->model_type == 'App\Bundle') {
                $bundle = $item->model;
                $percent = ($bundle->fake_price - $bundle->price) / ($bundle->fake_price * 100);
                $item['meta->discount_amount'] = number_format(
                    ($percent / 100) * ($bundle->price * $bundle->quantity),
                    2
                );

                $tax_collection = $tax_collection->merge(
                    $bundle->bundleItems->map(
                        function ($bundle_item) use ($percent, $savings, $item, $bundle, $savings_per) {
                            return [
                                'id' => "{$bundle->id}_{$bundle_item->model->id}",
                                'quantity' => $bundle_item->quantity,
                                'product_tax_code' => $bundle_item->model->tax_code,
                                'unit_price' => $bundle_item->model->price,
                                'discount' => number_format(
                                    ($percent / 100) * ($bundle_item->model->price * $bundle_item->quantity),
                                    2
                                ),
                                // ($savings_per / $bundle->bundleItems->count()) + (($percent / 100) * $item->model->price),
                            ];
                        }
                    )
                );
            } elseif ($item->model_type != 'App\GiftCard') {
                $item['meta->discount_amount'] = number_format(
                    $savings_per * ($item->model->price * $item->quantity),
                    2
                );

                if ($item->model && $item->model->personalizes && $item->meta['personalization']) {
                    $item['meta->personal'] = $item->model->personalizes->getInfo(
                        $item->meta['personalization'],
                        $item->quantity
                    );
                }

                if ($item->model->tax_code != '22') {
                    $tax_collection = $tax_collection->merge([
                        [
                            'id' => "{$item->model_type}_{$item->model->id}",
                            'quantity' => $item->quantity,
                            'product_tax_code' => $item->model->tax_code,
                            'unit_price' => $item->model->price,
                            'discount' => number_format($savings_per * ($item->model->price * $item->quantity), 2),
                        ]
                    ]);
                }
            } elseif ($item->model_type == 'App\GiftCard') {
                $item['meta->discount_amount'] = number_format(
                    $savings_per * (data_get($item->meta, 'price') * $item->quantity),
                    2
                );
            }
        });
        return ['tax_collection' => $tax_collection, 'bags' => $bags];
    }

    private function GetDiscount($discount, $item, $location)
    {
        if ($discount == null || $item->model_type == 'App\Bundle') { //} || $item->model_type == 'App\GiftCard') {
            $item['discount'] = false;
            return false;
        } else {
            $item['discount'] = $discount->getSavings([$item], $location) != null && $discount->getSavings([$item],
                    $location) != 'wrong_location';
            return $item['discount'];
        }
    }

    private function GetTaxAmount($client, $tax_order, $address, $final)
    {
        if ($final) {
            try {
                $taxes = $client->taxForOrder($tax_order);

                $this->storeRate(
                    data_get($taxes, 'rate'),
                    data_get($tax_order, 'to_country'),
                    data_get($tax_order, 'to_zip')
                );

                return $taxes;
            } catch (Exception $e) {
                try {
                    $tax_rate = session()->get('zip_code')['tax_rate']
                        ??
                        (new LocationController)->getTaxRate($address->country, $address->postal_code)
                        ??
                        (new LocationController)->getTaxRate(
                            settings()->getValue('store_country'),
                            settings()->getValue('store_postal_code')
                        )
                        ??
                        settings()->getValue('default_tax_rate');

                    $total = $tax_order['amount'] + $tax_order['shipping'];

                    return ['amount_to_collect' => ($total * (($tax_rate / 100) + 1)) - $total];
                } catch (Exception $e) {
                    return ['amount_to_collect' => 0];
                }
            }
        } else {
            try {
                $tax_rate = session()->get('zip_code')['tax_rate']
                    ??
                    (new LocationController)->getTaxRate($address->country, $address->postal_code)
                    ??
                    (new LocationController)->getTaxRate(
                        settings()->getValue('store_country'),
                        settings()->getValue('store_postal_code')
                    )
                    ??
                    settings()->getValue('default_tax_rate');

                $total = $tax_order['amount'] + $tax_order['shipping'];

                return ['amount_to_collect' => ($total * (($tax_rate / 100) + 1)) - $total];
            } catch (Exception $e) {
                return ['amount_to_collect' => 0];
            }
        }
    }

    private function storeRate($rate, $country, $zip)
    {
        $zip = ZipCode::getCode($zip);

        if ($country == 'US' && $zip) {
            $zip->update([
                'tax_rate' => $rate
            ]);
        }
    }

    private function AddTaxForBag($bags, $taxes, $address)
    {
        if (property_exists(forceObject($taxes), 'breakdown')) {
            $bags->each(function ($bag) use ($taxes) {
                if ($bag->model_type == 'App\Bundle') {
                    $bag['meta->tax_amount'] =

                        collect($taxes->breakdown->line_items)
                            ->filter(function ($item) use ($bag) {
                                return Str::startsWith($item->id, "{$bag->model_id}_");
                            })
                            ->pluck('tax_collectable')->sum();

                    $bag['meta->tax_amount'] = number_format($bag['meta']['tax_amount'], 2);

                    $bag['meta->grand_total'] = data_get($bag, 'meta.tax_amount') + data_get(
                            $bag,
                            'meta.total_amount'
                        ) - data_get($bag, 'meta.discount_amount');
                } elseif ($bag->model_type != 'App\GiftCard' && $bag->model->tax_code != '22') {
                    $bag['meta->tax_amount'] =

                        collect($taxes->breakdown->line_items)
                            ->first(function ($item) use ($bag) {
                                return $item->id == "{$bag->model_type}_{$bag->model->id}";
                            })
                            ->tax_collectable;

                    $bag['meta->tax_amount'] = number_format($bag['meta']['tax_amount'], 2);

                    $bag['meta->grand_total'] = data_get($bag, 'meta.tax_amount') + data_get(
                            $bag,
                            'meta.total_amount'
                        ) - data_get($bag, 'meta.discount_amount');
                }
            });
            return $bags;
        } else {
            if (is_object($taxes) && data_get($taxes, 'amount_to_collect') == 0) {
                $bags->each(function ($bag) {
                    $bag['meta->tax_amount'] = 0;
                });
                return $bags;
            } else {
                try {
                    $tax_rate = session()->get('zip_code')['tax_rate']
                        ??
                        (new LocationController)->getTaxRate($address->country, $address->postal_code)
                        ??
                        (new LocationController)->getTaxRate(
                            settings()->getValue('store_country'),
                            settings()->getValue('store_postal_code')
                        )
                        ??
                        settings()->getValue('default_tax_rate');

                    $total = collect($bags)->pluck('tax_total')->sum();

                    $bags->each(function ($item) use ($total, $tax_rate) {
                        $item['meta->tax_amount'] = ($total * (($tax_rate / 100) + 1)) - $total;

                        $item['meta->tax_amount'] = number_format($item['meta']['tax_amount'], 2);

                        $item['meta->grand_total'] = data_get($item, 'meta.tax_amount') + data_get(
                                $item,
                                'meta.total_amount'
                            ) - data_get($item, 'meta.discount_amount');
                    });

                    return $bags;
                } catch (Exception $e) {
                    return $bags;
                }
            }
        }
    }

    // private function AddDiscountToBag($bags, $location)
    // {
    //     if (!$this->discount) {
    //         return $bags;
    //     }
    //     $savings = $this->discount->getSavings($bags, $location)['savings'];

    //     $products_price_count = collect($bags)->map(function ($bag) {
    //         if ($bag['discount']) {
    //             return $bag['quantity'] * $bag['price'];
    //         }
    //     })->sum();

    //     $savings_per = $savings / $products_price_count;

    //     $bags = collect($bags)->map(function ($bag) use ($savings_per) {
    //         if ($bag['discount']) {
    //             $bag['meta->discount_amount'] = $savings_per * ($bag['quantity'] * $bag['price']);
    //         }
    //         return $bag;
    //     });
    //     return $bags;
    // }
}
