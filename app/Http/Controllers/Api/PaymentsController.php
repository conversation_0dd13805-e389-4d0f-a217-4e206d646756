<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class PaymentsController extends Controller
{
    public function index()
    {
        return auth()->user()->payments()->get();
    }

    public function createPayment(Request $request)
    {
        $data = $request->only(DB::getSchemaBuilder()->getColumnListing('payments'));
        $expDate = implode(
            '/',
            collect(explode('/', $request->expDate))->map(function ($s) {
                return strlen($s) > 2 ? substr($s, -2) : $s;
            })->toArray()
        );
        $data['expDate'] = now()->createFromFormat('m / y', trim($expDate))->endOfMonth();

        $payment = auth()
            ->user()
            ->payments()
            ->create($data);

        return [
            'status' => 200,
            'message' => 'Payment was created.',
            'payments' => auth()->user()->fresh()->payments,
            'payment' => $payment
        ];
    }

    public function updatePayment($id)
    {
        $payment = auth()->user()->payments()->findOrFail($id);

        $payment->update(request()->only([
            'name',
            'default',
            'address_line_1',
            'address_line_2',
            'country',
            'state',
            'city',
            'postal_code',
        ]));

        return [
            'status' => 200,
            'message' => 'Payment was updated.',
            'payments' => auth()->user()->fresh()->payments,
            'payment' => $payment,
        ];
    }

    public function deletePayment($id)
    {
        $payment = auth()->user()->payments()->findOrFail($id);
        $payment->delete();

        return [
            'status' => 200,
            'message' => 'Payment was deleted.',
            // 'payment' => $payment
        ];
    }
}
