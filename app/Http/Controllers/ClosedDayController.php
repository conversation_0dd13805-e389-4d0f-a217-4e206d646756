<?php

namespace App\Http\Controllers;

use App\ClosedDay;
use App\ClosedDayShippingOption;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ClosedDayController extends Controller
{
    /**
     * GET /closedday
     * Fetches all current closed days
     * @return ClosedDay[]|Collection
     */
    public function browseClosedDays()
    {
        return ClosedDay::all();
    }

    /**
     * GET /closedday/{id}
     * Fetches specified closed day
     * @param int id - The DB ID of the requested closed day
     * @return JsonResponse
     */
    public function readClosedDay($id)
    {
        try {
            return ClosedDay::findOrFail($id);
        } catch (ModelNotFoundException $e) {
            return response()->json(false);
        }
    }

    /**
     * POST /closedday
     * Adds a new closed day
     * @param string name - Name of the closed day
     * @param string date - The date of the closed day, formatted YYYY-MM-DD
     * @param array shipping_options - Array of JSON objects specifying shipping_option details (see below)
     * @param int closed_day_id - The DB ID of the closed day
     * @param int shipping_option_id - The DB ID of the shipping option to be associated with the closed day
     * @return JsonResponse
     */
    public function addClosedDay(Request $request)
    {
        try {
            $closedDay = ClosedDay::create([
                'name' => $request->input('name'),
                'date' => $request->input('date'),
                'source' => 'Admin Section'
            ]);

            foreach ($request->input('shipping_options') as $shippingOption) {
                ClosedDayShippingOption::create([
                    'closed_day_id' => $closedDay->id,
                    'pickup' => $shippingOption
                ]);
            }

            return response()->json(['success' => true]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Create failed']);
        }
    }

    /**
     * GET /closedday/import
     * Imports dates from the HebCal API for a specified year and/or month
     * @param string year - Query param specifying the year for which closed days should be fetched (formatted YYYY or 'now' for the current year)
     * @param string month - Query param specifying the month for which closed days should be fetched (formatted M or 'x' for the entire year)
     */
    public function importClosedDays(Request $request)
    {
        $c = curl_init();

        curl_setopt(
            $c,
            CURLOPT_URL,
            'https://www.hebcal.com/hebcal/?v=1&cfg=json&maj=on&min=off&mod=off&nx=off&year=' . $request->query(
                'year'
            ) . '&month=' . $request->query('month') .
            '&ss=off&mf=off&c=off&geo=zip&zip=11226&s=off'
        );
        curl_setopt($c, CURLOPT_POST, 0);
        curl_setopt($c, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($c, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($c, CURLOPT_SSL_VERIFYPEER, false);

        $result = curl_exec($c);
        $jsonResult = json_decode($result);

        foreach ($jsonResult->items as $item) {
            if ($item->category === "holiday" && !count(ClosedDay::where(['date' => $item->date])->get())) {
                ClosedDay::create([
                    'name' => $item->title,
                    'date' => explode('T', $item->date)[0],
                    'source' => 'HebCal'
                ]);
            }
        }

        curl_close($c);

        return response()->json($result);
    }

    /**
     * PATCH /closedday/{id}
     * Edits a closed day
     * @param int id - The DB ID of the closed day to be edited
     * @param string name - Name of the closed day
     * @param string date - The date of the closed day, formatted YYYY-MM-DD
     * @param array shipping_options - Array of JSON objects specifying shipping_option details (see below)
     * @param int closed_day_id - The DB ID of the closed day
     * @param int shipping_option_id - The DB ID of the shipping option to be associated with the closed day
     * @return JsonResponse
     */
    public function editClosedDay(Request $request, $id)
    {
        try {
            ClosedDay::findOrFail($id)
                ->update([
                    'name' => $request->input('name'),
                    'date' => $request->input('date')
                ]);

            $submittedShippingOptions = $request->input('shipping_options');

            if (count($submittedShippingOptions)) {
                foreach ($submittedShippingOptions as $shippingOption) {
                    ClosedDayShippingOption::updateOrCreate(
                        ['closed_day_id' => $id, 'shipping_option_id' => $shippingOption],
                        ['closed_day_id' => $id, 'shipping_option_id' => $shippingOption]
                    );
                }
            }

            $existingShippingOptions = ClosedDayShippingOption::where(['closed_day_id' => $id])->get();

            if (count($existingShippingOptions)) {
                foreach ($existingShippingOptions as $existingShippingOption) {
                    if (!in_array($existingShippingOption->shipping_option_id, $submittedShippingOptions)) {
                        $existingShippingOption->destroy();
                    }
                }
            }

            return response()->json(['success' => true]);
        } catch (ModelNotFoundException $e) {
            return response()->json(['success' => false, 'message' => 'Submitted ID does not exist']);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Update failed']);
        }
    }

    /**
     * DELETE /closedday/{id}
     * Deletes a closed day
     * @param int id - The DB ID of the closed day to be deleted
     * @return JsonResponse
     */
    public function deleteClosedDay($id)
    {
        try {
            ClosedDay::destroy($id);

            return response()->json(['success' => true]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Delete failed']);
        }
    }
}
