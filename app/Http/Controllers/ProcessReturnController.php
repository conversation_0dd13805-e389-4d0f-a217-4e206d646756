<?php

namespace App\Http\Controllers;

use App\Discount;
use App\GiftCard;
use App\GiftCardSettings;
use App\Product;
use App\Returns;
use Illuminate\Http\Request;
use App\Jobs\ReturnCancelCreate;
use App\Http\Controllers\Api\GiftCardController;
use App\Http\Controllers\Api\EmailsController;

class ProcessReturnController extends Controller
{
    public static function process($request)
    {
        $products = $request->products;
        $returnId = $request->returnId;
        $final = $request->final ?? false;

        $return = Returns::find($returnId);
        if (!$return) {
            return [];
        }
        $order = $return->order;

        $products = collect($products)->map(function ($product) {
            if ($product['refund'] == 'Full Refund') {
                $product['tax_amount'] = $product['tax_amount'] / $product['quantity'];

                $product['discount_amount'] = $product['discount_amount'] / $product['quantity'];
            } else {
                $product['tax_amount'] = 0;

                $product['discount_amount'] = 0;
            }

            $product['quantity'] = 1;
            $product['refund_total'] = data_get($product, 'refund_total') ?? data_get($product, 'price');
            $product['total_amount'] = $product['refund_total'];
            $product['grand_total'] = $product['refund_total'] + $product['tax_amount'] + $product['discount_amount'];
            $product['total'] = $product['total_amount'];

            $product['status'] = 'processed';
            return $product;
        });


        $sub_total = collect($products)->pluck('refund_total')->sum();
        $tax_amount = collect($products)->pluck('tax_amount')->sum();
        $discount_amount = collect($products)->pluck('discount_amount')->sum();
        $shipping_refund = 0.00;

        $message = '';

        $discount = Discount::find($order->discount_id);

        if (!$discount || data_get($order, 'discount.lost') == true) {
            $discount_amount = 0.00;
        } else {
            if ($discount->min > ($order->sub_total_left - $sub_total)) {
                $message = "You are losing your $" . number_format($order->discount_amount, 2) . " discount";
                $discount_amount = -$order->discount_amount;
            } elseif ($discount->type == 'percentage') {
                $discount_amount = collect($products)->pluck('discount_amount')->sum();
            } else {
                $discount_amount = 0.00;
            }
        }


        if (collect($products)->where('refund', 'Full Refund')->map(function ($product) {
            return $product['our_responsibility'];
        })->contains(true)) {
            $shipping_refund = $order->shipping_amount + data_get($order,'meta.shipping_tax');
        }

        if (collect($products)->map(function ($product) {
            return optional(Product::find($product['product_id']))->exclude_free_shipping;
        })->contains(true)) {
            $shipping_refund = -($order->shipping_amount + data_get($order,'meta.shipping_tax'));
        }

        $grand_total = $sub_total + $tax_amount + $shipping_refund + $discount_amount;

        $creditInfo = [];

        if ($order->chargeLeft) {
            $creditInfo = [
                'type' => data_get($order, 'payments.creditInfo.type'),
                'amount' => min($order->chargeLeft, $grand_total),
                'last_four' => data_get($order, 'payments.creditInfo.last_four'),
                'payment_type' => data_get($order, 'payments.creditInfo.payment_type'),
                'payment_id' => $order->payment_id
            ];
        }

        $giftCards = [];

        if ($grand_total > $order->chargeLeft) {
            $total_left = $grand_total - $order->chargeLeft;

            $giftCards = collect(data_get($order, 'payments.giftCard'))->map(
                function ($card) use (&$total_left, $order) {
                    // $amount = $card['amount'] - min($total_left, $order->TotalRefundedForGiftCard($card['id']));
                    $amount = min($card['amount'] - $order->TotalRefundedForGiftCard($card['id']), $total_left);

                    if ($total_left > 0 && $amount > 0) {
                        $total_left -= $card['amount'];
                        return [
                            'id' => $card['id'],
                            'code' => $card['code'],
                            'amount' => $amount
                        ];
                    }
                }
            )->filter(function ($g) {
                return $g != null;
            })->values();
        }

        $payments = [
            'creditInfo' => $creditInfo,
            'giftCard' => $giftCards
        ];

        if ($eGiftRefund = data_get($return, 'payments.giftCard.eGift')) {
            $payments = $return->payments;
            $payments['giftCard']['code'] = '';
        }

        if ($final) {
            if ($eGiftRefund = data_get($return, 'payments.giftCard.eGift')) {
                $giftCard = GiftCard::create([

                    'media' => GiftCardSettings::first()->media_urls[0],
                    'amount' => $grand_total,
                    'balance' => $grand_total,
                    'to_name' => data_get($eGiftRefund, 'name'),
                    'from_name' => 'Eichlers Team',
                    'message' => 'Here is your return refund eGift Card',
                    'to_email' => data_get($eGiftRefund, 'email'),
                    'code' => GiftCard::createUniqueUuid(),
                ]);

                $transaction = $giftCard->transactions()->create([
                    'order_id' => $order->id,
                    'type' => 'Return Create'
                ]);

                GiftCardController::createVoucher($transaction);
                $payments = [
                    'giftCard' => [
                        [
                            'id' => $giftCard->id,
                            'code' => 'eGift' . substr($giftCard->code, -4),
                            'amount' => 0,
                        ]
                    ]
                ];

                EmailsController::SendGiftCard($giftCard);
            }

            $reversion = [
                'payments' => $return->payments,
                'products' => $return->products,
                'sub_total' => +$return->sub_total,
                'tax_amount' => +$return->tax_amount,
                'shipping_amount' => +$return->shipping_amount,
                'discount_amount' => +$return->discount_amount,
                'grand_total' => +max($return->grand_total, 0),
                'status' => $return->status,
            ];
            $date = now()->ToDateTimeString();

            $return->update([
                'payments' => $payments,
                'products' => $products,
                'sub_total' => +$sub_total,
                'tax_amount' => +$tax_amount,
                'shipping_amount' => +$shipping_refund,
                'discount_amount' => +$discount_amount,
                'grand_total' => +max($grand_total, 0),
                "reversions->{$date}" => $reversion,
                'status' => 'processed'
            ]);

            if ($message) {
                $order->update([
                    'discount->lost' => true
                ]);
            }

            ReturnCancelCreate::dispatch(
                ChargeController::class,
                'RefundReturnPayments',
                $return
            );
            ReturnCancelCreate::dispatch(
                EmailsController::class,
                'SendReturnConfirmation',
                $return
            );
            ReturnCancelCreate::dispatch(
                TransactionController::class,
                'DeleteTransaction',
                $order
            )->onQueue('low');

            ReturnCancelCreate::dispatch(PosController::class, 'CreateReturn', $return)->onQueue(
                'low'
            );
            ReturnCancelCreate::dispatch(
                PosOnlineController::class,
                'CreateReturn',
                $return
            )->onQueue('low');

            if ($order->hasNonCancellableItems()) {
                ReturnCancelCreate::dispatch(
                    TransactionController::class,
                    'AddTransaction',
                    $order
                )->onQueue('low');
            }
        }

        return [
            'sub_total' => +$sub_total,
            'tax_amount' => +$tax_amount,
            'shipping_amount' => +$shipping_refund,
            'discount_amount' => +$discount_amount,
            'grand_total' => +max($grand_total, 0),
            'message' => $message,
            'payments' => $payments,
            'products' => $products
        ];
    }
}
