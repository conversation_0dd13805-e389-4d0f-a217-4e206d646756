<?php

namespace App\Http\Controllers;

use Google_Service_ShoppingContent_Price;
use Google_Service_ShoppingContent_Product;
use Illuminate\Http\Request;
use App\GoogleShopping\GoogleProducts;
use App\VariationInfo;
use Illuminate\Support\Facades\App;

class GoogleShoppingController extends Controller
{
    // https://developers.google.com/resources/api-libraries/documentation/content/v2.1/php/latest/class-Google_Service_ShoppingContent_ProductShipping.html
    public static function addProduct($item)
    {
        if (!App::environment('production')) {
            return;
        }
        if ($item->exclude_free_shipping) {
            return;
        }
        if (get_class($item) == VariationInfo::class) {
            return json_encode((new GoogleProducts)->insertProduct(self::createVariation($item)));
        } else {
            return json_encode((new GoogleProducts)->insertProduct(self::createProduct($item)));
        }
    }

    private static function createProduct($item)
    {
        $product = new Google_Service_ShoppingContent_Product();

        $product->setOfferId($item->id);
        $product->setTitle($item->title);
        $product->setDescription(strip_tags($item->description));
        $product->setLink(env('APP_URL') . data_get($item, 'search.path'));
        $product->setImageLink(data_get($item, 'search.image'));

        $product->setContentLanguage('en');
        $product->setTargetCountry('US');
        $product->setChannel('online');

        $availability = $item->searchable ? 'in stock' : 'out of stock';
        $product->setAvailability($availability);

        $product->setBrand(data_get($item, 'vendor.name'));

        $product->setCondition('new');
        // $product->setGoogleProductCategory(data_get($item, 'search.categories.0'));

        //gtin before barcode
        $gtin = data_get($item, 'gtni') ?? data_get($item, 'meta.isbn 13') ?? data_get($item, 'meta.isbn 10');
        $product->setGtin($gtin);


        $price = new Google_Service_ShoppingContent_Price();

        $item_price = $item->price ?? 0;
        $price->setValue($item_price);
        $price->setCurrency('USD');

        $product->setPrice($price);

        return $product;
    }

    private static function createVariation($item)
    {
        $product_json = data_get($item, 'product_json');
        $search = json_decode(data_get($product_json, 'search'));

        $product = new Google_Service_ShoppingContent_Product();

        $product->setOfferId('222' . $item->id);
        $product->setTitle(data_get($product_json, 'title'));
        $product->setDescription(strip_tags(data_get($product_json, 'description')));
        $product->setLink(env('APP_URL') . data_get($product_json, 'path'));
        $product->setImageLink(data_get($search, 'image'));

        $product->setContentLanguage('en');
        $product->setTargetCountry('US');
        $product->setChannel('online');

        $availability = $item->searchable ? 'in stock' : 'out of stock';
        $product->setAvailability($availability);

        $product->setBrand(data_get($item->product_json, 'vendor_name'));

        $product->setCondition('new');
        // $product->setGoogleProductCategory(data_get($search, 'categories.0'));

        $gtin = data_get($item, 'gtni') ?? data_get($item, 'meta.isbn 13') ?? data_get($item, 'meta.isbn 10');
        $product->setGtin($gtin);

        $price = new Google_Service_ShoppingContent_Price();

        $item_price = $item->price ?? 0;
        $price->setValue($item_price);
        $price->setCurrency('USD');

        $product->setPrice($price);

        return $product;
    }
}
