<?php

namespace App\Http\Controllers;

use App\Category;
use App\Page;
use App\Product;
use Carbon\Carbon;
use File;
use Spatie\ArrayToXml\ArrayToXml;

class SiteMapController extends Controller
{
    public static function GenerateSiteMap()
    {
        $start = now();

        $productsPerPage = 1000;

        $productCount = Product::active()->count();

        $pageCount = ceil($productCount / $productsPerPage);

        $home_page = ['sitemap' => []];

        for ($i = 0; $i < $pageCount; $i++) {
            $products = Product::active()->skip($i * $productsPerPage)->limit($productsPerPage)->get();

            $home_page['sitemap'] = array_merge($home_page['sitemap'], [
                [
                    'loc' => env('APP_URL') . "/sitemap_products_530413_" . $i . ".xml",
                    'lastmod' => now()->format('Y-m-d\TH:i:sP')
                ]
            ]);

            $array = ['url' => []];

            $products->each(function ($product) use ($i, $products, &$array) {
                $new_array = [];
                $new_array['loc'] = env('APP_URL') . $product->path;
                $new_array['lastmod'] = now()->format('Y-m-d\TH:i:sP');
                $new_array['changefreq'] = 'weekly';
                $new_array['priority'] = 0.5;
                if (data_get($product, 'mediaUrls.0.large')) {
                    $new_array['image:image'] = [
                        'image:loc' => data_get($product, 'mediaUrls.0.large'),
                        'image:title' => $product->title
                    ];
                }
                $array['url'] = array_merge($array['url'], [$new_array]);
            });

            $p_result = ArrayToXml::convert($array, [
                'rootElementName' => 'urlset',
                '_attributes' => [
                    'xmlns' => 'http://www.sitemaps.org/schemas/sitemap/0.9',
                    'xmlns:image' => 'http://www.google.com/schemas/sitemap-image/1.1',
                    'xmlns:xhtml' => 'http://www.w3.org/1999/xhtml'
                ],
            ], true, 'UTF-8');

            $location = "sitemap_products_530413_" . $i . ".xml";

            File::put(public_path($location), $p_result);
        }

        //ADD_PAGES
        $home_page['sitemap'] = array_merge($home_page['sitemap'], [
            [
                'loc' => env('APP_URL') . "/sitemap_pages_530413.xml",
                'lastmod' => now()->format('Y-m-d\TH:i:sP')
            ]
        ]);

        $pages = Page::get();
        $array = ['url' => []];

        $pages->each(function ($page) use (&$array) {
            $new_array = [];
            $new_array['loc'] = env('APP_URL') . $page->path;
            $new_array['lastmod'] = now()->format('Y-m-d\TH:i:sP');
            $new_array['changefreq'] = 'weekly';
            $new_array['priority'] = 1.0;
            if (data_get($page, 'mediaUrls.0')) {
                $new_array['image:image'] = [
                    'image:loc' => data_get($page, 'mediaUrls.0'),
                    'image:title' => $page->name
                ];
            }
            $array['url'] = array_merge($array['url'], [$new_array]);
        });

        $p_result = ArrayToXml::convert($array, [
            'rootElementName' => 'urlset',
            '_attributes' => [
                'xmlns' => 'http://www.sitemaps.org/schemas/sitemap/0.9',
                'xmlns:image' => 'http://www.google.com/schemas/sitemap-image/1.1',
                'xmlns:xhtml' => 'http://www.w3.org/1999/xhtml'
            ],
        ], true, 'UTF-8');

        $location = "sitemap_pages_530413.xml";

        File::put(public_path($location), $p_result);


        //ADD_CATEGORIES
        $home_page['sitemap'] = array_merge($home_page['sitemap'], [
            [
                'loc' => env('APP_URL') . "/sitemap_categories_530413.xml",
                'lastmod' => now()->format('Y-m-d\TH:i:sP')
            ]
        ]);

        $categories = Category::get();

        $array = ['url' => []];

        $categories->each(function ($category) use (&$array) {
            $new_array = [];
            $new_array['loc'] = env('APP_URL') . $category->path;
            $new_array['lastmod'] = now()->format('Y-m-d\TH:i:sP');
            $new_array['changefreq'] = 'weekly';
            $new_array['priority'] = 1.0;
            if (data_get($category, 'mediaUrls.0')) {
                $new_array['image:image'] = [
                    'image:loc' => data_get($category, 'mediaUrls.0'),
                    'image:title' => $category->name
                ];
            }
            $array['url'] = array_merge($array['url'], [$new_array]);
        });

        $p_result = ArrayToXml::convert($array, [
            'rootElementName' => 'urlset',
            '_attributes' => [
                'xmlns' => 'http://www.sitemaps.org/schemas/sitemap/0.9',
                'xmlns:image' => 'http://www.google.com/schemas/sitemap-image/1.1',
                'xmlns:xhtml' => 'http://www.w3.org/1999/xhtml'
            ],
        ], true, 'UTF-8');

        $location = "sitemap_categories_530413.xml";

        File::put(public_path($location), $p_result);


        $result = ArrayToXml::convert($home_page, [
            'rootElementName' => 'sitemapindex',
            '_attributes' => [
                'xmlns' => 'http://www.sitemaps.org/schemas/sitemap/0.9'
            ],
        ], true, 'UTF-8');

        File::put(public_path('sitemap_530413.xml'), $result);

        $end = now();
        return json_encode((new Carbon($start))->diff(new Carbon($end))->format('%I:%S'));
    }
}
