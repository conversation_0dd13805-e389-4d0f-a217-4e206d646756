<?php

namespace App\Http\Controllers;

use App\ShippingOption;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ShippingOptionController extends Controller
{
    /**
     * GET /shippingoption
     * Fetches all current shipping zones
     * @return ShippingOption[]|Collection
     */
    public function browseShippingOptions()
    {
        return ShippingOption::all();
    }

    /**
     * GET /shippingoption/{id}
     * Fetches specified shipping option
     * @param int id - The DB ID of the requested shipping option
     * @return JsonResponse
     */
    public function readShippingOption($id)
    {
        try {
            return ShippingOption::findOrFail($id);
        } catch (ModelNotFoundException $e) {
            return response()->json(false);
        }
    }

    /**
     * POST /shippingoption
     * Adds a new shipping option
     * @param string internal_name (nullable)
     * @param string visible_name
     * @param boolean delivery
     * @return JsonResponse
     */
    public function addShippingOption(Request $request)
    {
        try {
            ShippingOption::create($request->all());

            return response()->json(['success' => true]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Create failed']);
        }
    }

    /**
     * PATCH /shippingoption/{id}
     * Edits a shipping option
     * @param int id - The DB ID of the shipping option to be edited
     * @param string internal_name (nullable)
     * @param string visible_name
     * @param boolean delivery
     * @return JsonResponse
     */
    public function editShippingOption(Request $request, $id)
    {
        try {
            ShippingOption::findOrFail($id)
                ->update($request->all());

            return response()->json(['success' => true]);
        } catch (ModelNotFoundException $e) {
            return response()->json(['success' => false, 'message' => 'Submitted ID does not exist']);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Update failed']);
        }
    }

    /**
     * DELETE /shippingoption/{id}
     * Deletes a shipping option
     * @param int id - The DB ID of the shipping option to be deleted
     * @return JsonResponse
     */
    public function deleteShippingOption($id)
    {
        try {
            ShippingOption::destroy($id);

            return response()->json(['success' => true]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Delete failed']);
        }
    }

    /**
     * GET /shippingoption/schematic/{id}
     * Fetches the fulfillment schematic of the associated shipping option
     * @param int id - The DB ID of the shipping option for which the fulfillment schematic will be retrieved
     * @return JsonResponse
     */
    public function getFulfillmentSchematic($id)
    {
        try {
            $shippingOption = ShippingOption::findOrFail($id);

            return $shippingOption->fulfillmentSchematic()->get();
        } catch (ModelNotFoundException $e) {
            return response()->json(['success' => false, 'message' => 'Submitted ID does not exist']);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Retrieval failed']);
        }
    }
}
