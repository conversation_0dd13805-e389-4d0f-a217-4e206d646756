<?php

namespace App\Http\Controllers;

use App\Product;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    private $array;

    public function index(Request $request)
    {
        return view('vue.index');

        $options = [
            'page' => [
                'size' => 40,
                'current' => (int)($request->page ?? 1),
            ],
            'facets' => [
                'vendor' => ['type' => 'value'],
                'creators' => ['type' => 'value'],
                'categories' => ['type' => 'value'],
                'filters' => ['type' => 'value'],
                'variations' => ['type' => 'value'],
            ],
        ];

        foreach ($request->all() as $key => $value) {
            switch ($key) {
                case 'vendor':
                case 'creators':
                case 'categories':
                    $options['filters'][$key] = explode(',', $value);
                    break;
                case 'filters':
                case 'variations':
                    $options['filters']['all'] =
                        collect(explode(',', $value))->map(function ($item) use ($key) {
                            return [$key => $item];
                        })->all();
                    break;
            }
        }
        $data = Product::search($request->q);

        return [
            'products' => $this->getPagination($data),
            'filters' => $this->getFilters($data),
            'categories' => $this->getCategories($data),
            'vendors' => $this->getVendors($data),
            'variations' => $this->getVariations($data),
            'creators' => $this->getCreators($data),
        ];
    }

    private function getPagination($data)
    {
        return [
            'current_page' => $data['meta']['page']['current'],
            'data' => $data['results'],
            'per_page' => $data['meta']['page']['size'],
            'total' => $data['meta']['page']['total_results'],
            'last_page' => $data['meta']['page']['total_pages'],
            'next_page_url' => $this->getUrl($data, 'next'),
            'prev_page_url' => $this->getUrl($data, 'prev'),
        ];
    }

    private function getUrl($data, $dir)
    {
        if ($dir == 'next' && $data['meta']['page']['current'] < $data['meta']['page']['total_pages']) {
            return request()->url() . '?' . http_build_query(
                    ['page' => $data['meta']['page']['current'] + 1] + request()->all()
                );
        }
        if ($dir == 'prev' && $data['meta']['page']['current'] > 1) {
            return request()->url() . '?' . http_build_query(
                    ['page' => $data['meta']['page']['current'] - 1] + request()->all()
                );
        }
    }

    private function getFilters($data)
    {
        $this->array = [];
        collect($data['facets']['filters'][0]['data'])
            ->map(function ($item) {
                $split = explode('_', $item['value']);

                if (array_key_exists(1, $split)) {
                    $this->array[$split[0]][] = [
                        'name' => $split[1],
                        'count' => $item['count'],
                    ];
                }
            });
        return $this->array;
    }

    private function getCategories($data)
    {
        return collect($data['facets']['categories'][0]['data'])
            ->map(function ($item) {
                return [
                    'name' => $item['value'],
                    'count' => $item['count'],
                ];
            });
    }

    private function getVendors($data)
    {
        return collect($data['facets']['vendor'][0]['data'])
            ->map(function ($item) {
                return [
                    'name' => $item['value'],
                    'count' => $item['count'],
                ];
            });
    }

    private function getVariations($data)
    {
        $this->array = [];
        collect($data['facets']['variations'][0]['data'])
            ->map(function ($item) {
                $split = explode('_', $item['value']);

                if (array_key_exists(1, $split)) {
                    $this->array[$split[0]][] = [
                        'name' => $split[1],
                        'count' => $item['count'],
                    ];
                }
            });
        return $this->array;
    }

    private function getCreators($data)
    {
        return collect($data['facets']['creators'][0]['data'])
            ->map(function ($item) {
                return [
                    'name' => $item['value'],
                    'count' => $item['count'],
                ];
            });
    }
}
