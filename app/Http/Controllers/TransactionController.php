<?php

namespace App\Http\Controllers;

use Exception;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use TaxJar\Client;

class TransactionController extends Controller
{
    public static function AddTransaction($order)
    {
        $customer = $order->customer;

        $order->products = collect($order->products)
            ->filter(function ($product) {
                return
                    data_get($product, 'status') != 'returned'
                    &&
                    data_get($product, 'status') != 'cancelled'
                    &&
                    data_get($product, 'type') != 'giftCard'
                    &&
                    data_get($product, 'tax_code') != '22';
            })->values();

        $order->products = collect($order->products)->map(function ($product) {
            $product['quantity'] = data_get($product, 'returns_left') ?? data_get($product, 'quantity');
            return $product;
        });

        if (collect($order->products)->isEmpty()) {
            return;
        }

        if ($customer->tax_exempt && !self::GetCustomer($customer->id)) {
            self::AddCustomer($customer);
        }

        $client = Client::withApiKey(env('TAX_JAR'));
        $client->setApiConfig('headers', [
            'x-api-version' => '2022-01-24'
        ]);

        $tax_order = [
            'transaction_id' => $order->id,
            'transaction_date' => $order->created_at,
            'to_country' => $order->shipping['shippingInfo']['country'] ?? settings()->getValue('store_country'),
            'to_zip' => $order->shipping['shippingInfo']['postal_code'] ?? settings()->getValue('store_postal_code'),
            'to_state' => $order->shipping['shippingInfo']['state'] ?? settings()->getValue('store_state'),
            'to_city' => $order->shipping['shippingInfo']['city'] ?? settings()->getValue('store_city'),
            'to_street' => $order->shipping['shippingInfo']['address_line_1'] ?? settings()->getValue(
                    'store_address_line_1'
                ),
            'amount' => self::GetAmount($order),
            'shipping' => $order->shipping_amount,
            'sales_tax' => $customer->tax_exempt ? 0 : $order->tax_amount,
            'customer_id' => $customer->id,
            'exemption_type' => $customer->tax_exempt ? $customer->tax_exempt : 'non_exempt',
            'line_items' =>
                collect($order->products)
                    ->map(function ($o, $index) use ($customer) {
                        return
                            [
                                'quantity' => data_get($o, 'quantity'),
                                'product_identifier' => $index,
                                'description' => data_get($o, 'title'),
                                'unit_price' => data_get($o, 'price'),
                                'sales_tax' => $customer->tax_exempt ? 0 : data_get($o, 'tax_amount'),
                                'discount' => data_get($o, 'discount_amount')
                            ];
                    })
        ];
        return json_encode($client->createOrder($tax_order));
    }


    public static function EditTransaction($order)
    {
        $customer = $order->customer;

        if ($customer->tax_exempt && !self::GetCustomer($customer->id)) {
            self::AddCustomer($customer);
        }

        $client = Client::withApiKey(env('TAX_JAR'));
        $client->setApiConfig('headers', [
            'x-api-version' => '2022-01-24'
        ]);

        $tax_order = [
            'transaction_id' => $order->id,
            'transaction_date' => $order->created_at,
            'to_country' => $order->shipping['shippingInfo']['country'] ?? settings()->getValue('store_country'),
            'to_zip' => $order->shipping['shippingInfo']['postal_code'] ?? settings()->getValue('store_postal_code'),
            'to_state' => $order->shipping['shippingInfo']['state'] ?? settings()->getValue('store_state'),
            'to_city' => $order->shipping['shippingInfo']['city'] ?? settings()->getValue('store_city'),
            'to_street' => $order->shipping['shippingInfo']['address_line_1'] ?? settings()->getValue(
                    'store_address_line_1'
                ),
            'amount' => self::GetAmount($order),
            'shipping' => $order->shipping_amount,
            'sales_tax' => $customer->tax_exempt ? 0 : $order->tax_amount,
            'customer_id' => $customer->id,
            'exemption_type' => $customer->tax_exempt ? $customer->tax_exempt : 'non_exempt',
        ];
        return json_encode($client->updateOrder($tax_order));
    }


    public static function DeleteTransaction($order)
    {
        $client = Client::withApiKey(env('TAX_JAR'));
        $client->setApiConfig('headers', [
            'x-api-version' => '2022-01-24'
        ]);

        return json_encode($client->deleteOrder($order->id));
    }


    public static function AddCustomer($customer)
    {
        if (self::GetCustomer($customer->id)) {
            return;
        }

        $client = Client::withApiKey(env('TAX_JAR'));
        $client->setApiConfig('headers', [
            'x-api-version' => '2022-01-24'
        ]);

        $address = collect($customer->addresses)->where('default')->first();

        $customer = $client->createCustomer([
            'customer_id' => $customer->id,
            'exemption_type' => $customer->tax_exempt,
            'name' => $customer->name,
            'country' => data_get($address, 'country'),
            'state' => data_get($address, 'state'),
            'zip' => data_get($address, 'postal_code'),
            'city' => data_get($address, 'city'),
            'street' => data_get($address, 'address_line_1'),
        ]);

        return json_encode($customer);
    }

    public static function UpdateCustomer($customer)
    {
        if (!self::GetCustomer($customer->id)) {
            return;
        }

        $client = Client::withApiKey(env('TAX_JAR'));
        $client->setApiConfig('headers', [
            'x-api-version' => '2022-01-24'
        ]);

        $address = collect($customer->addresses)->where('default')->first();

        $customer = $client->updateCustomer([
            'customer_id' => $customer->id,
            'exemption_type' => $customer->tax_exempt,
            'name' => $customer->name,
            'country' => data_get($address, 'country'),
            'state' => data_get($address, 'state'),
            'zip' => data_get($address, 'postal_code'),
            'city' => data_get($address, 'city'),
            'street' => data_get($address, 'address_line_1'),
        ]);
        return json_encode($customer);
    }

    public static function DeleteCustomer($customer)
    {
        if (!self::GetCustomer($customer->id)) {
            return;
        }
        $client = Client::withApiKey(env('TAX_JAR'));

        $client->setApiConfig('headers', [
            'x-api-version' => '2022-01-24'
        ]);
        $customer = $client->deleteCustomer($customer->id);
        return json_encode($customer);
    }

    public static function GetCustomer($id)
    {
        $client = Client::withApiKey(env('TAX_JAR'));
        $client->setApiConfig('headers', [
            'x-api-version' => '2022-01-24'
        ]);

        try {
            $result = json_encode($client->showCustomer($id));
        } catch (Exception $ex) {
            $result = false;
        }
        return $result;
    }


    public static function getAmount($order)
    {
        return collect($order->products)
                ->map(function ($p) {
                    return ($p['price'] * $p['quantity']) - data_get($p, 'discount_amount');
                })->sum() + $order->shipping_amount;
    }

}
