<?php

namespace App\Http\Controllers;

use App\Product;
use App\VariationInfo;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Rap2hpoutre\FastExcel\FastExcel;
use Illuminate\Support\Str;

class SkuInventoryController extends Controller
{
    public function exportInventoryReport(Request $request)
    {
        $options = [
            'active' => $request->active,
            'type' => $request->type,
            'categories' => $request->categories,
            'vendors' => $request->vendors
        ];

        $title = $this->generateTitle($options);
        $title = str_replace('/', '-', $title);
        dispatch(function () use ($options, $title) {
            (new FastExcel($this->getUsersOneByOne($options)))->export(
                storage_path("app/{$title}.csv")
            ); //->export('storage/app/exports.csv');
            (new ReportsController)->create($title);
        });
    }


    public function getUsersOneByOne($options)
    {
        $categories = collect();
        if (data_get($options, 'categories')) {
            collect(data_get($options, 'categories'))->each(function ($category) use (&$categories) {
                $categories->push(DB::table('categories')->select('name')->where('id', $category)->first()->name);
            });
        }


        if (data_get($options, 'type') != 'Variations') {
            $eachChunk = 100;
            $productCount = Product::count();
            $timesToLoop = $productCount / $eachChunk;

            $category_products = collect(DB::table('category_product')->get());

            for ($i = 0; $i <= $timesToLoop; $i++) {
                foreach (
                    DB::table('products')->select('*')->skip($i * $eachChunk)->take($eachChunk)->cursor() as $user
                ) {
                    $active = 0;
                    if ($user->visibility) {
                        $active = 1;
                    }

                    if ((data_get($options, 'active') == null || ($active == data_get($options, 'active')))
                        && (!data_get($options, 'vendors') || collect(data_get($options, 'vendors'))->contains(
                                data_get($user, 'vendor_id')
                            ))
                        && (!data_get($options, 'categories') || $categories->intersect(
                                collect(data_get(json_decode($user->search), 'categories'))
                            )->count())
                    ) {
                        yield [
                            'SKU' => $user->sku,
                            'TYPE' => 'Product',
                            'TITLE' => $user->title,
                            'STORE QUANTITY' => $user->store_quantity,
                            'ONLINE QUANTITY' => $user->website_quantity,
                            'TRACK INVENTORY' => $user->track_inventory,
                            'VENDOR' => data_get(json_decode($user->search), 'vendor'),
                            'ACTIVE' => $active,
                        ];
                    }
                }
            }
        }
        if (data_get($options, 'type') != 'Products') {
            $eachChunk = 100;
            $productCount = VariationInfo::count();
            $timesToLoop = $productCount / $eachChunk;

            for ($i = 0; $i <= $timesToLoop; $i++) {
                foreach (
                    DB::table('variation_infos')->select('*')->skip($i * $eachChunk)->take($eachChunk)->cursor(
                    ) as $user
                ) {
                    if ((data_get($options, 'active') == null || ($user->visibility == data_get($options, 'active')))
                        && (!data_get($options, 'vendors') || collect(data_get($options, 'vendors'))->contains(
                                data_get(json_decode($user->product_json), 'vendor_id')
                            ))
                        && (!data_get($options, 'categories') || $categories->intersect(
                                collect(
                                    data_get(
                                        json_decode(data_get(json_decode($user->product_json), 'search')),
                                        'categories'
                                    )
                                )
                            )->count())
                    ) {
                        yield [
                            'SKU' => $user->sku,
                            'TYPE' => 'Variation',
                            'TITLE' => data_get(json_decode($user->product_json), 'title'),
                            'STORE QUANTITY' => $user->store_quantity,
                            'ONLINE QUANTITY' => $user->website_quantity,
                            'TRACK INVENTORY' => $user->track_inventory,
                            'VENDOR' => data_get(
                                json_decode(data_get(json_decode($user->product_json), 'search')),
                                'vendor'
                            ),
                            'ACTIVE' => $user->visibility
                        ];
                    }
                }
            }
        }
    }

    public function generateTitle($options)
    {
        $title = '';

        if (data_get($options, 'active') == 1) {
            $title .= 'Active ';
        } elseif (data_get($options, 'active') === 0) {
            $title .= 'Non Active ';
        }

        if (data_get($options, 'type') == 'Products') {
            $title .= 'Products ';
        } elseif (data_get($options, 'type') == 'Variations') {
            $title .= 'Variations ';
        } else {
            $title .= 'Products and Variations ';
        }

        if (data_get($options, 'categories')) {
            $title .= 'For Categories: ';
            collect(data_get($options, 'categories'))->each(function ($category) use (&$title) {
                $title .= DB::table('categories')->select('name')->where('id', $category)->first()->name . ', ';
            });
        }


        if (data_get($options, 'vendors')) {
            $title .= 'For Vendors: ';
            collect(data_get($options, 'vendors'))->each(function ($vendor) use (&$title) {
                $title .= DB::table('vendors')->select('name')->where('id', $vendor)->first()->name . ', ';
            });
        }

        $title .= 'Export';

        return Str::limit($title, 200);
    }
}
