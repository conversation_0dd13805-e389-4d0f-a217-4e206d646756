<?php

namespace App\Http\Controllers\Admin;

use App\Category;
use App\Filter;
use App\Http\Controllers\Controller;
use Illuminate\Support\Str;
use Outl1ne\MenuBuilder\Models\Menu;
use Outl1ne\MenuBuilder\Models\MenuItem;

class CategoriesController extends Controller
{
    public static function filters($ids)
    {
        return Category::with(['filters'])
            ->find(explode(',', $ids))->map(function ($item) {
                return $item->filters;
            })
            ->collapse()
            ->unique('id')
            ->sortBy('sort_order')
            ->map(function ($item) {
                return [
                    'id' => $item->id . '_',
                    'name' => $item->name,
                    'label' => $item->name,
                    'children' => $item->items()
                        ->get(['id', 'name'])->map(function ($item) {
                            return [
                                'name' => $item['name'],
                                'label' => $item['name'],
                                'id' => $item['id'],
                            ];
                        }),
                ];
            })
            ->values();
    }

    public static function allFilters()
    {
        return Filter::all()
            ->sortBy('sort_order')
            ->map(function ($item) {
                return [
                    'id' => $item->id . '_',
                    'name' => $item->name,
                    'label' => $item->name,
                    'children' => $item->items()
                        ->get(['id', 'name'])->map(function ($item) {
                            return [
                                'name' => $item['name'],
                                'label' => $item['name'],
                                'id' => $item['id'],
                            ];
                        }),
                ];
            })
            ->values();
    }

    public static function categoryTree()
    {
        return \App\Category::get(['name', 'id'])
        ->map(function ($item) {
            return [
                'name' => $item->name,
                'label' => $item->name,
                'id' => $item->id,
            ];
        });
        if ($menu = Menu::where('slug', 'departments')->first()) {
            $ids = MenuItem::where('menu_id', $menu->id)
                ->where('class', 'App\Classes\Categories')
                ->pluck('value');

            $departments = self::formatForAPI($menu);
            return [
                $departments,
                [
                    'name' => 'Other Categories',
                    'children' => Category::whereNotIn('id', $ids)->get(['name', 'id'])
                        ->map(function ($item) {
                            return [
                                'name' => $item->name,
                                'label' => $item->name,
                                'id' => $item->id,
                            ];
                        })
                ],
            ];
        } else {
            return Category::get(['name', 'id']);
        }
    }

    public static function formatForAPI($menu)
    {
        $array = [
            'id' => $menu->id,
            'name' => $menu->name,
            'label' => $menu->name,
        ];

        if (count($menu->rootMenuItems) > 0) {
            $array['id'] = Str::random(4);
            $array['children'] = collect($menu->rootMenuItems)->map(function ($item) {
                return self::formatMenuItem($item);
            });
        }

        return $array;
    }

    public static function formatMenuItem($menuItem)
    {
        $array = [
            'name' => $menuItem->name,
            'label' => $menuItem->name,
        ];

        if ($menuItem['class'] != 'App\Classes\Categories' || count($menuItem->children) > 0) {
            $array['id'] = Str::random(4);
            $array['children'] = collect($menuItem->children)->map(function ($item) {
                return self::formatMenuItem($item);
            });
        } else {
            $array['id'] = $menuItem->value;
        }

        return $array;
    }

    public static function menuTree()
    {
        return nova_get_menu('departments')['menuItems'];
    }
}
