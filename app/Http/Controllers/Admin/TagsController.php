<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Tag;

class TagsController extends Controller
{
    public static function index()
    {
        return Tag::all(['id', 'name', 'path'])->map(function ($item) {
            return [
                'name' => $item['name'],
                'label' => $item['name'],
                'id' => $item['path'],
            ];
        });
    }
}
