<?php

namespace App\Http\Controllers\Admin;

use App\AssociatedPurchase;
use App\Digital;
use App\GiftCard;
use App\GiftRecord;
use App\Http\Controllers\Api\BagController;
use App\Http\Controllers\Api\EmailsController;
use App\Http\Controllers\Api\GiftCardController;
use App\Http\Controllers\ChargeController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\PosController;
use App\Http\Controllers\PosOnlineController;
use App\Http\Controllers\TransactionController;
use App\Jobs\OrderCreate;
use App\Membership;
use App\Order;

class ModifyOrderController extends Controller
{
    public static function Breakdown($id, $request)
    {
        $order = Order::findOrFail($id);

        $original_products = collect($order->products);
        $canceled_products = collect($request->cancelproducts);
        $added_products = collect(
            $request->addproducts
        ); //collect(GetFromFrontEnd(collect(makeForFronEnd(collect($request->addproducts)))));
        $discount_id = $request->discountId;
        $shipping_amount = $request->shippingAmount ?? null;

        $message = $request->message ?? null;

        $final_products = $original_products->map(function ($product) use ($canceled_products) {
            $cancelled = $canceled_products->filter(function ($prod) use ($product) {
                return $product['id'] == $prod['id'] &&
                    $product['item_type'] == $prod['item_type'];
            })->first();

            if ($cancelled) {
                $cancelled['quantity'] = $product['quantity'] - $cancelled['cancelQuantity'];
                return $cancelled;
            }
            return $product;
        })->filter()->where('quantity', '>', 0)->merge($added_products);

        $breakdown = (new BagController)->getBreakDown(request()->merge([
            'products' => $final_products,
            'address' => data_get($order, 'shipping.shippingInfo'),
            'discountId' => $discount_id,
            'shipping_id' => data_get($order, 'shipping.shippingType.id'),
            'shipping_amount' => $shipping_amount
        ]));

        $breakdown['transactions'] = self::getTransactions($order, data_get($breakdown, 'total'));

        return $breakdown;
    }

    public static function getTransactions($order, $new_total)
    {
        $message = '';
        $error_message = '';
        $creditInfo = [];
        $giftCards = [];
        $type = '';

        $new_total = number_format($new_total, 2);
        $order_total = number_format($order->grand_total, 2);


        if ($order_total > $new_total) {
            $type = 'refund';
            $refund = $order_total - $new_total;

            $chargeLeft = $order->chargeLeft;

            if ($chargeLeft >= $refund) {
                $creditInfo = [
                    'type' => data_get($order, 'payments.creditInfo.type'),
                    'last_four' => data_get($order, 'payments.creditInfo.last_four'),
                    'amount' => $refund,
                    'payment_type' => $order->payment_type,
                    'paymentt_id' => $order->payment_id,
                ];
            } else {
                $creditInfo = $chargeLeft ? [
                    'type' => data_get($order, 'payments.creditInfo.type'),
                    'last_four' => data_get($order, 'payments.creditInfo.last_four'),
                    'amount' => $chargeLeft,
                    'payment_type' => $order->payment_type,
                    'paymentt_id' => $order->payment_id,
                ] : [];

                $total_left = $refund - $chargeLeft;

                $cards = collect(data_get($order, 'payments.giftCard'))->map(function ($card) {
                    return GiftCard::find(data_get($card, 'id'));
                });

                $giftCards = collect($cards)->map(function ($card) use (&$total_left, $order) {
                    $amount = min($card->amount - $order->TotalRefundedForGiftCard($card->id), $total_left);

                    if ($total_left > 0 && $amount > 0) {
                        $total_left -= $amount;

                        return [
                            'id' => $card->id,
                            'code' => substr($card->code, -4),
                            'amount' => $amount
                        ];
                    }
                })->filter()->values();
            }

            $message = 'Refund $' . $refund . ' to';
        } else {
            if ($order_total < $new_total) {
                $type = 'charge';
                $add_charge = $new_total - $order_total;

                $message = 'Charge $' . $add_charge . ' from';

                if ($order->payment_type == 'App\CreditCardPayment') {
                    $creditInfo = [
                        'type' => data_get($order, 'payments.creditInfo.type'),
                        'last_four' => data_get($order, 'payments.creditInfo.last_four'),
                        'amount' => $add_charge,
                        'payment_type' => $order->payment_type,
                        'paymentt_id' => $order->payment_id
                    ];
                } else {
                    $cards = collect(data_get($order, 'payments.giftCard'))->map(function ($card) {
                        return GiftCard::find(data_get($card, 'id'));
                    });

                    $giftCards = collect($cards)->map(function ($giftCard) use ($order, &$add_charge) {
                        $balance = (new GiftCardController)->checkGiftCard(
                            request()->merge(['code' => $giftCard->code])
                        );

                        $amount = min($balance, $add_charge);

                        if ($add_charge > 0 && $amount > 0) {
                            $add_charge -= $amount;

                            return [
                                'id' => $giftCard->id,
                                'code' => substr($giftCard->code, -4),
                                'amount' => $amount
                            ];
                        }
                    })->filter()->values();
                }
            } else {
                return;
            }
        }

        if ($order_total < $new_total && data_get($creditInfo, 'amount') + collect($giftCards)->sum(
                'amount'
            ) < $new_total - $order_total) {
            $error_message = 'You cannot add this charge..';
        } else {
            if ($order_total > $new_total && data_get($creditInfo, 'amount') + collect($giftCards)->sum(
                    'amount'
                ) < $order_total - $new_total) {
                $error_message = 'You cannot refund this amount..';
            }
        }

        return [
            'error_message' => $error_message,
            'message' => $message,
            'creditInfo' => $creditInfo,
            'giftCards' => collect($giftCards),
            'type' => $type,
        ];
    }

    public static function Submit($id, $request)
    {
        $order = Order::findOrFail($id);

        $products = collect($request->products);
        $discount_id = $request->discountId;
        $shipping_amount = $request->shippingAmount ?? null;
        $savingsData = $request->savingsData;

        $message = $request->message ?? null;


        $totals = (new BagController)->getBreakDown(request()->merge([
            'products' => $products,
            'address' => data_get($order, 'shipping.shippingInfo'),
            'discountId' => $discount_id,
            'shipping_id' => data_get($order, 'shipping.shippingType.id'),
            'shipping_amount' => $shipping_amount
        ]));

        $transactions = self::getTransactions($order, data_get($totals, 'total'));

        ChargeController::ModifyPayments($order, $transactions);

        $reversion = [
            'sub_total' => $order->sub_total,
            'tax_amount' => $order->tax_amount,
            'shipping_amount' => $order->shipping_amount,
            'meta->gift_options' => data_get($order, 'meta.gift_options'),
            'discount' => $order->discount,
            'discount_amount' => $order->discount_amount,
            'discount_id' => $order->discount_id,
            'grand_total' => $order->grand_total,
            'products' => $order->products,
            'canceled_products' => collect($request->cancelproducts),
            'added_products' => collect($request->addproducts),
            'product_ids' => $order->product_ids,
        ];
        $date = now()->toDateTimeString();

        OrderCreate::dispatch(TransactionController::class, 'DeleteTransaction', $order)->onQueue(
            'low'
        );

        OrderCreate::dispatch(Membership::class, 'removePoints', $order);

        OrderCreate::dispatch(GiftRecord::class, 'removeFromOrder', $order);

        OrderCreate::dispatch(
            InventoryController::class,
            'AddToInventory',
            collect($order->products)->where('item_type', 'physical')
        );

        $order->update([
            "reversions->{$date}" => $reversion,
            'sub_total' => $totals['sub_total'],
            'tax_amount' => $totals['tax'],
            'shipping_amount' => $totals['shipping'],
            'meta->gift_options' => $totals['giftOptions'],
            'discount' => $savingsData,
            'discount->savings' => data_get($totals, 'discount'),
            'discount_amount' => data_get($totals, 'discount'),
            'discount_id' => $discount_id,
            'grand_total' => $totals['total'],
            'products' => $totals['bag'],
            'product_ids' => $totals['bag']->pluck('product_id')->filter(),
        ]);

        OrderCreate::dispatch(Digital::class, 'saveOrder', $order);

        OrderCreate::dispatch(GiftRecord::class, 'saveFromOrder', $order);

        OrderCreate::dispatch(InventoryController::class, 'DeductFromInventory', $order);

        OrderCreate::dispatch(Membership::class, 'updatePoints', $order);

        OrderCreate::dispatch(TransactionController::class, 'AddTransaction', $order)->onQueue(
            'low'
        );

        OrderCreate::dispatch(AssociatedPurchase::class, 'saveAssociation', $order)->onQueue('low');

        OrderCreate::dispatch(PosController::class, 'CreateOrder', $order)->onQueue('low');
        OrderCreate::dispatch(PosOnlineController::class, 'CreateOrder', $order)->onQueue('low');

        EmailsController::SendOrderModified(
            $order,
            $reversion,
            collect($message)->implode('<br/>')
        );

        return;
    }
}
