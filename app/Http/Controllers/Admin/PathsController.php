<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Capitalc\Paths\Paths;
use Illuminate\Support\Facades\DB;
use Laravel\Nova\Fields\Hidden;

class PathsController extends Controller
{
    public static function index()
    {
        $array = [];
        $array[] = [
            'label' => 'Categories',
            'id' => 'Categories',
            'children' => DB::table('categories')->get(['id', 'name'])
                ->map(function ($item) {
                    return [
                        'id' => "/categories/" . str_slug($item->name) . "/{$item->id}",
                        'name' => $item->name,
                        'label' => $item->name,
                    ];
                })
        ];
        $array[] = [
            'label' => 'Automated Categories',
            'id' => 'Automated Categories',
            'children' => DB::table('automated_categories')->get(['id', 'name'])
                ->map(function ($item) {
                    return [
                        'id' => "/a/categories/" . str_slug($item->name) . "/{$item->id}",
                        'name' => $item->name,
                        'label' => $item->name,
                    ];
                })
        ];

        $array[] = [
            'label' => 'Vendors',
            'id' => 'Vendors',
            'children' => DB::table('vendors')->get(['id', 'name'])
                ->map(function ($item) {
                    return [
                        'id' => "/vendor/" . str_slug($item->name) . "/{$item->id}",
                        'name' => $item->name,
                        'label' => $item->name,
                    ];
                })
        ];

        $array[] = [
            'label' => 'Creators',
            'id' => 'Creators',
            'children' => DB::table('creators')->get(['id', 'name'])
                ->map(function ($item) {
                    return [
                        'id' => "/creator/" . str_slug($item->name) . "/{$item->id}",
                        'name' => $item->name,
                        'label' => $item->name,
                    ];
                })
        ];
        $array[] = [
            'label' => 'Pages',
            'id' => 'Pages',
            'children' => DB::table('pages')->get(['id', 'name', 'title'])
                ->map(function ($item) {
                    return [
                        'id' => "/" . str_slug($item->title) . "/{$item->id}",
                        'name' => $item->name,
                        'label' => $item->name,
                    ];
                })
        ];
        $array[] = [
            'label' => 'Collections',
            'id' => 'Collections',
            'children' => DB::table('product_collections')->get(['id', 'name'])
                ->map(function ($item) {
                    return [
                        'id' => "/collections/" . str_slug($item->name) . "/{$item->id}",
                        'name' => $item->name,
                        'label' => $item->name,
                    ];
                })
        ];

        $array[] = [
            'label' => 'Custom',
            'id' => 'custom',
        ];

        return $array;
    }

    public static function novaComponents()
    {
        return [
            Paths::make('Link', 'link')->withMeta(
                [
                    'options' => self::index(),
                    'tags' => DB::table('tags')->get(['name', 'id'])
                        ->map(function ($item) {
                            return ['label' => data_get(json_decode($item->name), 'en'), 'id' => $item->id];
                        })->values()->toArray(),
                    'statuses' => [
                        ['label' => 'New', 'id' => 'new'],
                        ['label' => 'Sale', 'id' => 'sale'],
                    ]
                ]
            ),
            Hidden::make('Filters', 'filters')->hide(),
            Hidden::make('Tags ids', 'tags_ids')->hide(),
            Hidden::make('Status', 'status')->hide(),
            Hidden::make('Path', 'path')->hide()
        ];
    }

    public static function vendors()
    {
        if (request()->is('nova-api/pages')) {
            return [];
        }
        return DB::table('vendors')->get(['name', 'id'])
            ->map(function ($item) {
                return ['display' => $item->name, 'value' => $item->id];
            });
    }

    public static function relationships()
    {
        return;
    }
}
