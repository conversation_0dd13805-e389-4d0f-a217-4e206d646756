<?php

namespace App\Http\Controllers\Admin;

use App\GiftCard;
use App\GiftCardSettings;
use App\Http\Controllers\Api\EmailsController;
use App\Http\Controllers\Api\GiftCardController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\ShippingController;
use App\Order;

class OrdersController extends Controller
{
    public static function captureCharge($request)
    {
        $order = Order::find($request->orderId);
        if (!data_get($order, 'meta.captured')) {
            $order->capturePayment();

            $order->update([
                'payment_status' => 'paid',
                'meta->captured' => true,
                'meta->captured_source' => 'nova at ' . now()->toDateTimeString()
            ]);
        }
    }

    public static function update($id)
    {
        $order = Order::find($id);

        $array = [];
        if (request()->shipping) {
            collect(request()->shipping)->map(function ($value, $key) use (&$array) {
                $array = array_merge(["shipping->shippingInfo->$key" => $value], $array);
            });
        }
        if (request()->pickup) {
            collect(request()->pickup)->map(function ($value, $key) use (&$array) {
                $array = array_merge(["shipping->pickupInfo->$key" => $value], $array);
            });
        }
        if (request()->payments) {
            collect(request()->payments)->map(function ($value, $key) use (&$array) {
                $array = array_merge(["payments->creditInfo->$key" => $value], $array);
            });
        }
        $order->update($array);

        return response()->json(['success' => true]);
    }

    public static function refundAmount($request)
    {
        $order = Order::find($request->orderId);
        $refundAmount = (double)$request->amount;
        $message = $request->message;
        $type = $request->type;

        if ($type == 'giftCard') {
            $giftCard = GiftCard::create([

                'media' => GiftCardSettings::first()->media_urls[0],
                'amount' => $refundAmount,
                'balance' => $refundAmount,
                'to_name' => $order->customer->name,
                'from_name' => 'Eichlers Team',
                'message' => collect($message)->implode('<br/>'),
                'to_email' => $order->customer->email,
                'code' => GiftCard::createUniqueUuid(),
            ]);

            $transaction = $giftCard->transactions()->create([
                'order_id' => $order->id,
                'type' => 'Return Create'
            ]);

            GiftCardController::createVoucher($transaction);

            EmailsController::SendGiftCard($giftCard);

            $payments = [
                'giftCards' => [
                    'id' => $giftCard->id,
                    'code' => substr($giftCard->code, -4),
                ],
                'amount' => $refundAmount,
                'message' => collect($message)->implode('<br/>')
            ];

            $refunds = data_get($order, 'payments.egift_refunds') ?? [];

            $refunds = collect($refunds)->push([
                'Date' => now(),
                'Payments' => $payments
            ]);

            $order->update([
                'payments->egift_refunds' => $refunds,
                'payments->egift_refunds_total' => collect($refunds)->map->Payments->pluck('amount')->sum()
            ]);

            return;
        }
        if (+number_format($order->refundMax, 2, '.', '') < +number_format($refundAmount, 2, '.', '')) {
            $refundMax = $order->refundMax;
            abort(400, "Max refund is ${refundMax}");
        }


        $charge_left = $order->chargeLeft;

        $creditInfo = [];
        $giftCards = [];

        if ($charge_left) {
            $order->refundPayment(min($charge_left, $refundAmount));

            $creditInfo = [
                'type' => data_get($order, 'payments.creditInfo.type'),
                'amount' => $refundAmount,
                'last_four' => data_get($order, 'payments.creditInfo.last_four'),
                'payment_type' => data_get($order, 'payments.creditInfo.payment_type'),
            ];
        }

        if ($refundAmount > $charge_left) {
            $total_left = $refundAmount - $charge_left;

            $giftCards = collect(data_get($order, 'payments.giftCard'))->map(
                function ($card) use (&$total_left, $order) {
                    // $amount = $card['amount'] - min($total_left, $order->TotalRefundedForGiftCard($card['id']));
                    $amount = min($card['amount'] - $order->TotalRefundedForGiftCard($card['id']), $total_left);

                    if ($total_left > 0 && $amount > 0) {
                        $total_left -= $amount;

                        $giftCard = GiftCard::find(data_get($card, 'id'));

                        $giftCard->update([
                            'balance' => $giftCard->balance + $amount
                        ]);

                        $transaction = $giftCard->transactions()->create([
                            'order_id' => $order->id,
                            'amount' => $amount,
                            'type' => 'Refund'
                        ]);

                        GiftCardController::refillVoucher($transaction);
                        return [
                            'id' => $card['id'],
                            'code' => $card['code'],
                            'amount' => $amount
                        ];
                    }
                }
            );
        }

        $payments = [
            'creditInfo' => $creditInfo,
            'giftCards' => $giftCards,
            'amount' => $refundAmount,
            'message' => collect($message)->implode('<br/>')
        ];

        $refunds = data_get($order, 'payments.refunds') ?? [];

        $refunds = collect($refunds)->push([
            'Date' => now(),
            'Payments' => $payments
        ]);

        $order->update([
            'payments->refunds' => $refunds,
            'payments->refunds_total' => collect($refunds)->map->Payments->pluck('amount')->sum()
        ]);

        EmailsController::SendRefundOrder($payments, $order->customer);
    }


    public static function markAsShipped($request)
    {
        $request = [
            'SS-UserName' => env('SHIP_STATION_USERNAME'),
            'SS-Password' => env('SHIP_STATION_PASSWORD'),
            'order_number' => $request->orderId,
            'carrier' => 'Other',
            'service' => '',
            'tracking_number' => '',
            'source' => 'nova'
        ];
        (new ShippingController)->post(request()->merge($request));
    }

    public static function markAsDelivered($request)
    {
        $order = Order::find($request->orderId);

        if (!data_get($order, 'meta.captured')) {
            $order->capturePayment();

            $order->update([
                'payment_status' => 'paid',
                'meta->captured' => true,
                'meta->captured_source' => 'nova at ' . now()->toDateTimeString()
            ]);
        }

        $order->markAsDelivered();
    }

    public static function addToAccount($id)
    {
        $order = Order::find($id);

        $order->update([
            'guest' => false,
        ]);

        return response()->json(['success' => true]);
    }
}
