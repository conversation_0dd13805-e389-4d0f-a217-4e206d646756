<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Api\CalculateRefundController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\ProcessReturnController;
use App\Returns;

class ReturnsController extends Controller
{
    public static function update($id)
    {
        $request = request();

        $return = Returns::find($id);

        $breakDown = (new CalculateRefundController)->GetBreakDown(request()->merge([
            'products' => $request->products,
            'order_id' => $return->order_id,
            'final' => false,
        ]));

        $return->update([
            'dropoff' => $request->dropoff,
            'products' => $breakDown['products'],
            'sub_total' => $breakDown['sub_total'],
            'tax_amount' => $breakDown['tax_amount'],
            'payments' => data_get($breakDown, 'payments'),
            'grand_total' => max($breakDown['grand_total'], 0),
            'shipping_amount' => $breakDown['shipping_amount'],
            'discount_amount' => $breakDown['discount_amount'],
        ]);


        return response()->json(['response' => 'success'], 200);
    }

    public static function process($id)
    {
        $request = request();

        $return = Returns::find($id);

        if ($return->status == 'processed') {
            return;
        }

        $request = [
            'products' => $request->products,
            'returnId' => $return->id,
            'final' => true
        ];

        $request = request()->merge($request);
        ProcessReturnController::process(forceObject($request));

        return response()->json(['response' => 'success'], 200);
    }
}
