<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Product;

class ProductsController extends Controller
{
    public static function productVariations(Product $product)
    {
        return $product;
    }

    public function index()
    {
        return Product::paginate();
    }

    public function duplicate($slug, Product $product)
    {
        $many = ['categories', 'creators', 'tags'];

        $newModel = $product->replicate();
        $newModel->title = $newModel->title . ' - Copy';
        $newModel->visibility = false;

        unset($newModel['notification']);

        $newModel->withoutEvents(function () use ($newModel) {
            $newModel->push();
        });

        foreach ($product->getRelations() as $relation => $items) {
            if (in_array($relation, $many)) {
                foreach ($items as $item) {
                    $newModel->{$relation}()->attach($item->id);
                }
            }
        }

        return [
            'status' => 200,
            'message' => 'Done',
            'destination' => '/resources/products/' . $newModel->id . '/edit',
            'id' => $newModel->id
        ];
    }
}
