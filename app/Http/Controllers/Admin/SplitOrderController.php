<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Order;

class SplitOrderController extends Controller
{
    public static function splitOrder($id, $request)
    {
        return;
        $order = Order::findOrFail($id);

        $notify = $request->notify;
        $shipments = $request->shipments;

        $order->splitShipments()->delete();

        collect($shipments)->filter(function ($shipment) {
            return collect(data_get($shipment, 'products'))->count();
        })->each(function ($shipment) use ($order) {
            $products = collect(data_get($shipment, 'products'))->map(function ($product) {
                if (data_get($product, 'quantity') > 0) {
                    return $product;
                }
            })->filter()->values();

            $order->splitShipments()->create([
                'products' => $products
            ]);
        });
    }
}
