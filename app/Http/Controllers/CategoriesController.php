<?php

namespace App\Http\Controllers;

use App\Category;

class CategoriesController extends Controller
{
    public static function saveFromAdmin()
    {
        self::saveItems(request()->all(), null);
        return response()->json(['response' => 'success'], 200);
    }

    public static function saveItems($data, $parent_id = null)
    {
        $old = null;
        foreach ($data as $key => $item) {
            $data = [
                'parent_id' => $parent_id,
            ];
            $category = Category::firstOrCreate(
                ['id' => $item['id']],
                ['parent_id' => $parent_id]
            );

            $category->update(['parent_id' => $parent_id]);

            if ($old) {
                $category->afterNode($old)->save();
            }
            $old = $category;

            if ($item['children']) {
                self::saveItems($item['children'], $category->id);
            }
        }
        $old = null;
    }

    public function index()
    {
        return view('categories.index');
    }

    public function show($slug, Category $category)
    {
        return view('categories.show', compact('category'));
    }
}
