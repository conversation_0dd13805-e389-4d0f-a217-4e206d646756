<?php

namespace App\Http\Controllers;

use App\Discount;
use TaxJar\Client;

class TaxJarTestController extends Controller
{

    function GetTotalForBags($bags, $address, $discountId = null)
    {
        $address = is_array($address) ? (object)$address : $address;

        $client = Client::withApiKey(env('TAX_JAR'));
        $client->setApiConfig('headers', [
            'x-api-version' => '2022-01-24'
        ]);
        $tax_order = [

            'from_country' => 'US',
            'from_zip' => '11219',
            'from_state' => 'NY',
            'from_city' => 'Brooklyn',
            'to_country' => $address->country,
            'to_zip' => $address->postal_code,
            'to_state' => $address->state,
            'to_city' => $address->city,
            'to_street' => $address->address_line_1,
        ];

        if ($customer = auth()->user()) {
            $collection = $customer->bag;
        } else {
            $collection = GetFromFrontEnd($bags);
        }

        $discount = Discount::find($discountId) ?? null;
        $used = false;

        $tax_collection = $collection->map(function ($item, $index) use ($discount, &$used) {
            $savings = $this->GetDiscount($discount, $item, $used);
            $used = $savings ? true : false;

            return [
                'id' => $index,
                'quantity' => $item->quantity,
                'product_tax_code' => $item->model->tax_code,
                'unit_price' => $item->model_type == 'App\GiftOptions' ? $item->model->price : $item->model->toArray(
                )['price'],
                'discount' => $savings
            ];
        });

        $sub_total = $tax_collection->pluck('unit_price')->sum();
        $discountAmount = $tax_collection->pluck('discount')->sum();

        $tax_order['amount'] = $sub_total - $discountAmount;
        $tax_order['shipping'] = 5.99;                 //????????????????????????
        $tax_order['line_items'] = $tax_collection;
        $taxes = $client->taxForOrder($tax_order);
        $amount_to_collect = $taxes->amount_to_collect;

        $total = $sub_total + $amount_to_collect;
        return [
            'sub_total' => $sub_total,
            'tax' => $amount_to_collect,
            'shipping' => 1.99,
            'discount' => $discountAmount,
            'total' => $total
        ];
    }


    private function GetDiscount($discount, $item, $used)
    {
        if ($discount == null) {
            return 0;
        }
        if ($discount->discount_type == 'fixed' && $used) {
            return 0;
        } else {
            return $discount->getSavings([$item])['savings'];
        }
    }


    function GetFromFrontEnd($bags)
    {
        return collect(forceObject($bags))->map(function ($item) {
            if ($item->type == 'product') {
                $type = 'App\Product';
            } else {
                if ($item->type == 'variation') {
                    $type = 'App\VariationInfo';
                } else {
                    if ($item->type == 'giftCard') {
                        $type = 'App\GiftCard';
                    } else {
                        return;
                    }
                }
            }
            return
                App\Bag::make([
                    'model_id' => $item->id,
                    'model_type' => $type,
                    'quantity' => $item->quantity,
                ]);
        });
    }
    // function GetTotalForBags(){//$bags, $address, $discountId){

    //     $discountId = 1;
    //     $address = ['country' => "US", 'state' => 'NY', 'postal_code' => '11219', 'city' => 'Brooklyn', 'address_line_1' => '5304 13th Ave'];
    //     $bags = [
    //              [
    //                 "title" => "Numquam alias aut laborum tempore atque dicta fugiat facilis.",
    //                 "vendor" => "dolor",
    //                 "path" => "/products/numquam-alias-aut-laborum-tempore-atque-dicta-fugiat-facilis/93",
    //                 "price" => 143.78,
    //                 "fakePrice" => 0,
    //                 "taxable" => true,
    //                 "product_id" => 7,
    //                 "media" => "/img/default_image.png",
    //                 "id" => 93,
    //                 "type" => "product",
    //                 "item_type" => null,
    //                 "quantity" => 1
    //              ],
    //             [
    //                 "title" => "Neque facilis sint officia quaerat assumenda et sed.",
    //                 "vendor" => "officiis",
    //                 "path" => "/products/neque-facilis-sint-officia-quaerat-assumenda-et-sed/1",
    //                 "price" => 6,
    //                 "fakePrice" => 78.68,
    //                 "taxable" => true,
    //                 "product_id" => 5,
    //                 "media" => "https://s3.amazonaws.com/meilech.dev/media/20/conversions/com_fwi-thumbnail.jpg",
    //                 "id" => 1,
    //                 "type" => "product",
    //                 "item_type" => "phsical"
    //                 ,"quantity" => 1
    //             ]
    //         ];

    //     $client = \TaxJar\Client::withApiKey('********************************');

    //     $tax_order = [
    //         'from_country' => 'US',
    //         'from_zip' => '11219',
    //         'from_state' => 'NY',
    //         'from_city' => 'Brooklyn',

    //         'to_country' => $address['country'],
    //         'to_zip' => $address['postal_code'],
    //         'to_state' => $address['state'],
    //         'to_city' => $address['city'],
    //         'to_street' => $address['address_line_1'],

    //     ];

    //     if($customer = auth()->user()){
    //         $collection = $customer->bag;
    //     }
    //     else{
    //         $collection = $this->GetFromFrontEnd($bags);
    //         return $collection;
    //     }

    //     $discount = \App\Discount::find($discountId);
    //     //return $collection;
    //     $collection = $collection->map(function($item, $index) use ($discount){
    //         return[
    //             'id' => $index,
    //             'quantity' => $item->quantity,
    //             'product_tax_code' => $item->model->tax_code,
    //             'unit_price' => $item->model->toArray()['price'],
    //             'discount' => $discount->getSavings($item->getFrontEndAttribute([$quantity => $item->quantity]))['savings']
    //         ];
    //     });


    //     $sub_total = $collection->pluck('unit_price')->sum();

    //     $tax_order['amount'] = $sub_total - $collection->pluck('discount')->sum();

    //     if ($discount->type == "Free Shipping"){

    //         $tax_order['shipping'] = 0.00;

    //     } else {

    //         $tax_order['shipping'] = 5.99;             //????????????????????????
    //     }

    //     $tax_order['line_items'] = $collection;

    //     return json_encode($tax_order);

    //     $taxes = $client->taxForOrder($tax_order);

    //     $amount_to_collect = $taxes->amount_to_collect;

    //     $total = $sub_total +  $amount_to_collect;

    //     return ['sub_total' => $sub_total, 'tax' => $amount_to_collect, 'shipping' => 5.99, 'discount' => $discountAmount, 'total' => $total];
    // }


    // function GetFromFrontEnd($bags){

    //     return collect($bags)->map(function($item)
    //     {
    //         if($item['type'] == 'product'){
    //             $type = 'App\Product';
    //         }
    //         elseif($item->type == 'variation'){
    //             $type = 'App\VariationInfo';
    //         }

    //         return [
    //             'model' => \App\Bag::make([
    //                 'model_id' => $item['id'],
    //                 'model_type' => $type,
    //             ])->model
    //             // ,
    //             // 'quantity' => $item['quantity'],
    //         ];
    //     });
    // }


    public function GetForFrontEnd($bags)
    {
        return $bags->map(function ($item) {
            return $item->model->getFrontEndAttribute([$quantity => $item->quantity]);
        });
    }
}
