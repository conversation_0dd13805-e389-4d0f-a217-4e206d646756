<?php

namespace App;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
use DateTime;
use Carbon;
use App\AssociatedPurchase;

class FrequentlyBought extends Model
{
    protected $guarded = [];
    public $table = 'frequently_bought';

    public static function getTotalTransactions($date)
    {
        $productIds = DB::table('associated_purchases')->select('product_id')->get()->pluck('product_id');
        $orders = Order::all()->lazy()->where('created_at', '>=', $date);

        $totalOrders = $orders->map(function ($order) {
            return $order->product_ids;
        })->flatten()->intersect($productIds)->countBy();

        $customerIds = $orders->map(function ($order) {
            return [
                'order_id' => $order->id,
                'customer_id' => $order->customer_id
            ];
        });
        return [
            'total_orders_by_product' => $totalOrders,
            'customer_ids' => $customerIds
        ];
    }

    public static function calculateAllBundles()
    {
        $date = self::miningSettings()['setting'];

        $totalTransactions = static::getTotalTransactions($date);

        $totalOrders = $totalTransactions['total_orders_by_product']->toArray();
        $customerIds = $totalTransactions['customer_ids']->toArray();

        $date = now()->parse($date)->timestamp;
        $threshold = self::miningSettings()['threshold'];
        $confidence = self::miningSettings()['confidence'] / 100;

        \App\AssociatedPurchase::all()->lazy()->chunk(50)->each(
            function ($items) use ($date, $totalOrders, $customerIds, $threshold, $confidence) {
                dispatch(function () use ($items, $date, $totalOrders, $customerIds, $threshold, $confidence) {
                    $items->each(function ($item) use ($date, $totalOrders, $customerIds, $threshold, $confidence) {
                        $bundle = $item->calculateRuleMining(
                            $date,
                            $totalOrders,
                            $customerIds,
                            $threshold,
                            $confidence
                        );
                        if ($bundle) {
                            static::updateOrCreate(
                                ['product_id' => $item->product_id],
                                [
                                    'bundle' => json_encode($bundle[0]),
                                    'confidence' => $bundle[1]
                                ]
                            );
                        }
                    });
                });
            }
        );
    }

    public static function miningSettings()
    {
        return [
            'threshold' => settings()->getValue('frequently_bought_together_threshold'),
            'confidence' => settings()->getValue('frequently_bought_together_confidence'),
            'setting' => settings()->getValue('frequent_date')
        ];
    }
}
