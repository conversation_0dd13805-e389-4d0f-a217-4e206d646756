<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Tax extends Model
{
    public $guarded = [];

    public static $options = [
        'null' => 'Uncategorized',
        '22' => 'Gift Card',
        '31000' => 'Digital Goods',
        '20010' => 'Clothing',
        '51010' => 'Non-Prescription',
        '51020' => 'Prescription',
        '40030' => 'Food & Groceries',
        '30070' => 'Software as a Service',
        '81300' => 'Magazines & Subscriptions',
        '81100' => 'Books',
        '81310' => 'Magazine',
        '81110' => 'Textbook',
        '81120' => 'Religious books',
        '40020' => 'Supplements',
        '40010' => 'Candy',
        '40050' => 'Soft Drinks',
        '40060' => 'Bottled Water',
        '41000' => 'Prepared Foods',
        '19009' => 'Printing Services',
        '19001' => 'Advertising Services',
        '10040' => 'Installation Services',
        '19002' => 'Parking Services',
        '19003' => 'Admission Services',
        '19004' => 'Training Services',
        '19006' => 'Dry Cleaning Services',
        '19007' => 'Repair Services',
        '19008' => 'Hairdressing Services',
        '19000' => 'General Services',
        '19005' => 'Professional Services',
        '20041' => 'Clothing - Swimwear',
    ];
}
