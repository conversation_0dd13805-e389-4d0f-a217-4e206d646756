<?php

namespace App\Console\Commands;

use App\Combination;
use Illuminate\Console\Command;

class CreateSubscriptionOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:create-orders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create subscription orders';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Combination::createOrders();
    }
}
