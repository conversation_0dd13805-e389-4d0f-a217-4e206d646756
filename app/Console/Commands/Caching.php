<?php

namespace App\Console\Commands;

use App\Http\Controllers\CacheController;
use App\Jobs\Miscellaneous;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class Caching extends Command
{

    protected $signature = 'cache:update';

    protected $description = 'Command description';


    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        if (Cache::get('refresh')) {
            Cache::forget('refresh');
            Miscellaneous::dispatch(CacheController::class, 'refresh', null);
        }
    }
}
