<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class MoveLogFile extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'move-log-file';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Move Log file to S#';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Storage::disk('backup')->putFileAs(
            'logs/',
            File::get(storage_page('logs/laravel.log')),
            now() . '.log'
        );
        Storage::disk('backup')->put(
            'logs/' . now() . '.log',
            file_get_contents(config('logging.channels.single.path'))
        );
        return 0;
    }
}
