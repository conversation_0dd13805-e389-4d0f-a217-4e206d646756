<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\ReturnLabelController;

class VoidOldReturnLabels extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'returns:void-old-labels';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Voids All Old Labels That Were Not Shipped';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        ReturnLabelController::VoidOldLabels();
    }
}
