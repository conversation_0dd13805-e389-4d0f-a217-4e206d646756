<?php

namespace App\Console\Commands;

use App\InventoryTrack;
use Illuminate\Console\Command;

class DeleteInventoryTrack extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:inventory-track';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete old Inventory Track';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        InventoryTrack::whereDate(
            'created_at',
            '<',
            now()->subDays(settings()->getValue('how_many_days_to_keep_inventory_track'))
        )->delete();
    }
}
