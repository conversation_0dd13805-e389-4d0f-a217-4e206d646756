<?php

namespace App\Console\Commands;

use App\Combination;
use App\Recurring;
use App\SubscriptionType;
use Illuminate\Console\Command;

class SendSubscriptionEmails extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:send-emails';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send subscription notice to customers before the orders are created';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // SubscriptionType::sendTodaysEmails();
        Combination::sendEmails();
        Recurring::sendTodaysEmails();
    }
}
