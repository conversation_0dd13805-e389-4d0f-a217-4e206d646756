<?php

namespace App\Console\Commands;

use App\Http\Controllers\SyncInventoryController;
use Illuminate\Console\Command;

class InventorySync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inventory:sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync Inventory from First Choice Api';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        SyncInventoryController::syncAll();
        return;
    }
}
