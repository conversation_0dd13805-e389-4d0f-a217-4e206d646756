<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Storage;

class DeleteTempImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'medialibrary:clean:name';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        collect(Storage::disk('temp-images')->listContents())->each(function ($folder) {
            if ($folder['timestamp'] < now()->subDay()->timestamp) {
                Storage::disk('temp-images')->deleteDirectory($folder['path']);
            }
        });
    }
}
