<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\PosReportsController;


class InventoryReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inventory:get';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gets the updated Inventory for each product';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        PosReportsController::Reports();
    }
}
