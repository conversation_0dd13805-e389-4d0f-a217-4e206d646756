<?php

namespace App\Console\Commands;

use App\Http\Controllers\SyncInventoryController;
use Illuminate\Console\Command;
use App\Http\Controllers\PosReportsController;

class GetAllSalePrice extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inventory:get-sale-price';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gets nighly all the sale prices';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        SyncInventoryController::syncAllSaleItems();
        return;
    }
}
