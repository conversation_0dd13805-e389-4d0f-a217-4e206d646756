<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Recurring;

class CreateRecurringOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'recurring:create-orders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create recurring orders';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Recurring::createTodaysOrders();
    }
}
