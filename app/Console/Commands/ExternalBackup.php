<?php

namespace App\Console\Commands;

use <PERSON><PERSON>\MediaLibrary\MediaCollections\Models\Media;
use Storage;
use Illuminate\Console\Command;

class ExternalBackup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'external-backup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Backup to other file system.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // command to backup log file to s3
        // aws s3 cp storage/logs/laravel.log s3://cdn.platform.capitalc.co/backup/logs/laravel.log

        $this->database();
        $this->media();

        return 0;
    }

    public function media()
    {
        Media::whereDate(
            'created_at',
            today()->subday()
        )->each(function ($media) {
            $path = $media->getPath();
            if (!Storage::disk(env('EXTERNAL_MEDIA_BACKUP'))->exists($path)) {
                Storage::disk(env('EXTERNAL_MEDIA_BACKUP'))->put(
                    $path,
                    Storage::disk(env('MEDIA_DISK'))->get($path)
                );
                $this->info('copied: ' . $path);
            }
        });
        // $missing = array_diff(
        //     Storage::disk(env('MEDIA_DISK'))->allFiles(),
        //     Storage::disk(env('EXTERNAL_MEDIA_BACKUP'))->allFiles(),
        // );

        collect($missing)->each(function ($item) {
            Storage::disk(env('EXTERNAL_MEDIA_BACKUP'))->put(
                $item,
                Storage::disk(env('MEDIA_DISK'))->get($item)
            );
            $this->info('copied: ' . $item);
        });
    }

    public function database()
    {
        $this->call('backup:run', [
            '--only-db' => true,
            '--only-to-disk' => 'external_database_backup'
        ]);
    }
}
