<?php

namespace App\Console\Commands;

use App\DTO\Klaviyo\BulkCatalogJobDTO;
use App\Enums\Klaviyo\CatalogJobType;
use App\Product;
use App\Services\KlaviyoService;
use App\Services\ProductKlaviyoService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncProductsToKlaviyo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-products-to-klaviyo';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            if (!$this->confirm('❗ Are you sure you want to delete all products from Klaviyo?')) {
                $this->info('Operation cancelled.');
                return;
            }

            $this->info('▶️ Starting deletion of products from Klaviyo...');

            $klaviyoService = new KlaviyoService();
            $nextUrl = null;
            $deletedCount = 0;
            do {
                $response = $klaviyoService->getCatalogItems($nextUrl);
                $catalogItems = $response['data'] ?? [];
                $catalogItemIds = array_column($catalogItems, 'id');
                if (empty($catalogItems)) {
                    $this->info('No catalog items found in Klaviyo.');
                    break;
                }
                $klaviyoService->bulkDeleteCatalogItems($catalogItemIds);
                $deletedCount += count($catalogItemIds);
                $this->info('Deleted ' . $deletedCount . ' products from Klaviyo.');
                $nextUrl = $response['links']['next'] ?? null;
            } while ($nextUrl);
            Product::query()->update(['klaviyo_synced' => 0]);
            $this->info("✅ All products have been queued for deletion.");
            $this->warn("⏳ Please wait until Klaviyo finishes removing all catalog items.");
            $this->warn("🔁 Once there are no products visible in Klaviyo, press Enter to continue...");
            $this->ask('Press Enter to continue');
            $syncedProducts = 0;
            Product::chunk(100, function ($products) use ($klaviyoService, &$syncedProducts) {
                $items = [];
                foreach ($products as $product) {
                    $items[] = ProductKlaviyoService::convertProductToCatalogItem($product);
                }
                if (!empty($items)) {
                    $bulkCatalogItems = new BulkCatalogJobDTO(
                        items: $items,
                        jobType: CatalogJobType::BulkCreate,
                    );
                    $klaviyoService->bulkCreateCatalogItems($bulkCatalogItems);
                    Product::whereIn('id', $products->pluck('id'))->update(['klaviyo_synced' => 1]);
                    $syncedProducts += count($items);
                    $this->info('Synced ' . $syncedProducts . ' products to Klaviyo.');
                }
            });
            $this->info('Sync completed. Total products synced: ' . $syncedProducts);
        } catch (\Exception $e) {
            $this->error('An error occurred while syncing products to Klaviyo: ' . $e->getMessage());
        }
    }
}
