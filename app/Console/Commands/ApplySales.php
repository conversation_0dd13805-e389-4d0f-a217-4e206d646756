<?php

namespace App\Console\Commands;

use App\Http\Controllers\SalesController;
use Illuminate\Console\Command;

class ApplySales extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sale:apply';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Will start or end scheduled sales';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        SalesController::ApplySales();
    }
}
