<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BackupLogFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:logs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Backup all old log files.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // make sure all sub directories {orders, api calls}
        $files = collect(Storage::disk('logs')->allFiles())
            ->filter(function ($file) {
                return Storage::disk('logs')
                        ->lastModified($file) < now()->subday()->timestamp;
            })
            ->filter(function ($file) {
                return Str::endsWith($file, '.log');
            })
            ->map(function ($file) {
                return 'storage/logs/' . $file;
            })
            ->values()
            ->toArray();

        config([
            'backup.backup.source.files.include' => $files,
            'backup.backup.destination.disks' => ['file_backup'],
        ]);

        Artisan::call('backup:run --only-files');
        return 0;
    }
}
