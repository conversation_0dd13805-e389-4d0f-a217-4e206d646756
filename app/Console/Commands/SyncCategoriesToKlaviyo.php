<?php

namespace App\Console\Commands;

use App\Category;
use App\Services\KlaviyoService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncCategoriesToKlaviyo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-categories-to-klaviyo';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('Starting to sync categories to Klaviyo...');
            $klaviyoService = new KlaviyoService();
            Category::query()
                ->chunk(50, function ($categories) use ($klaviyoService) {
                    foreach ($categories as $category) {
                        $klaviyoCategory = $klaviyoService->getCatalogCategory($category->id);
                        if (!$klaviyoCategory) {
                            $this->info('Creating new category in Klaviyo: ' . $category->name);
                            $klaviyoService->createCatalogCategory($category);
                        } else {
                            $this->info('Already exists in Klaviyo: ' . $category->name);
                        }

                    }
                });
            $this->info('Finished syncing categories to Klaviyo.');
        } catch (\Exception $e) {
            Log::error('Error syncing categories to Klaviyo: ' . $e->getMessage());
            $this->error('An error occurred while syncing categories to Klaviyo.');
        }
    }
}
