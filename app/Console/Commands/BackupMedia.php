<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\MediaLibrary\Models\Media;
use Storage;

class BackupMedia extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:media {--date=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $query = Media::query();

        if ($this->option('date') != 'all') {
            $date = now()->parse($this->option('date') ?? now()->subDay());
            $query->whereDate('updated_at', $date);
        }

        $media = $query
            ->pluck('id')
            ->map(function ($id) {
                collect(\Storage::disk(env('MEDIA_DISK'))->allFiles($id))
                    ->each(function ($item) {
                        \Log::channel('backup')->info($item);
                        Storage::disk(env('EXTERNAL_MEDIA_BACKUP'))->put(
                            $item,
                            Storage::disk(env('MEDIA_DISK'))->get($item)
                        );
                    });
            });
        return 0;
    }

    static public function backupAllMedia() {
        $dates = \DB::table('media')
         ->select(\DB::raw('DISTINCT DATE(updated_at) as date'))
         ->get()
         ->pluck('date');

        $dates->each(function ($date, $index) {
             dispatch(new \App\Jobs\BackupMediaJob($date))
             ->onConnection('database')
             ->onQueue('old_media')
             ->delay(now()->addminutes($index * 3));
             
             \Log::channel('backup')->info([$date, $index]);
         });
         return $dates->count() . ' jobs created';
     }
}
