<?php

namespace App\Console\Commands;

use App\Jobs\UpdateProductSearch;
use App\Product;
use Illuminate\Console\Command;

class SyncProductsToAlgolia extends Command
{
    protected $signature = 'app:sync-products-to-algolia';

    protected $description = 'Sync products to Algolia';

    public function handle()
    {
        $this->info('Syncing products with Algolia...');
        Product::isSearchable()
        ->select('id')->chunk(50, function ($products) {
            $products = $products->pluck('id')->toArray();
            UpdateProductSearch::dispatch($products)->onQueue('search');
        });
        Product::isUnSearchable()
        ->select('id')->chunk(50, function ($products) {
            $products->unsearchable();
        });
        $this->info('Products synced with Algolia.');
    }
}
