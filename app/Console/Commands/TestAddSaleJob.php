<?php

namespace App\Console\Commands;

use App\Jobs\ProcessAddSale;
use App\Product;
use Illuminate\Console\Command;

class TestAddSaleJob extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:add-sale-job {--product-id=1} {--count=1 : Number of products to test with}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the ProcessAddSale job with a sample product';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $productId = $this->option('product-id');
        $count = (int) $this->option('count');

        if ($count === 1) {
            // Test with single product
            $product = Product::find($productId);
            if (!$product) {
                $this->error("Product with ID {$productId} not found.");
                return 1;
            }

            $productIds = [$productId];
            $this->info("Testing ProcessAddSale job with single product: {$product->title}");
        } else {
            // Test with multiple products to demonstrate chunking
            $products = Product::take($count)->pluck('id')->toArray();
            if (empty($products)) {
                $this->error("No products found in database.");
                return 1;
            }

            $productIds = $products;
            $this->info("Testing ProcessAddSale job with {$count} products to demonstrate chunking");
        }

        // Sample sale data
        $saleData = [
            'add_sale' => true,
            'sale_type' => 'percent',
            'sale_amount' => 10,
            'sale_from' => 'list_price',
            'start_sale' => now()->format('Y-m-d'),
            'end_sale' => now()->addDays(30)->format('Y-m-d'),
        ];

        // Chunk the products and dispatch jobs (simulating the Nova action behavior)
        $chunks = array_chunk($productIds, 50);
        $totalChunks = count($chunks);

        foreach ($chunks as $index => $chunk) {
            ProcessAddSale::dispatch($saleData, $chunk, $index + 1, $totalChunks);
        }

        $totalProducts = count($productIds);
        $this->info("Dispatched {$totalChunks} job(s) for {$totalProducts} products (max 50 per batch)");
        $this->info('You can monitor the jobs with: php artisan queue:work --verbose');

        return 0;
    }
}
