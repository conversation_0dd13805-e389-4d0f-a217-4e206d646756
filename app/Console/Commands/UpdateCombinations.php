<?php

namespace App\Console\Commands;

use App\Combination;
use Illuminate\Console\Command;

class UpdateCombinations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'combinations:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Combination::whereDate('upcoming_date', today())
            ->each(function ($combination) {
                $id = $combination->id;
                dispatch(function () use ($id) {
                    Combination::find($id)->setUpcoming();
                })->onQueue('high');
            });
    }
}
