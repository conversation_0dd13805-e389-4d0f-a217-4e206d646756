<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Bag;

class SendAbandonedCartEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:sendAbandonedCart';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sends an email every four hours to all abandoned carts';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        l('Abandoned Cart Email');
        Bag::SendAbandonedEmails();
    }
}
