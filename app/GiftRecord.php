<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class GiftRecord extends Model
{
    protected $guarded = [];

    public static function findRecord($record)
    {
        return static::where('last_name', '=', $record['last_name'])
            ->where('product_id', '=', $record['id'])
            ->whereDate('date', now()->parse($record['date']))
            ->first();
    }

    public static function saveFromOrder($order)
    {
        foreach ($order->products as $product) {
            if (data_get($product, 'gift')) {
                $gift = $product['gift'];
                GiftRecord::firstOrCreate([
                    'date' => now()->parse($gift['date']),
                    'last_name' => $gift['last_name'],
                    'product_id' => $product['product_id'],
                ]);
            }
        }
    }

    public static function removeFromOrder($order)
    {
        foreach ($order->products as $product) {
            if (data_get($product, 'gift')) {
                $gift = $product['gift'];
                GiftRecord::where([
                    'date' => now()->parse($gift['date']),
                    'last_name' => $gift['last_name'],
                    'product_id' => $product['product_id'],
                ])->first()->delete();
            }
        }
    }
}
