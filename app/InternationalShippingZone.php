<?php

namespace App;

use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;

class InternationalShippingZone extends Model
{
    public $casts = [
        'carrier_info' => 'array',
        'service_info' => 'array',
    ];

    public function shippingOptions()
    {
        return $this->belongsToMany(ShippingOption::class);
    }

    public function active()
    {
        return $this->options->map(function ($option) {
            return $option->active;
        })->contains(true);
    }

    public function countrys()
    {
        return $this->hasMany('App\Country');
    }

    public function options()
    {
        return $this->hasMany(InternationalShippingOptionShippingZone::class);
    }


    public function setCountriesTextAttribute($value)
    {
        Cache::tags('countries')->flush();

        $this->attributes['countries_text'] = $value;
    }

    public function getPriceDurationAttribute()
    {
        return $this->options;
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($zone) {
            $priceDuration = request()->price_duration;
            if (!is_array($priceDuration)) {
                return;
            }
            $collection = collect(array_map('forceArray', $priceDuration));

            $collection->each(function ($price) use ($zone) {
                $price = forceArray($price);
                if ($item = $zone->options()->where(['shipping_option_id' => $price['shipping_option_id']])->first()) {
                    $item->update([
                        'shipping_option_id' => $price['shipping_option_id'],
                        'carrier_info' => $price['carrier_info'],
                        'carrier' => data_get($price, 'carrier_info.label'),
                        'service' => data_get($price, 'service_info.label'),
                        'service_info' => $price['service_info'],
                        'base_rate' => $price['base_rate'],
                        'free_shipping_above' => $price['free_shipping_above'],
                        'active' => $price['active'],
                    ]);
                } else {
                    $zone->options()->create([
                        'shipping_option_id' => $price['shipping_option_id'],
                        'carrier_info' => json_encode($price['carrier_info']),
                        'carrier' => data_get($price, 'carrier_info.label'),
                        'service' => data_get($price, 'service_info.label'),
                        'service_info' => json_encode($price['service_info']),
                        'base_rate' => $price['base_rate'],
                        'free_shipping_above' => $price['free_shipping_above'],
                        'active' => $price['active'],
                    ]);
                }
            });

            $zone->options()
                ->whereNotIn('shipping_option_id', $collection->pluck('shipping_option_id'))
                ->delete();
        });
    }

    public function setPriceDurationAttribute()
    {
    }
}
