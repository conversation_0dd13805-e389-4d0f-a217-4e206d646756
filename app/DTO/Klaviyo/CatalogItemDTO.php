<?php

namespace App\DTO\Klaviyo;

use <PERSON><PERSON>\LaravelData\Data;

class CatalogItemDTO extends Data
{
    public string $integration_type = '$custom';
    public string $catalog_type = '$default';

    public function __construct(
        public string $id,
        public string $title,
        public float $price,
        public string $description,
        public string $url,
        public ?string $image_full_url = null,
        public ?string $image_thumbnail_url = null,
        public ?array $images = null,
        public ?array $custom_metadata = null,
        public ?array $categories = null,
        public ?bool $published = null,
    ) {}

    public function toApiItem(bool $includeId = false, bool $includeExternalId = true): array
    {
        $item = [
            'type' => 'catalog-item',
            'attributes' => array_filter([
                'title' => $this->title,
                'price' => $this->price,
                'description' => $this->description,
                'url' => $this->url,
                'image_full_url' => $this->image_full_url,
                'image_thumbnail_url' => $this->image_thumbnail_url,
                'images' => $this->images,
                'custom_metadata' => $this->custom_metadata,
                'published' => $this->published,
            ], fn($value) => $value !== null),
        ];

        if ($includeExternalId) {
            $item['attributes']['external_id'] = 'product-' . $this->id;
        }

        if ($includeId) {
            $item['id'] = $this->integration_type . ':::' . $this->catalog_type . ':::product-' . $this->id;
        }

        if ($this->categories && count($this->categories) > 0) {
            $item['relationships']['categories'] = [
                'data' => array_map(fn($categoryId) => [
                    'type' => 'catalog-category',
                    'id' => '$custom:::$default:::category-' . $categoryId,
                ], $this->categories),
            ];
        }

        return $item;
    }
}
