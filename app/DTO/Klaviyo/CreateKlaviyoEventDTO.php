<?php

namespace App\DTO\Klaviyo;

use <PERSON><PERSON>\LaravelData\Data;

class CreateKlaviyoEventDTO extends Data
{

    public function __construct(
        public string $metric_name,
        public string $profile_email,
        public array $properties,
    )
    {
    }

    public function toApiPayload(): array
    {
        return [
            'data' => [
                'type' => 'event',
                'attributes' => [
                    'properties' => $this->properties,
                    'metric' => [
                        'data' => [
                            'type' => 'metric',
                            'attributes' => [
                                'name' => $this->metric_name,
                            ],
                        ],
                    ],
                    'profile' => [
                        'data' => [
                            'type' => 'profile',
                            'attributes' => [
                                'email' => $this->profile_email
                            ],
                        ],
                    ],
                    'time' => now()->toIso8601String(),
                ],
            ],
        ];
    }
}
