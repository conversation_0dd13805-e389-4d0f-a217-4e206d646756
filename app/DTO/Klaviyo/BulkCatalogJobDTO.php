<?php

namespace App\DTO\Klaviyo;

use App\Enums\Klaviyo\CatalogJobType;
use Spa<PERSON>\LaravelData\Data;

class BulkCatalogJobDTO extends Data
{
    /**
     * @param CatalogItemDTO[] $items
     */
    public function __construct(
        public array $items,
        public CatalogJobType $jobType,
    ) {}

    public function toApiPayload(): array
    {
        $includeId = $this->jobType === CatalogJobType::BulkUpdate;

        return [
            'data' => [
                'type' => $this->jobType,
                'attributes' => [
                    'items' => [
                        'data' => array_map(
                            fn(CatalogItemDTO $item) => $item->toApiItem($includeId),
                            $this->items
                        ),
                    ],
                ],
            ],
        ];
    }
}
