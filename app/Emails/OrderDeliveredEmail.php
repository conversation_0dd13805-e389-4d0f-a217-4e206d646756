<?php

namespace App\Emails;

class OrderDeliveredEmail extends Email
{
    public function getEmailId()
    {
        return '9ad388a4-f7fa-412a-893a-f5b6efa46dfe';
    }

    public function variables($user, $data)
    {
        $order = SetValues::SetOrderValues($data['order']);

        return [
                'order' => $order,
                'type' => 'delivered',
            ] + SetValues::SetEmailValues();
    }
}
