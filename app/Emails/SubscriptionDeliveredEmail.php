<?php

namespace App\Emails;

class SubscriptionDeliveredEmail extends Email
{
    public function getEmailId()
    {
        return 'b1fb0ae4-de28-4f96-afd9-24a7584f0af5';
    }

    public function variables($user, $data)
    {
        $order = SetValues::SetOrderValues($data['order']);

        return [
                'order' => $order,
                'type' => 'delivered',
                'update' => env('APP_URL') . '/account/subscriptions'
            ] + SetValues::SetEmailValues();
    }
}
