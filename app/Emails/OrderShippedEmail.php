<?php

namespace App\Emails;

class OrderShippedEmail extends Email
{
    public function getEmailId()
    {
        return 'de9f7d8e-da4a-48cf-a1d9-77df9d0243c3';
    }

    public function variables($user, $data)
    {
        $order = SetValues::SetOrderValues($data['order']);

        return [
                'order' => $order,
                'type' => 'shipped',
            ] + SetValues::SetEmailValues();
    }
}
