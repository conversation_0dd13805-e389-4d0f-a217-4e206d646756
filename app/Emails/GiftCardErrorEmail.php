<?php

namespace App\Emails;

class GiftCardErrorEmail extends Email
{
    public function getEmailId()
    {
        return '5f81df3a-e675-4a58-9a85-e42cc679f773';
    }

    public function variables($user, $data)
    {
        return [
                'reason' => $data['reason'],
                'gift_card' => $data['gift_card'],
                'icon' => env('APP_URL') . "/img/eichlers-logo-icon.png",
            ] + SetValues::SetEmailValues();
    }
}
