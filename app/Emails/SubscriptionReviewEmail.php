<?php

namespace App\Emails;

class SubscriptionReviewEmail extends Email
{
    public function getEmailId()
    {
        return '00e27981-62de-431b-9d5f-8b9b5aba8d98';
    }

    public function variables($user, $data)
    {
        $subscription = SetValues::SetSubscriptionValues($data['subscription'], $data['products']);

        return [
                'subscription' => $subscription,
                'type' => 'review',
                'update' => env('APP_URL') . '/account/subscriptions'
            ] + SetValues::SetEmailValues();
    }
}
