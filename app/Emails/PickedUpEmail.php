<?php

namespace App\Emails;

use Carbon\Carbon;

class PickedUpEmail extends Email
{
    public function getEmailId()
    {
        return 'f48d4858-88a7-4505-99b1-5660b1b6f42b';
    }

    public function variables($user, $data)
    {
        $order = SetValues::SetOrderValues($data['order']);
        $order->arrival = Carbon::parse($order->arrival)->calendar();

        return [
                'order' => $order,
                'type' => 'pickedUp',
            ] + SetValues::SetEmailValues();
    }
}
