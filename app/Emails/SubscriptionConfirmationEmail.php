<?php

namespace App\Emails;

class SubscriptionConfirmationEmail extends Email
{
    public function getEmailId()
    {
        return '2250eda2-fa46-46ac-ab77-a4fa3ca73a90';
    }

    public function variables($user, $data)
    {
        $subscription = SetValues::setSubscriptionValues($data['subscription'], collect($data['products']));

        return [
                'subscription' => $subscription,
                'type' => 'confirmation',
                'update' => env('APP_URL') . '/account/subscriptions'
            ] + SetValues::SetEmailValues();
    }
}
