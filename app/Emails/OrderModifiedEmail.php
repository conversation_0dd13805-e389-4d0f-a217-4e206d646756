<?php

namespace App\Emails;

class OrderModifiedEmail extends Email
{
    public function getEmailId()
    {
        return '071ff338-aced-4963-a31a-884849656a17';
    }

    public function variables($user, $data)
    {
        $order = SetValues::SetOrderModifiedValues($data['order'], $data['reversion'], $data['message']);

        return [
                'order' => $order,
            ] + SetValues::SetEmailValues();
    }
}
