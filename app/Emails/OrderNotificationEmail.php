<?php

namespace App\Emails;

class OrderNotificationEmail extends Email
{
    public function getEmailId()
    {
        return '9b624a52-4fc7-4240-a0ff-a0c9ccd153c2';
    }

    public function variables($user, $data)
    {
        $order = SetValues::SetOrderValues($data['order']);

        return [
                'order' => $order,
                'admin' => true,
                'type' => 'confirmation',
            ] + SetValues::SetEmailValues();
    }
}
