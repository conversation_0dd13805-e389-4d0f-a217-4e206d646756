<?php

namespace App\Emails;

class ReturnSummeryEmail extends Email
{
    public function getEmailId()
    {
        return '59af0402-5bb4-441f-a1cf-7cb661ba4ff9';
    }

    public function variables($user, $data)
    {
        $return = SetValues::SetReturnValues($data['return'], false);

        return [
                'return' => $return,
                'type' => 'summary',
            ] + SetValues::SetEmailValues();
    }
}
