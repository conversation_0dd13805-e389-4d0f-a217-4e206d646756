<?php

namespace App\Emails;

class OrderConfirmationEmail extends Email
{
    public function getEmailId()
    {
        return 'f9b846a3-277a-4b51-8cb6-1b07190ade5a';
    }

    public function variables($user, $data)
    {
        $order = SetValues::SetOrderValues($data['order']);

        return [
                'order' => $order,
                'type' => 'confirmation',
            ] + SetValues::SetEmailValues();
    }
}
