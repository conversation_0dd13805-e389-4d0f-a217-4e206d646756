<?php

namespace App\Emails;

use CS_REST_Transactional_Timeline;

class MessageDetails
{

    function details($message_id)
    {
        return forceArray(
            json_encode(
                (new CS_REST_Transactional_Timeline(
                    $auth_details = array('api_key' => env('COMPAIGN_MONITOR'))
                ))
                    ->details($message_id, true)
            )
        );
    }


}
