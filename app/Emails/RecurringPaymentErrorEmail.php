<?php

namespace App\Emails;

class RecurringPaymentErrorEmail extends Email
{
    public function getEmailId()
    {
        return 'b0c0a43b-c476-40b3-8f18-28a8305d7a9c';
    }

    public function variables($user, $data)
    {
        $subscription = SetValues::setRecurringValues($data['subscription']);

        return [
                'subscription' => $subscription,
                'type' => 'declined',
            ] + SetValues::SetEmailValues();
    }
}
