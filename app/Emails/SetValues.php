<?php

namespace App\Emails;

use App\Http\Controllers\Api\ShippingController;
use App\OrderRoute;

class SetValues
{
    public static function setAbandonedCartValues($products)
    {
        return collect($products)->map(function ($p) {
            $p['link'] = env('APP_URL') . $p['path'] . '?utm_source=abandonedcartemail&utm_campaign=abandonedcartemail';

            if (data_get($p, 'type') == 'variation') {
                $p['variations'] = implode(
                    ' | ',
                    collect(json_decode($p['meta']))->map(function ($item, $key) {
                        return $key . ': ' . $item;
                    })->values()->all()
                );
            }
            if (data_get($p, 'item_type') != 'giftCard' && data_get($p, 'item_type') == 'digital') {
                $p['download'] = env('APP_URL') . '/api' . $p['path'] . "/download";
            }

            if (data_get($p, 'gift_options.price')) {
                $p['gift_options->price'] = number_format(data_get($p, 'gift_optins.price'), 2) ?? '0.00';
            }

            $p['price'] = number_format($p['price'], 2);

            return $p;
        });
    }

    public static function SetOrderValues($order)
    {
        $order->products = collect($order->products)->map(function ($p) use ($order) {
            $p['link'] = env('APP_URL') . $p['path'];

            if (data_get($p, 'type') == 'variation') {
                $p['variations'] = implode(
                    ' | ',
                    collect(json_decode($p['meta']))->map(function ($item, $key) {
                        return $key . ': ' . $item;
                    })->values()->all()
                );
            }
            if (data_get($p, 'item_type') != 'giftCard' && data_get($p, 'item_type') == 'digital') {
                $p['download'] = env('APP_URL') . '/api' . $p['path'] . "/download?token=" . encrypt($order->id);
            }

            if (data_get($p, 'gift_options.price')) {
                $p['gift_options->price'] = number_format(data_get($p, 'gift_optins.price'), 2) ?? '0.00';
            }

            $p['price'] = number_format($p['price'], 2);

            return $p;
        });

        $giftCards = collect($order->payments['giftCard'])->map(function ($giftCard) {
            $giftCard['amount'] = number_format($giftCard['amount'], 2) ?? 0.00;
            return $giftCard;
        });

        $order->cards = $giftCards;

        $order->sub_total = number_format($order->sub_total, 2);
        $order->shipping_amount = number_format($order->shipping_amount, 2);
        $order->tax_amount = number_format($order->tax_amount, 2);
        $order->grand_total = number_format($order->charge, 2);
        $order->discount_amount = $order->discount_amount ? number_format($order->discount_amount, 2) : null;
        $order->personal_amount = $order->total_personalize ? number_format($order->total_personalize, 2) : null;
        $order->gift_options_amount = $order->total_gift_options ? number_format($order->total_gift_options, 2) : null;

        $order->arrival = data_get($order, 'shipping.tracking.arrival_date')
            ? now()->parse(data_get($order, 'shipping.tracking.arrival_date'))->format('D, M d, Y')
            : now()->parse(data_get($order, 'shipping.shippingType.estimated_arrival'))->format('D, M d, Y');

        if ($route_order = OrderRoute::firstWhere('order_id', $order->id)) {
            if ($estimated_arrival = data_get($route_order, 'meta.estimated_arrival')) {
                $start = now()->parse($estimated_arrival)->format('h:i a');
                $end = now()->parse($estimated_arrival)->addHours(2)->format('h:i a');
                $start = $start['0'] == 0 ? substr($start, 1) : $start;
                $end = $end['0'] == 0 ? substr($end, 1) : $end;
                $order->arrival = 'Today ' . $start . ' - ' . $end;
            }
        }

        $order->arrived = now()
            ->parse(data_get($order, 'shipping.tracking.arrived_at'))
            ->timezone('America/New_York')
            ->format('D, M d, Y');

        $order->viewOrder = $order->guest ? env(
                'APP_URL'
            ) . '/orders/guest/' . $order->id . '/status?email=' . $order->customer->email
            : env('APP_URL') . '/account/orders/' . $order->id;

        $order->adminView = env('APP_URL') . "/admin/resources/orders/" . $order->id . "/edit";
        $order->viewCart = env('APP_URL') . "/bag";

        return $order;
    }


    public static function setSubscriptionValues($subscription, $products)
    {
        $products = $products->map(function ($p) {
            if (data_get($p, 'type') == 'variation') {
                $p['variations'] = implode(
                    ' | ',
                    collect(json_decode($p['meta']))->map(function ($item, $key) {
                        return $key . ': ' . $item;
                    })->values()->all()
                );
            }
            $p['quantity'] = $p['quantity'] ?? 1;
            $p['price'] = number_format(data_get($p, 'price'), 2);
            $p['link'] = env('APP_URL') . $p['path'];
            return $p;
        });
        $tax_rate = session()->get('zip_code')['tax_rate']
            ?? settings()->getValue('default_tax_rate')
            ?? 0;
        $sub_total = $products->pluck('price')->sum();
        $tax_amount = $sub_total * ($tax_rate / 100);
        return [
            'products' => $products,
            'id' => $subscription->id,
            'ship' => now()->addDays(4),
            'product' => $products->first(),
            'name' => $subscription->subscriptionGroup->name,
            'customer' => $subscription->customer->only(['name']),
            'subscriptiontype' => $subscription->subscriptionType->only(['name']),
            'update' => env('APP_URL') . '/account/subscriptions',
            'from_price' => number_format($subscription->from, 2),
            'from_price' => number_format($subscription->from, 2),
            'city' => data_get($subscription, 'shipping.city'),
            'state' => data_get($subscription, 'shipping.state'),
            'card' => data_get($subscription, 'payments.last_four'),
            'paid_with' => data_get($subscription, 'payments.type'),
            'date' => data_get($subscription->firstProducts, 'date'),
            'postal_code' => data_get($subscription, 'shipping.postal_code'),
            'address_line_2' => data_get($subscription, 'shipping.address_line_2'),
            'address_line_1' => data_get($subscription, 'shipping.address_line_1'),
            'subscriptiontype' => ['name' => $subscription->subscriptionType->name],
            'sub_total' => number_format($sub_total, 2),
            'tax_amount' => number_format($tax_amount, 2),
            'grand_total' => number_format($sub_total + $tax_amount, 2),
        ];
    }


    public static function setRecurringValues($subscription)
    {
        $p = collect($subscription->orders->last()->products)->first();

        // $p['product']['link'] = env('APP_URL');//.$p['product']['path'];

        if (data_get($p, 'type') == 'variation') {
            $p['variations'] = implode(
                ' | ',
                collect(json_decode($p['meta']))->map(function ($item, $key) {
                    return $key . ': ' . $item;
                })->values()->all()
            );
        }

        $p['quantity'] = $p['quantity'] ?? 1;

        $p['price'] = number_format(data_get($p, 'price'), 2);

        $p['link'] = env('APP_URL') . $p['path'];


        $subscription->product = $p;

        $subscription->customer = $subscription->customer;
        $subscription->address = $subscription->address;

        $subscription->setting_name = $subscription->recurringSetting->name;

        $tax_rate = session()->get('zip_code')['tax_rate'] ?? settings()->getValue('default_tax_rate') ?? 0;

        $subscription->sub_total = number_format(data_get($subscription->product, 'price'), 2);

        $subscription->tax_amount = number_format(
            ($subscription->sub_total * (($tax_rate / 100) + 1)) - $subscription->sub_total,
            2
        );
        $subscription->grand_total = number_format($subscription->sub_total + $subscription->tax_amount, 2);

        $subscription->update = env('APP_URL') . '/account/subscriptions';

        $options = (new ShippingController)->rates(request()->merge([
            'country' => data_get($subscription, 'address.country') ?? settings()->getValue('store_country'),
            'zip' => data_get($subscription, 'address.postal_code') ?? settings()->getValue('store_postal_code'),
            'products' => collect([$subscription->product])
        ]))->where('delivery', true)->sortByDesc('days')->first();
        $shipping_days = data_get($options, 'days');

        $subscription->ship = $subscription->next_date->format('D, M d, Y');
        $subscription->date = $subscription->next_date->addDays($shipping_days)->format('D, M d, Y');

        $subscription->card = $subscription->payment->last_four;
        $subscription->paid_with = $subscription->payment->type;

        return $subscription;
    }


    public static function SetReturnValues($return, $confirmation)
    {
        $products = collect($return->products);

        if ($confirmation) {
            $collection = collect();
            $products = $products->map(function ($product) use (&$collection, &$products) {
                $p = $collection->where('id', $product['id'])
                    ->where('type', $product['type'])
                    ->where('refund_total', $product['refund_total'])
                    ->where('refund', $product['refund'])
                    ->first();

                if (!$p) {
                    $product['quantity'] = $products->where('id', $product['id'])
                        ->where('type', $product['type'])
                        ->where('refund_total', $product['refund_total'])
                        ->where('refund', $product['refund'])
                        ->count();
                    $product['price'] = $product['refund_total'] * $product['quantity'];

                    $collection->push($product);

                    return $product;
                }
            })->filter()->values();
        }

        $products = collect($products)->map(function ($p) use ($products) {
            if (data_get($p, 'type') == 'variation') {
                $p['variations'] = implode(
                    ' | ',
                    collect(json_decode($p['meta']))->map(function ($item, $key) {
                        return $key . ': ' . $item;
                    })->values()->all()
                );
            }

            $p['price'] = number_format(data_get($p, 'price'), 2);

            $p['link'] = env('APP_URL') . $p['path'];

            return $p;
        });
        $return->products = $products;

        $giftCards = collect(data_get($return->payments, 'giftCard'))->where('amount')->map(function ($giftCard) {
            $giftCard['amount'] = number_format(data_get($giftCard, 'amount'), 2);
            return $giftCard;
        });
        $return->cards = $giftCards;

        if (data_get($return->payments, 'giftCard.eGift')) {
            $return->eGift = data_get($return->payments, 'giftCard.eGift');
        }

        $return->sub_total = number_format($return->sub_total, 2);
        $return->tax_amount = number_format($return->tax_amount, 2);
        $return->grand_total = number_format($return->grand_total, 2);

        $return->viewReturn = env('APP_URL') . "/account/orders";

        $return->valid_til = $return->valid_until;

        return $return;
    }


    public static function SetOrderModifiedValues($order, $reversion, $message)
    {
        $order->message = $message;
        $order->productsets = collect([$reversion['canceled_products'], $reversion['added_products'], $order->products]
        )->map(function ($products, $index) {
            if ($index == 0) {
                $message = 'We cancelled these items';
            } elseif ($index == 1) {
                $message = 'We added these items';
            } else {
                $message = 'This is your final order';
            }
            if (collect($products)->count()) {
                return [
                    'message' => $message,
                    'products' => collect($products)->map(function ($p) {
                        if (data_get($p, 'path')) {
                            $p['link'] = env('APP_URL') . $p['path'];
                        }

                        if (data_get($p, 'type') == 'variation') {
                            $p['variations'] = implode(
                                ' | ',
                                collect(json_decode($p['meta']))->map(function ($item, $key) {
                                    return $key . ': ' . $item;
                                })->values()->all()
                            );
                        }
                        if (data_get($p, 'changeReason')) {
                            $p['reason'] = data_get($p, 'changeReason');
                        }
                        if (data_get($p, 'item_type') != 'giftCard' && data_get(
                                $p,
                                'item_type'
                            ) == 'digital' && data_get($p, 'path')) {
                            $p['download'] = env('APP_URL') . '/api' . $p['path'] . "/download";
                        }

                        if (data_get($p, 'gift_options.price')) {
                            $p['gift_options->price'] = number_format(data_get($p, 'gift_optins.price'), 2) ?? '0.00';
                        }

                        $p['price'] = number_format($p['price'], 2);

                        return $p;
                    })
                ];
            }
        })->filter();


        $giftCards = collect($order->payments['giftCard'])->map(function ($giftCard) {
            $giftCard['amount'] = number_format($giftCard['amount'], 2) ?? 0.00;
            return $giftCard;
        });

        $order->cards = $giftCards;

        $order->sub_total = number_format($order->sub_total, 2);
        $order->shipping_amount = number_format($order->shipping_amount, 2);
        $order->tax_amount = number_format($order->tax_amount, 2);
        $order->grand_total = number_format($order->charge, 2);
        $order->discount_amount = $order->discount_amount ? number_format($order->discount_amount, 2) : null;

        $order->arrival = data_get($order, 'shipping.tracking.arrival_date')
            ? now()->parse(data_get($order, 'shipping.tracking.arrival_date'))
            : now()->parse(data_get($order, 'shipping.shippingType.estimated_arrival'));

        $order->viewOrder = $order->guest ? env(
                'APP_URL'
            ) . '/orders/guest/' . $order->id . '/status?email=' . $order->customer->email
            : env('APP_URL') . '/account/orders/' . $order->id;

        $order->viewCart = env('APP_URL') . "/bag";

        return $order;
    }


    public static function SetEmailValues()
    {
        return [
            'help' => [
                'number' => settings()->getValue('phone'),
                'email' => settings()->getValue('email')
            ],
            'url' => env('APP_URL'),
            'image' => env('APP_URL') . "/img/eichlers-logo.png",
            'chat' => env('APP_URL') . "?chat=true",
            'year' => date('Y'),
        ];
    }
}
