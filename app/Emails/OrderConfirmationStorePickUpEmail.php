<?php

namespace App\Emails;

use Carbon\Carbon;

class OrderConfirmationStorePickUpEmail extends Email
{
    public function getEmailId()
    {
        return '3278449e-2bcb-4b7b-8f0a-4ef071daf2bb';
    }

    public function variables($user, $data)
    {
        $order = SetValues::SetOrderValues($data['order']);
        $order->arrival = Carbon::parse($order->arrival)->calendar();

        return [
                'order' => $order,
                'type' => 'storePickup',
                'store' => [
                    'name' => settings()->getValue('store_name'),
                    'address_line_1' => settings()->getValue('store_address_line_1'),
                    'city' => settings()->getValue('store_city'),
                    'state' => settings()->getValue('store_state'),
                    'postal_code' => settings()->getValue('store_postal_code')
                ],
                'pickup' => data_get($order, 'shipping.shippingType.date'),
            ] + SetValues::SetEmailValues();
    }
}
