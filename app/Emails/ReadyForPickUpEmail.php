<?php

namespace App\Emails;

use App\ClosedDay;
use App\ShippingOption;

class ReadyForPickUpEmail extends Email
{
    public function getEmailId()
    {
        return '72ddb776-afcb-45f5-8c0d-bcf217ba99b7';
    }

    public function variables($user, $data)
    {
        $order = SetValues::SetOrderValues($data['order']);

        return [
                'order' => $order,
                'type' => 'ready',
                'expire' => $this->expire(),
                'store' => [
                    'name' => settings()->getValue('store_name'),
                    'address_line_1' => settings()->getValue('store_address_line_1'),
                    'city' => settings()->getValue('store_city'),
                    'state' => settings()->getValue('store_state'),
                    'postal_code' => settings()->getValue('store_postal_code')
                ],
            ] + SetValues::SetEmailValues();
    }

    public function expire()
    {
        $shippingOption = ShippingOption::firstWhere('delivery', false);
        $a = 0;
        $now = now()->addHours(settings()->getValue('how_many_hours_we_keep_store_pickup_orders'));

        while ($a <= 10) {
            if (
                ClosedDay::whereDate('date', $now)->first()
                || !collect(explode(',', $shippingOption->operational_days))->contains($now->dayOfWeek)
            ) {
                $now = $now->addDay();
                $a++;
            } else {
                break;
            }
        }
        return $now->format('D, M d, Y');
    }
}
