<?php

namespace App\Emails;

class LinkedProductNotification extends Email
{
    public function getEmailId()
    {
        return '071ff338-aced-4963-a31a-884849656a18';
    }

    public function variables($user, $data)
    {
        $customer = $data['customer'];
        $product = $data['product'];
        return [
                'customer' => $customer,
                'product' => $product,
            ] + SetValues::SetEmailValues();
    }
}
