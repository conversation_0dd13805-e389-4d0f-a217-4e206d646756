<?php

namespace App\Emails;

use App\Product;

class FollowCreatorEmail extends Email
{
    public function getEmailId()
    {
        return 'e243894c-9f55-4340-9fa0-b98f111f6343';
    }

    public function variables($user, $data)
    {
        $product = $data['product'];
        $following = $data['following'];

        $product = Product::find(data_get($product, 'id'));

        $picture = data_get($product, 'media_urls.0.large');

        $creators = optional($product->creators)->map(function ($creator) {
            return $creator->name;
        });

        if ($following && data_get(collect($following)->first(), 'media_urls.0') && collect($following)->count() == 1) {
            $image = data_get(collect($following)->first(), 'media_urls.0');
        } else {
            $image = null;
        }

        $following_text = "";

        if($following) {
            collect($following)->each(function ($creator, $index) use (&$following_text, $creators, $following) {
                if (collect($following)->count() - $index == 1 && collect($following)->count() > 1) {
                    $following_text .= 'and ';
                }
                $following_text .= $creator->name;
                if (collect($following)->count() - $index > 0) {
                    $following_text .= ", ";
                }
            });
        }

        $creator_text = "";

        collect($creators)->each(function ($creator, $index) use (&$creator_text, $creators) {
            if ($creators->count() - $index == 1 && $creators->count() > 1) {
                $creator_text .= 'and ';
            }
            $creator_text .= strtoupper($creator);
            if ($creators->count() - $index > 0) {
                $creator_text .= ", ";
            }
        });

        return [
                'picture' => $picture,
                'product' => $product,
                'following' => $following,
                'creator_name' => $following ? collect($following)->first()->name : null,
                'following_image' => $image,
                'following_text' => $following_text,
                'product_link' => env('APP_URL') . $product->path,
                'product_name' => strtoupper($product->title),
                'price' => $product->from_price ? "From $" . number_format(
                        $product->from_price,
                        2
                    ) : "$" . number_format($product->price, 2),
                'creator_text' => $creator_text,
            ] + SetValues::SetEmailValues();
    }
}
