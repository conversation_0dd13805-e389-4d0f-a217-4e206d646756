<?php

namespace App\Emails;

use CS_REST_Transactional_SmartEmail;

abstract class Email
{
    protected $apiKey;

    protected $data = [];

    public function __construct()
    {
        $this->apiKey = env('COMPAIGN_MONITOR');
    }

    public function withData($data)
    {
        $this->data = $data;

        return $this;
    }

    public function sendTo($user)
    {
        $mailer = $this->newTransaction();

        $data = call_user_func_array(
            [$this, 'variables'],
            array_merge(compact('user'), $this->data)
        );

        return $mailer->send([
            'To' => data_get($user, 'email'),
            'Data' => $data
        ], 'Yes');
    }

    public function newTransaction()
    {
        return new CS_REST_Transactional_SmartEmail(
            $this->getEmailId(),
            ['api_key' => $this->apiKey]
        );
    }

    abstract protected function getEmailId();
}
