<?php

namespace App\Emails;

use App\Bag;

class AbandonedCartEmail extends Email
{
    public function getEmailId()
    {
        return 'ead7c060-8ef8-49a0-b1c0-4c20c1e7ac65';
    }

    public function variables($user, $data)
    {
        $products = makeForFronEnd(Bag::find($data['bag_ids']));

        $products = SetValues::setAbandonedCartValues($products);

        return [
                'products' => $products,
                'viewBag' => env('APP_URL') . "/bag?utm_source=abandonedcartemail&utm_campaign=abandonedcartemail",
            ] + SetValues::SetEmailValues();
    }
}
