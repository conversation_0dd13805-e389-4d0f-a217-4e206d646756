<?php

namespace App\Emails;

class OrderCancelledEmail extends Email
{
    public function getEmailId()
    {
        return '6e41b48d-e9c4-4ce1-bd86-270bc7e9d0f5';
    }

    public function variables($user, $data)
    {
        $order = SetValues::SetOrderValues($data['order']);
        $order->products = collect($order->products)->where('status', 'cancelled')->values();

        return [
                'order' => $order,
                'type' => 'cancelled',
            ] + SetValues::SetEmailValues();
    }
}
