<?php

namespace App\Emails;

class OrderNotificationStorePickupEmail extends Email
{
    public function getEmailId()
    {
        return '7653bd3c-e4cf-4358-a5eb-59721aa6dc17';
    }

    public function variables($user, $data)
    {
        $order = SetValues::SetOrderValues($data['order']);

        return [
                'order' => $order,
                'admin' => true,
                'type' => 'storePickup',
            ] + SetValues::SetEmailValues();
    }
}
