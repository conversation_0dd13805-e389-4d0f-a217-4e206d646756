<?php

namespace App\Emails;

class PaymentErrorSubscriptionEmail extends Email
{
    public function getEmailId()
    {
        return '573384af-d7fe-4c65-a8ae-32354059fa8f';
    }

    public function variables($user, $data)
    {
        $order = SetValues::SetOrderValues($data['order']);

        return [
                'order' => $order,
                'type' => 'declined',
                'update' => env('APP_URL') . '/account/subscriptions'
            ] + SetValues::SetEmailValues();
    }
}
