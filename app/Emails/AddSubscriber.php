<?php

namespace App\Emails;

use CS_REST_Subscribers;

class AddSubscriber
{
    public $_subscribers_base_route;

    public function add($subscriber)
    {
        return $this->newTransaction()->add($subscriber);
    }

    public function get($email)
    {
        return $this->newTransaction()->get($email);
    }

    public function delete($email)
    {
        return $this->newTransaction()->delete($email);
    }

    public function unsubscribe($email)
    {
        return $this->newTransaction()->unsubscribe($email);
    }

    public function newTransaction()
    {
        return new CS_REST_Subscribers(
            $list_id = '2f7c46cdd56074fc309b4dbb999d00a5',//env('COMPAIGN_MONITOR_LIST_ID');
            $auth_details = array('api_key' => env('COMPAIGN_MONITOR'))
        );
    }
}
