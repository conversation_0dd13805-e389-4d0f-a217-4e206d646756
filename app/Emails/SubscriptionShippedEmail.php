<?php

namespace App\Emails;

class SubscriptionShippedEmail extends Email
{
    public function getEmailId()
    {
        return '55f09167-29ce-4e27-b3f3-fea838429798';
    }

    public function variables($user, $data)
    {
        $order = SetValues::SetOrderValues($data['order']);

        return [
                'order' => $order,
                'type' => 'shipped',
                'update' => env('APP_URL') . '/account/subscriptions'
            ] + SetValues::SetEmailValues();
    }
}
