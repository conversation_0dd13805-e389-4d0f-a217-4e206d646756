<?php

namespace App\Emails;

class RecurringEmail extends Email
{
    public function getEmailId()
    {
        return 'cd7ac958-a54e-4c7e-ac26-32f29c556af7';
    }

    public function variables($user, $data)
    {
        $subscription = SetValues::setRecurringValues($data['subscription']);

        return [
                'subscription' => $subscription,
                'type' => 'confirmation',
            ] + SetValues::SetEmailValues();
    }
}
