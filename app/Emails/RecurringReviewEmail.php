<?php

namespace App\Emails;

class RecurringReviewEmail extends Email
{
    public function getEmailId()
    {
        return 'a6db0068-3cc7-4351-85d4-00ecf9ad7232';
    }

    public function variables($user, $data)
    {
        $subscription = SetValues::setRecurringValues($data['subscription']);

        return [
                'subscription' => $subscription,
                'type' => 'review',
            ] + SetValues::SetEmailValues();
    }
}
