<?php

namespace App\Emails;

class PaymentErrorEmail extends Email
{
    public function getEmailId()
    {
        return '472313f7-9ad8-4038-b923-c5a567dbca97';
    }

    public function variables($user, $data)
    {
        $order = SetValues::SetOrderValues($data['order']);

        return [
                'order' => $order,
                'type' => 'declined',
                'viewCart' => env('APP_URL'),                   //??????????????????
            ] + SetValues::SetEmailValues();
    }
}
