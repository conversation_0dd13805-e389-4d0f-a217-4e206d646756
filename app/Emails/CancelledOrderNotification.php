<?php

namespace App\Emails;

class CancelledOrderNotification extends Email
{
    public function getEmailId()
    {
        return '3e852532-1301-437d-92c9-f5ae33d6e8c1';
    }

    public function variables($user, $data)
    {
        $order = SetValues::SetOrderValues($data['order']);
        $order->products = collect($order->products)->where('status', 'cancelled');

        return [
                'order' => $order,
                'admin' => true,
                'type' => 'cancelled',
            ] + SetValues::SetEmailValues();
    }
}


