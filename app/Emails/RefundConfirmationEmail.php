<?php

namespace App\Emails;

class RefundConfirmationEmail extends Email
{
    public function getEmailId()
    {
        return 'e89a7eac-b136-4ad8-a050-d855a766593f';
    }

    public function variables($user, $data)
    {
        $return = SetValues::SetReturnValues($data['return'], true);

        return [
                'return' => $return,
                'type' => 'confirmation',
            ] + SetValues::SetEmailValues();
    }
}
