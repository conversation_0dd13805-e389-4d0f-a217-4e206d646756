<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Address extends Model
{
    protected $guarded = [];

    public function model()
    {
        return $this->morphTo();
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($address) {
            if ($address->default) {
                $address->model->addresses()->whereNotIn('id', [$address->id])->update(['default' => false]);
            }
        });
    }
}
