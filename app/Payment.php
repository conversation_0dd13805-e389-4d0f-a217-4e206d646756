<?php

namespace App;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    protected $guarded = [];

    protected $casts = [
        'expDate' => 'date'
    ];

    protected $appends = [
        'has_subscription'
    ];

    public function model()
    {
        return $this->morphTo();
    }

    public function setExpDateAttribute($value)
    {
        if (preg_match('/^(\d{2}|\d{1})\/(\d{2})$/', $value)) {
            $date = explode('/', $value);
            $this->attributes['expDate'] = now()
                ->createFromDate('20' . $date[1], $date[0])
                ->endOfMonth();
        } else {
            $this->attributes['expDate'] = $value;
        }
    }

    public function getHasSubscriptionAttribute()
    {
        return DB::table('subscriptions')->where('status', 'active')->select('payment_id')->get()->pluck('payment_id')
            ->merge(
                DB::table('recurrings')->where('status', 'active')->select('payment_id')->get()->pluck('payment_id')
            )
            ->unique()
            ->contains($this->id);
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($payment) {
            if ($payment->default) {
                $payment->model->payments()->whereNotIn('id', [$payment->id])->update(['default' => false]);
            }
        });
    }
}
