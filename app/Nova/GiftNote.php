<?php

namespace App\Nova;

use App\Nova\Actions\DownloadGiftNote;
use <PERSON>vel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class GiftNote extends Resource
{
    use OrderFulfiller;

    public static string $model = \App\GiftNote::class;
    public static $title = 'name';
    public static $search = [
        'id',
        'occasionText',
        'order_id',
        'sku',
    ];

    public static function indexQuery(NovaRequest $request, $query)
    {
        if (request('search')) {
            return $query;
        }
        return $query->where('shipped', false)->orWhereNull('shipped');
    }

    public static function applySearch($query, $term)
    {
        $query->where(function ($query) use ($term) {
            collect(self::$search)->each(function ($srch) use ($query, $term) {
                $query->orWhere('gift_notes.' . $srch, 'like', '%' . $term . '%');
            });
        });
        return $query;
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Sku')->showOnCreating()->hideFromIndex(),
            Text::make('Order Id')->showOnCreating()->hideFromIndex(),
            Select::make('Text Direction', 'textDirection')->options([
                'center' => 'Center',
                'ltr' => 'Left To Right',
                'rtl' => 'Right To Left'
            ])->showOnCreating()->hideFromIndex(),

            Textarea::make('Note', 'occasionText')->hideFromIndex()->alwaysShow(),
            BelongsTo::make('GiftNotesSetting')->hideFromIndex(),
            Text::make('Sku')->onlyOnIndex(),
            Text::make('Order Id')->onlyOnIndex(),
            Boolean::make('Downloaded')->onlyOnIndex(),
        ];
    }

    public function actions(NovaRequest $request): array
    {
        return [
            (new DownloadGiftNote)->canRun(function () {
                return true;
            })->exceptOnIndex()
                ->confirmText('Are you sure you want to download this gift note?')
                ->confirmButtonText('Download')
                ->cancelButtonText("Don't download"),
        ];
    }
}
