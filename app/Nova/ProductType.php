<?php

namespace App\Nova;

use Illuminate\Support\Facades\DB;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\KeyValue;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Outl1ne\NovaSimpleRepeatable\SimpleRepeatable;

class ProductType extends Resource
{
    use ProductLister;

    public static string $model = \App\ProductType::class;

    public static function label(): string
    {
        return 'Product Types';
    }

    public static $title = 'name';

    public static $search = [
        'name',
        'fields',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->rules(
                    'required',
                    'regex:/^[A-Za-z0-9]+$/',
                    'unique:product_types,name,{{resourceId}}'
                ),
            Select::make('Creator Type', 'creator_type')
                ->nullable()
                ->options(\App\Creator::$options),

            KeyValue::make('Spec Presets', 'fields')
                ->help('You can add a value to make it the default.')
                ->rules('json'),

            SimpleRepeatable::make('Variations', 'variations',[
                    Text::make('Name')
                        ->rules('required', 'distinct'),
                    Text::make('Explained', 'info')
                ]),

            Text::make('Products', 'id')->resolveUsing(function () {
                return DB::table('products')->where('product_type_id', $this->id)->count();
            })->onlyOnIndex(),
            HasMany::make('Products')
        ];
    }
}
