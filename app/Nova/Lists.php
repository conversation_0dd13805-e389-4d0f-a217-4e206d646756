<?php

namespace App\Nova;

use Capitalc\Currencyfield\Currencyfield;
use Laravel\Nova\Fields\Heading;
use Capitalc\ProductSearch\ProductSearch;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Laravel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class Lists extends Resource
{
    use OrderFulfiller;

    public static string $model = \App\Lists::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'grade',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()
                ->sortable(),

            BelongsTo::make('School')
                ->searchable()
                ->rules('required'),

            Text::make('Grade')
                ->rules('required')
                ->sortable(),

            Text::make('Year')
                ->rules('required')
                ->sortable(),

            Currencyfield::make('Bundle Price', 'price'),
            Images::make('Media'),

            Heading::make('Products Included')->onlyOnForms(),
            ProductSearch::make('Products', 'formatted_products')
                ->onlyOnForms()
                ->withMeta(['showQuantity' => true]),
        ];
    }
}
