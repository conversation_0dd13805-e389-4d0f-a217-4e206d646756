<?php

namespace App\Nova;

use Illuminate\Support\Str;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Setting extends Resource
{
    use SuperAdmin;

    public static string $model = \App\Setting::class;

    public static $globallySearchable = false;

    public static $title = 'key';

    public static $search = [
        'key',
        'value'
    ];

    public static function indexQuery(NovaRequest $request, $query)
    {
        if (request('search')) {
            return $query;
        }
        return $query->where('admin', '!=', true)->orWhere('admin', null);
    }

    public static function applySearch($query, $term)
    {
        $query->where('admin', '!=', true)->orWhere('admin', null);
        $query->where(function ($query) use ($term) {
            collect(self::$search)->each(function ($srch) use ($query, $term) {
                $query->orWhere('settings.' . $srch, 'like', '%' . $term . '%');
            });
        });
        return $query;
    }

    public static function label(): string
    {
        return 'Configurations';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            Text::make('key')
                ->resolveUsing(function ($key) {
                    $str = str_replace('_', ' ', $key);
                    return Str::title($str);
                })
                ->fillUsing(function ($key) {
                    return strtolower(str_replace(' ', '_', $key));
                })
                ->withMeta([
                    'extraAttributes' => [
                        'readonly' => true
                    ]
                ])->sortable(),

            Text::make('value')
                ->help($this->helper),
        ];
    }
}
