<?php

namespace App\Nova\Dashboards;

use Abordage\TotalCard\TotalCard;
use App\Nova\Metrics\OrdersPerDay;
use App\Product;
use Laravel\Nova\Dashboards\Main as Dashboard;
use Akbsit\NovaCardCache\NovaCardCache;

class Main extends Dashboard
{
    /**
     * Get the cards for the dashboard.
     *
     * @return array
     */
    public function cards()
    {
        $user = auth('admin')->user();
        $array = [
            new TotalCard(Product::class, 'Products'),
        ];
        if ($user->role == 'Super Admin' || $user->role == 'Product Lister') {
            array_push(
                $array,
                new \Capitalc\CheckPos\CheckPos(),
                new \Capitalc\CheckGc\CheckGc(),
                NovaCardCache::make(),
                new OrdersPerDay,
            );
        }
        return $array;
    }
}
