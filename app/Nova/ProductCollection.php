<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Fields\Heading;
use Capitalc\Checkbox\Checkbox;
use Capitalc\ProductSearch\ProductSearch;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Laravel\Nova\Http\Requests\NovaRequest;

class ProductCollection extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\ProductCollection>
     */
    public static $model = \App\ProductCollection::class;
    public static $group = 'Products';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
        'description',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->rules('required', 'min:3')
                ->sortable(),

            Textarea::make('Description')
                ->hideFromIndex(),

            Heading::make('Images')->onlyOnForms(),
            Images::make('Media')
                ->croppingConfigs(['ratio' => 4 / 3])
                ->conversionOnIndexView('thumb')
                ->singleImageRules('max:10240')
                ->conversionOnDetailView('thumb'),

            Heading::make('Products')->onlyOnForms(),
            ProductSearch::make('Products', 'formatted_products')
                ->onlyOnForms()
                ->withMeta(['showQuantity' => false]),

            Checkbox::make('Search Query Redirect', 'redirect')
                ->help(
                    "When set as active, search queries that match " . $this->help_name() . " will be redirected to the " . $this->help_name() . " page"
                ),

            Heading::make('SEO')->onlyOnForms(),
            Text::make('SEO Title', 'seo_title')
                ->onlyOnForms(),

            Textarea::make('SEO Description', 'seo_description')
                ->onlyOnForms(),
        ];
    }

    public function help_name(): string
    {
        return $this->name ? "'" . $this->name . "'" : 'collection name';
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
