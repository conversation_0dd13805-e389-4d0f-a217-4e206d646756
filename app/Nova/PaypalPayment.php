<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\MorphOne;
use <PERSON>vel\Nova\Fields\Heading;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class PaypalPayment extends Resource
{
    public static $displayInNavigation = false;

    public static string $model = \App\PaypalPayment::class;

    public static $title = 'id';

    public static $search = [
        'id',
    ];

    public function fields(NovaRequest $request): array
    {
        return array_merge(
            [
                ID::make()->sortable()->onlyOnDetail(),
                DateTime::make('Created At')->displayUsing(function ($date) {
                    return $date ? $date->format('m/d/Y h:i A') : null;
                })->onlyOnDetail(),
                Text::make('Payment Id')->onlyOnDetail(),
                Text::make('Token')->onlyOnDetail(),
                Text::make('O Id')->onlyOnDetail(),
                Text::make('Status')->onlyOnDetail(),
                Text::make('Amount')->resolveUsing(
                    function ($value) {
                        return '$' . data_get($value, 'total');
                    }
                )->onlyOnDetail(),
                Heading::make('Authorization')->onlyOnDetail(),
                Text::make('Id', 'authorization')->resolveUsing(
                    function ($value) {
                        return data_get($value, 'id');
                    }
                )->onlyOnDetail(),
                DateTime::make('Date', 'authorization')->resolveUsing(
                    function ($value) {
                        return data_get($value, 'date');
                    }
                )->displayUsing(function ($date) {
                    return $date ? $date->format('m/d/Y h:i A') : null;
                })->onlyOnDetail(),
                Text::make('Amount', 'authorization')->resolveUsing(
                    function ($value) {
                        return '$' . data_get($value, 'amount.total');
                    }
                )->onlyOnDetail(),
                DateTime::make('Valid Until', 'authorization')->resolveUsing(
                    function ($value) {
                        return data_get($value, 'valid_until');
                    }
                )->displayUsing(function ($date) {
                    return $date ? $date->format('m/d/Y h:i A') : null;
                })->onlyOnDetail(),

            ],
            $this->capturesHead(),
            $this->capturesFields(),
            $this->refundsHead(),
            $this->refundsFields(),
            [
                MorphOne::make('Order')->onlyOnDetail(),

            ]
        );
    }

    public function capturesHead()
    {
        if (!collect(data_get($this->captures, 'captures'))->count()) {
            return [];
        }
        return [
            Heading::make('Captures')->onlyOnDetail(),
        ];
    }

    public function refundsHead()
    {
        if (!collect($this->refunds)->count()) {
            return [];
        }
        return [
            Heading::make('Refunds')->onlyOnDetail(),
        ];
    }


    public function capturesFields()
    {
        return collect(data_get($this->captures, 'captures'))->map(function ($charge) {
            return [
                DateTime::make('Date')->resolveUsing(
                    function ($value) use ($charge) {
                        return data_get($charge, 'date');
                    }
                )->displayUsing(function ($date) {
                    return $date ? $date->format('m/d/Y h:i A') : null;
                })->onlyOnDetail(),
                Text::make('Id')->resolveUsing(
                    function ($value) use ($charge) {
                        return data_get($charge, 'id');
                    }
                )->onlyOnDetail(),
                Text::make('Amount')->resolveUsing(
                    function ($value) use ($charge) {
                        return '$' . data_get($charge, 'amount.total');
                    }
                )->onlyOnDetail(),
                Text::make('Fee')->resolveUsing(
                    function ($value) use ($charge) {
                        return '$' . data_get($charge, 'fees.value');
                    }
                )->onlyOnDetail(),
                Heading::make('')->onlyOnDetail(),
            ];
        })->flatten(1)->toArray();
    }

    public function refundsFields()
    {
        return collect($this->refunds)->map(function ($charge) {
            return [
                Text::make('Id')->resolveUsing(
                    function ($value) use ($charge) {
                        return data_get($charge, 'id');
                    }
                )->onlyOnDetail(),
                DateTime::make('Date')->resolveUsing(
                    function ($value) use ($charge) {
                        return data_get($charge, 'date');
                    }
                )->displayUsing(function ($date) {
                    return $date ? $date->format('m/d/Y h:i A'): null;
                })->onlyOnDetail(),
                Text::make('Amount')->resolveUsing(
                    function ($value) use ($charge) {
                        return '$' . data_get($charge, 'amount.total');
                    }
                )->onlyOnDetail(),
                Heading::make('')->onlyOnDetail(),
            ];
        })->flatten(1)->toArray();
    }
}
