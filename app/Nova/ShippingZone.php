<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\Heading;
use Capitalc\PriceDuration\PriceDuration;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class ShippingZone extends Resource
{
    use SuperAdmin;

    public static string $model = \App\ShippingZone::class;

    public static function label(): string
    {
        return 'Shipping Zone';
    }

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
        'zip_codes_text',
    ];

    public function fields(NovaRequest $request): array
    {
        return array_merge([
            ID::make()->sortable(),

            Text::make('Name')->rules('required'),

            Textarea::make('Zip Codes', 'zip_codes_text')->rules('required'),

        ], $this->priceDuration());
    }

    protected function priceDuration(): array
    {
        return \App\ShippingOption::all()->map(function ($item) {
            return [
                Heading::make($item->visible_name)->onlyOnForms(),
                PriceDuration::make('Active', 'price_duration')
                    ->withMeta(['setting' => $item])
                    ->onlyOnForms()
            ];
        })->flatten(1)->all();
    }
}
