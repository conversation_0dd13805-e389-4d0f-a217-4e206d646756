<?php

namespace App\Nova\Filters;

use App\Order;
use App\ZipCode;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Outl1ne\NovaMultiselectFilter\MultiselectFilter;

class ByShippingZone extends MultiselectFilter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    // public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param Request $request
     * @param Builder $query
     * @param mixed $value
     * @return Builder
     */
    public function apply(Request $request, $query, $value)
    {
        $value = collect(explode('--', collect($value)->implode('--')))->values();
        return $query->whereIn('shipping->shippingInfo->postal_code', $value);
    }

    /**
     * Get the filter's available options.
     *
     * @param Request $request
     * @return array
     */
    public function options(Request $request)
    {
        return Order::where('shipping->shippingType->name', 'Local Delivery')->where('status', 'shipped')->where(
            'shipping->route_4_me_address->created_route',
            null
        )
            ->get()
            ->pluck('shipping.shippingInfo.postal_code')
            ->filter()
            ->unique()
            ->map(function ($code) {
                $zip_code = ZipCode::getCode($code);
                $zone = optional($zip_code)->shippingZone;
                return [
                    'zone' => optional($zone)->name,
                    'code' => $code,
                ];
            })
            ->groupBy('zone')
            ->mapWithKeys(function ($zone_group, $key) use (&$object) {
                return [
                    $zone_group
                        ->pluck('code')
                        ->map(function ($item) {
                            //remove ZIP+4
                            return substr($item, 0, 5);
                        })
                        ->unique()
                        ->implode('--') => $key
                ];
            });
    }
}
