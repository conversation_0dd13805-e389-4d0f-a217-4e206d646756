<?php

namespace App\Nova\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Filters\Filter;

class ByVerifed extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param Request $request
     * @param Builder $query
     * @param mixed $value
     * @return Builder
     */
    public function apply(Request $request, $query, $value)
    {
        if ($value == 'true') {
            return $query->where('shipping->route_4_me_address->verified', 'true');
        }
        return $query->where(function ($query) {
            return $query->where('shipping->route_4_me_address->verified', 'false')->orWhere(
                'shipping->route_4_me_address->verified',
                null
            );
        });
    }

    /**
     * Get the filter's available options.
     *
     * @param Request $request
     * @return array
     */
    public function options(Request $request)
    {
        return [
            'Verified' => true,
            'Not Verified' => false,
        ];
    }
}
