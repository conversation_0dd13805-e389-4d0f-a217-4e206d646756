<?php

namespace App\Nova\Filters;

use DigitalCreative\RangeInputFilter\RangeInputFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class StorePriceRangeFilter extends RangeInputFilter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    // public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param Request $request
     * @param Builder $query
     * @param mixed $value
     * @return Builder
     */
    public function apply(Request $request, $query, $value)
    {
        return $query->whereBetween('store_price', [$value['from'], $value['to']]);
        // $value will always be [ "from" => ?, "to" => ? ]
    }

    /**
     * Get the filter's available options.
     *
     * @param Request $request
     * @return array
     */
    public function options(Request $request): array
    {
        return [
            'fromPlaceholder' => 0,
            'toPlaceholder' => 20,
            'dividerLabel' => 'to',
        ];
    }
}
