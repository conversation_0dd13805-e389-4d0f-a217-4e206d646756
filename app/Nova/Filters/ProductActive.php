<?php

namespace App\Nova\Filters;

use Laravel\Nova\Filters\Filter;
use Laravel\Nova\Http\Requests\NovaRequest;

class ProductActive extends Filter
{
    public $component = 'select-filter';

    public $name = 'Active Status';

    public function apply(NovaRequest $request, $query, $value)
    {
        if ($value == 'active') {
            return $query->active();
        } elseif ($value == 'inactive') {
            return $query->inactive();
        }
    }

    public function options(NovaRequest $request)
    {
        return [
            'Active' => 'active',
            'Inactive' => 'inactive',
        ];
    }
}
