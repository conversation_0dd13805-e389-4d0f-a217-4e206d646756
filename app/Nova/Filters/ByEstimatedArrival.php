<?php

namespace App\Nova\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Laravel\Nova\Filters\DateFilter;

class ByEstimatedArrival extends DateFilter
{
    /**
     * Apply the filter to the given query.
     *
     * @param Request $request
     * @param Builder $query
     * @param mixed $value
     * @return Builder
     */
    public function apply(Request $request, $query, $value)
    {
        $value = Carbon::parse($value);

        return $query->where(function ($query) use ($value) {
            return $query
                ->where('shipping->shippingType->estimated_arrival', 'LIKE', '%' . $value->toDateString() . '%')
                ->orWhere(
                    'shipping->shippingType->estimated_arrival',
                    'LIKE',
                    '%' . $value->addDay()->toDateString() . '%'
                );
        });
    }
}
