<?php

namespace App\Nova\Filters;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Outl1ne\NovaMultiselectFilter\MultiselectFilter;

class StoreSubCategory extends MultiselectFilter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    // public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param Request $request
     * @param Builder $query
     * @param mixed $value
     * @return Builder
     */
    public function apply(Request $request, $query, $value)
    {
        return $query->whereIn('store_sub_category', $value);
    }

    /**
     * Get the filter's available options.
     *
     * @param Request $request
     * @return array
     */
    public function options(Request $request)
    {
        return Db::table('products')
            ->select(['store_sub_category'])
            ->distinct()
            ->get()
            ->mapWithKeys(function ($category) {
                return [
                    $category->store_sub_category => $category->store_sub_category
                ];
            })
            ->filter()
            ->prepend(null, '');
    }
}
