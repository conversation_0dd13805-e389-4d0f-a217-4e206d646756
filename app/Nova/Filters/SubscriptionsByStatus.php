<?php

namespace App\Nova\Filters;

use App\Subscription;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Filters\Filter;

class SubscriptionsByStatus extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param Request $request
     * @param Builder $query
     * @param mixed $value
     * @return Builder
     */
    public function apply(Request $request, $query, $value)
    {
        return $query->where('status', $value);
    }

    /**
     * Get the filter's available options.
     *
     * @param Request $request
     * @return array
     */
    public function options(Request $request)
    {
        return Subscription::pluck('status', 'status')->toArray();
    }
}
