<?php

namespace App\Nova\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Filters\Filter;

class Photographed extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param Request $request
     * @param Builder $query
     * @param mixed $value
     * @return Builder
     */
    public function apply(Request $request, $query, $value)
    {
        if ($value) {
            return $query->where('not_photographed', true);
        }
        return $query->where(function ($query) {
            return $query->where('not_photographed', false)->orWhere('not_photographed', null);
        });
    }

    /**
     * Get the filter's available options.
     *
     * @param Request $request
     * @return array
     */
    public function options(Request $request)
    {
        return [
            'Photographed' => 0,
            'Was Not Photographed' => 1,
        ];
    }
}
