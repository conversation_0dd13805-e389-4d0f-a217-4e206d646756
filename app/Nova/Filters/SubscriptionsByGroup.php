<?php

namespace App\Nova\Filters;

use App\SubscriptionGroup;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Filters\Filter;

class SubscriptionsByGroup extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param Request $request
     * @param Builder $query
     * @param mixed $value
     * @return Builder
     */
    public function apply(Request $request, $query, $value)
    {
        return $query->where('subscription_group_id', $value);
    }

    /**
     * Get the filter's available options.
     *
     * @param Request $request
     * @return array
     */
    public function options(Request $request)
    {
        return SubscriptionGroup::pluck('id', 'name')->toArray();
    }
}
