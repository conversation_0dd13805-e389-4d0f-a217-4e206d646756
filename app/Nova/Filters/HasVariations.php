<?php

namespace App\Nova\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Filters\Filter;

class HasVariations extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param Request $request
     * @param Builder $query
     * @param mixed $value
     * @return Builder
     */
    public function apply(Request $request, $query, $value)
    {
        if ($value == 'true') {
            return $query->where('var_skus->skus', '!=', null)->where('var_skus', 'not like', '%"skus": []%');
        }
        return $query->where(function ($query) {
            return $query->where('var_skus->skus', '=', null)->orWhere('var_skus', 'like', '%"skus": []%');
        });
    }

    /**
     * Get the filter's available options.
     *
     * @param Request $request
     * @return array
     */
    public function options(Request $request)
    {
        return [
            'Has Variations' => true,
            'Does Not Have Variations' => false,
        ];
    }
}
