<?php

namespace App\Nova;

use App\Nova\Actions\CreateRoute;
use App\Nova\Filters\ByEstimatedArrival;
use App\Nova\Filters\ByPostalCode;
use App\Nova\Filters\ByShippingZone;
use App\Nova\Filters\ByVerifed;
use App\ZipCode;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class RouteOrder extends Resource
{
    public static string $model = \App\Order::class;

    public static $title = 'id';

    public static $search = [
        'id',
    ];

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query
            ->where('shipping->shippingType->name', 'Local Delivery')
            ->where('status', 'shipped')
            ->where('shipping->route_4_me_address->created_route', null)
            ->where('created_at', '>', '2021-06-01');
    }

    public static function authorizedToCreate(Request $request)
    {
        return false;
    }

    public function authorizedToUpdate(Request $request)
    {
        return false;
    }

    public function authorizedToDelete(Request $request)
    {
        return false;
    }

    public function fields(NovaRequest $request): array
    {
        return [
            Text::make('Order Id', 'id')->resolveUsing(function ($value) {
                return '<a href="/admin/resources/orders/' . $value . '" class="no-underline dim text-primary font-bold">' . $value . '</a>';
            })->asHtml(),

            Text::make('Shipping Zone')->resolveUsing(function ($value) {
                return optional(
                    optional(ZipCode::getCode(data_get($this->shipping, 'shippingInfo.postal_code')))->shippingZone
                )->name;
            }),
            DateTime::make('Estimated Arrival')->resolveUsing(function ($value) {
                return data_get($this->shipping, 'shippingType.estimated_arrival') ? now()->parse(
                    data_get($this->shipping, 'shippingType.estimated_arrival')
                ) : null;
            })->displayUsing(function ($date) {
                return $date ? $date->format('F j, Y') : null;
            }),
            Boolean::make('Verified')->resolveUsing(function ($value) {
                return data_get($this->shipping, 'route_4_me_address.verified');
            }),
            Text::make('Address')->resolveUsing(function ($value) {
                $shippingInfo = data_get($this->shipping, 'shippingInfo');

                return data_get($shippingInfo, 'address_line_1')
                    . ', ' . data_get($shippingInfo, 'city')
                    . ', ' . data_get($shippingInfo, 'state')
                    . ' ' . data_get($shippingInfo, 'postal_code');
                // return data_get($this->shipping, 'route_4_me_address.original');
            }),
            Text::make('Verified Address')->resolveUsing(function ($value) {
                return data_get($this->shipping, 'route_4_me_address.address');
            }),
            Text::make('Customer')
                ->resolveUsing(function ($customer) {
                    return $customer->name ?? '';
                }),
            DateTime::make('Created At')->displayUsing(function ($date) {
                return $date ? $date->format('m/d/Y h:i A') : null;
            })->sortable(),
        ];
    }

    public function filters(NovaRequest $request): array
    {
        return [
            new ByVerifed,
            new ByPostalCode,
            new ByShippingZone,
            new ByEstimatedArrival,
        ];
    }


    public function actions(NovaRequest $request): array
    {
        return [
            (new CreateRoute)->canRun(function () {
                return true;
            })->canSee(function () {
                return true;
            })
        ];
    }
}
