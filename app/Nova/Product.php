<?php

namespace App\Nova;

use App\Nova\Actions\AddCountryOrigin;
use App\Nova\Actions\AddHsCode;
use App\Nova\Actions\ApplyOnlinePrice;
use App\Nova\Actions\MarkAsPhotographed;
use Capitalc\Anchor\Anchor;
use Capitalc\Link\Link;
use Illuminate\Support\Facades\Log;
use Laravel\Nova\Fields\FormData;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Panel;
use Spatie\TagsField\Tags;
use Illuminate\Support\Str;
use Laravel\Nova\Fields\ID;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Date;
use Capitalc\Select2\Select2;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Number;
use Capitalc\Checkbox\Checkbox;
use Capitalc\Creators\Creators;
use App\Nova\Actions\SetVisible;
use Laravel\Nova\Fields\Country;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Fields\KeyValue;
use Capitalc\Froala\Froala;
use Laravel\Nova\Fields\BelongsTo;
use App\Nova\Filters\Category;
use App\Nova\Filters\HasVariations;
use App\Nova\Filters\Photographed;
use App\Nova\Filters\StorePriceRangeFilter;
use App\Nova\Filters\StoreCategory;
use App\Nova\Filters\StoreSubCategory;
use App\Nova\Filters\Vendor;
use App\Nova\Filters\UpdatedToFilter;
use App\Nova\Filters\UpdatedFromFilter;
use Capitalc\ProductSearch\ProductSearch;
use Capitalc\Currencyfield\Currencyfield;
use Ebess\AdvancedNovaMediaLibrary\Fields\Files;
use Ebess\AdvancedNovaMediaLibrary\Fields\Media;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Alexwenzel\DependencyContainer\DependencyContainer;
use Alexwenzel\DependencyContainer\HasDependencies;
use Capitalc\SidebarNavigation\SidebarNavigation;

class Product extends Resource
{
    use HasDependencies, ProductViewer;

    public function authorizedToDelete(Request $request)
    {
        return auth()->user()->role != 'Product Viewer';
    }

    public static $export_list = [
        'id',
        'title',
        'description',
        'short_desc',
        'heb_title',
        'heb_description',
        'heb_short_desc',
        'list_price',
        'store_price',
        'online_price',
        'sale_price',
        'store_quantity',
        'website_quantity',
        'barcode',
        'item_type',
        'sku',
        'item_type',
        'width',
        'height',
        'length',
        'weight',
        'origin',
        'system_code',
        'website_quantity',
        'track_inventory',
        'vendor_id',
        'label_id',
        'personalizes_id',
        'tax_code',
        'publish',
        'visibility',
        'exclude_free_shipping',
        'category_ids',
    ];

    public static $model = 'App\Product';

    public static function applySearch($query, $term)
    {
        $query = parent::applySearch($query, $term);
        if (request('viaRelationship')) {
            return $query;
        }
        $query->whereJsonContains('products.var_skus', $term);
        $query->orWhereJsonContains('products.var_skus', strtoupper($term));
        collect(self::$search)->each(function ($srch) use ($query, $term) {
            if ((starts_with($term, '"') && ends_with($term, '"'))) {
                $query->orWhere('products.' . $srch, 'like', trim($term, '"'));
            } elseif (starts_with($term, "'") && ends_with($term, "'")) {
                $query->orWhere('products.' . $srch, 'like', trim($term, "'"));
            } else {
                $query->orWhere('products.' . $srch, 'like', '%' . $term . '%');
            }
        });
        return $query;
    }

    public static $title = 'title';
    public static $group = 'Products';

    public static function label(): string
    {
        return 'All Products';
    }
    public static function singularLabel(): string
    {
        return 'Product';
    }

    public static $search = [
        'id',
        'title',
        'heb_title',
        'sku',
        'barcode',
        'var_skus',
    ];

    public function fieldsForIndex($request): array
    {
        return [
            ID::make()->sortable(),
            Text::make('Title')
                ->rules('required', 'min:3')
                ->sortable()
                ->help("Store title = $this->store_title"),
            Images::make('Media')
                ->croppingConfigs(['ratio' => 4 / 3])
                ->conversionOnIndexView('thumb')
                ->conversionOnDetailView('thumb'),
            Text::make('Online Price', 'add_online_price'),
            Text::make('Price'),
            // \Capitalc\S3Upload\S3Upload::make('Digital File', 'digital'),
            Checkbox::make('Active'),
            Checkbox::make('Preview'),
        ];
    }

    public function fields(NovaRequest $request): array
    {
        return [
            SidebarNavigation::make('Navigation', [
                'Details' => '#details',
                'Images' => '#images',
                'Pricing' => '#pricing',
                'Inventory' => '#inventory',
                'Shipping' => '#shipping',
                'Organize' => '#organize',
                'SEO' => '#seo',
                'Publishing' => '#publishing',
                'Variations' => '#variations',
                'Previews' => '#previews',
                'Product Relations' => '#product_relations',
            ], !empty($this->path) ? url($this->path) : null),

            Panel::make('Details', [
                Anchor::make('details'),
                ID::make()->sortable(),
                Text::make('Title')
                    ->rules('required', 'min:3')
                    ->sortable()
                    ->help("Store title = $this->store_title"),
                Froala::make('Highlights', 'short_desc')
                    ->hideFromIndex(),
                Froala::make('Description')
                    ->hideFromIndex(),
                Checkbox::make('Hebrew', 'show_hebrew')
                    ->hideFromIndex(),
                DependencyContainer::make([
                    Text::make('Hebrew Title', 'heb_title')
                        ->hideFromIndex()
                        ->rules('nullable', 'min:3')
                        ->sortable(),
                    Froala::make('Hebrew Highlights', 'heb_short_desc')
                        ->hideFromIndex(),
                    Froala::make('Hebrew Description', 'heb_description')
                        ->hideFromIndex(),
                ])->dependsOn('show_hebrew', true),
            ]),

            Panel::make('Images', [
                Anchor::make('images'),
                Images::make('Media')
                    ->croppingConfigs(['ratio' => 4 / 3])
                    ->conversionOnIndexView('thumb')
                    ->conversionOnDetailView('thumb'),
                Checkbox::make('Not Photographed')->hideFromIndex(),
            ]),

            Panel::make('Pricing', [
                Anchor::make('pricing'),
                Currencyfield::make('List Price')
                    ->hideFromIndex(),
                Currencyfield::make('Store Price')
                    ->hideFromIndex()
                    ->withMeta(['disabled' => true])
                    ->help("<a href={$this->track_link}>See the POS history</>"),

                Select::make('Online Price', 'add_online_price')->options(['set' => 'Set Price', 'rules' => 'Pricing Rule']),

                DependencyContainer::make([
                    Select::make('Based On', 'online_price_based_on')->options([
                        'list_price' => 'List Price',
                        'store_price' => 'Store Price',
                        'online_price' => 'Online Price',
                        'cost_price' => 'Cost Price',
                        'sale_price' => 'Store Sale Price',
                    ]),
                    Currencyfield::make('Percent', 'online_price_percent')
                        ->withMeta(['side' => 'right', 'symbol' => '%']),
                ])->dependsOn('add_online_price', 'rules'),
                DependencyContainer::make([
                    Currencyfield::make('Online Price')
                        ->hideFromIndex(),
                ])->dependsOn('add_online_price', 'set'),

                Currencyfield::make('Store Sale Price', 'store_sale_price')
                    ->hideFromIndex()
                    ->withMeta(['disabled' => true]),



                Checkbox::make('Online Sale Price', 'add_sale')->hideFromIndex(),
                DependencyContainer::make([
                    Select::make('Sale Type')->options($this->saleOptions),

                    DependencyContainer::make([
                        Currencyfield::make('Percent Off', 'sale_amount')
                            ->withMeta(['side' => 'right', 'symbol' => '%']),
                        Select::make('Deduct From', 'sale_from')->options([
                            'list_price' => 'List Price',
                            'store_price' => 'Store Price',
                            'online_price' => 'Online Price',
                            'cost_price' => 'Cost Price',
                            'sale_price' => 'Sale Price',
                        ]),
                    ])->dependsOn('sale_type', 'percent'),

                    DependencyContainer::make([
                        Currencyfield::make('Sale Price', 'sale_amount')
                    ])->dependsOn('sale_type', 'fixed'),

                    Date::make('Start Sale'),
                    Date::make('End Sale'),

                ])->dependsOn('add_sale', true)->hideFromIndex(),
                Currencyfield::make('Price')
                    ->exceptOnForms(),

                Select::make('Tax Code')
                    ->onlyOnForms()
                    ->options(\App\Tax::$options),
            ]),

            Panel::make('Inventory', [
                Anchor::make('inventory'),
                Text::make('SKU Number', 'sku')
                    ->hideFromIndex()
                    ->rules('nullable'),

                Text::make('Vendor SKU', 'vendor_sku')
                    ->hideFromIndex()
                    ->rules('nullable'),

                Text::make('Barcode (UPC)', 'barcode')
                    ->hideFromIndex()
                    ->rules('nullable')
                    ->withMeta(['extraAttributes' => [
                        'readonly' => true
                    ]]),

                Text::make('GTNI', 'gtni')
                    ->hideFromIndex()
                    ->rules('nullable'),

                Checkbox::make('Allow Sell Out Of Stock', 'allow_sell_out_of_stock'),
                DependencyContainer::make([
                    Checkbox::make('Track Inventory')
                        ->withMeta(['value' => $this->track_inventory ?? true])
                        ->hideFromIndex()
                        ->help('Even if Track Inventory is off we still send the orders to the POS to deduct from inventory and for store reports.'),
                    DependencyContainer::make([
                        Number::make('Store Quantity')
                            ->hideFromIndex()
                            ->rules('nullable')
                            ->withMeta(['extraAttributes' => [
                                'readonly' => true
                            ]])
                            ->help("<a href={$this->track_link}>See the POS history</>"),
                        Number::make('Fulfillment Quantity', 'website_quantity')
                            ->hideFromIndex()
                            ->rules('nullable')
                            ->withMeta(['extraAttributes' => [
                                'readonly' => true
                            ]]),

                    ])->dependsOn('track_inventory', true),

                ])->dependsOn('item_type', 'physical'),

                Number::make('Max Quantity')
                    ->hideFromIndex()
                    ->rules('nullable'),

                DependencyContainer::make([
                    Checkbox::make('Track Inventory')
                        ->withMeta(['value' => true])
                        ->hideFromIndex()
                        ->help('Even if Track Inventory is off we still send the orders to the POS to deduct from inventory and for store reports.'),
                    DependencyContainer::make([
                        Number::make('Store Quantity')
                            ->hideFromIndex()
                            ->rules('nullable')
                            ->withMeta(['extraAttributes' => [
                                'readonly' => true
                            ]]),
                        Number::make('Fulfillment Quantity', 'website_quantity')
                            ->hideFromIndex()
                            ->rules('nullable')
                            ->withMeta(['extraAttributes' => [
                                'readonly' => true
                            ]]),
                    ])->dependsOn('track_inventory', true),
                ])->dependsOn('item_type', 'both'),
            ]),

            Panel::make('Shipping', [
                Anchor::make('shipping'),
                Select::make('Item Type')->options([
                    'physical' => 'Physical Item',
                    'digital' => 'Digital Item',
                    'both' => 'Physical & Digital Item',
                    'service' => 'Service Item',
                ])
                    ->withMeta(["value" => $this->item_type ?? "physical"])
                    ->displayUsingLabels()
                    ->onlyOnForms(),

                DependencyContainer::make([
                    Currencyfield::make('Width')->rules('required')->help('Required')
                        ->withMeta(['side' => 'right', 'symbol' => 'in'])
                        ->hideFromIndex()
                        ->rules('nullable'),
                    Currencyfield::make('Height')->rules('required')->help('Required')
                        ->withMeta(['side' => 'right', 'symbol' => 'in'])
                        ->hideFromIndex()
                        ->rules('nullable'),
                    Currencyfield::make('Length')->rules('required')->help('Required')
                        ->withMeta(['side' => 'right', 'symbol' => 'in'])
                        ->hideFromIndex()
                        ->rules('nullable'),
                    Currencyfield::make('Weight')->rules('required')
                        ->help('Required, per box')
                        ->withMeta(['side' => 'right', 'symbol' => 'lb'])
                        ->hideFromIndex()
                        ->rules('nullable'),
                    Number::make('Amount Of Boxes', 'boxes')->withMeta(['value' => $this->boxes ?? 1]),
                    Checkbox::make('Exclude From Free Shipping', 'exclude_free_shipping')
                        ->hideFromIndex()
                        ->rules('nullable')->help($this->freeShippingMessage()),
                    Country::make('Country Origin', 'origin')
                        ->hideFromIndex(),
                    Text::make('HS Code', 'system_code')
                        ->hideFromIndex()
                        ->rules('nullable'),
                ])->dependsOnIn('item_type', ['both', 'physical']),

                // \Capitalc\S3Upload\S3Upload::make('Digital File', 'digital'),

                Number::make('Extended Duration', 'duration')
                    ->hideFromIndex()
                    ->rules('nullable'),

                Checkbox::make('Not eligible for return', 'exclude_from_returns')->hideFromIndex()->rules('nullable'),
            ]),

            Panel::make('Organize', [
                Anchor::make('organize'),
                BelongsTo::make('Product Type', 'product_type')
                    ->nullable()
                    ->hideFromIndex(),

                Select2::make('Vendor')
                    ->nullable()
                    ->onlyOnForms()->help("Store Vendor = $this->store_vendor"),

                Creators::make('Creators Ids')
                    ->withMeta([
                        'extraAttributes' => [
                            'data' => $this->creators_ids,
                            'availableResources' => $this->creators_ids ? \App\Creator::select(['id as value', 'name as display'])->whereIn('id',  $this->creators_ids)->get() : [],
                            'hide' => true,
                            'delay' => true,
                            'label' => '',
                        ],
                    ])
                    ->dependsOn('product_type', true)
                    ->onlyOnForms(),

                \Capitalc\Categories\Categories::make('Categories Ids')
                    ->onlyOnForms()->help("Store Category = $this->store_category</br>Store Sub Category = $this->store_sub_category"),

                \Capitalc\Filters\Filters::make('Filters', 'filter_ids')
                    ->dependsOn('categories_ids', true)
                    ->onlyOnForms(),

                Date::make('Release Date')
                    ->hideFromIndex()
                    ->rules('date', 'nullable'),

                KeyValue::make('Specs', 'meta')
                    ->rules('json')
                    ->dependsOn('product_type', function (KeyValue $field, NovaRequest $request, FormData $formData) {
                        $productType = $formData->get('product_type');
                        if ($productType) {
                            $productType = \App\ProductType::find($productType);
                            $oldValue = collect($field->value);
                            $newValue = collect($productType->fields)->map(function ($field, $key) use ($oldValue) {
                                if (isset($oldValue[$key])) {
                                    return $oldValue[$key];
                                }
                                return $field;
                            })->toArray();
                            $field->setValue($newValue);
                        } else{
                            $field->setValue([]);
                        }
                    }),

                BelongsTo::make('Label')
                    ->nullable()
                    ->onlyOnForms(),

                BelongsTo::make('Personalizes')
                    ->nullable()
                    ->hideFromIndex(),

                Creators::make('Recurrings')
                    ->withMeta(['extraAttributes' => [
                        'hide' => false,
                        'label' => 'Recurrings',
                        'type' => '',
                        'availableResources' => \App\RecurringSetting::get(['name', 'id'])->map(function ($recurring) {
                            return ['display' => $recurring['name'], 'value' => $recurring['id']];
                        }),
                    ]])
                    ->onlyOnForms(),
            ]),

            Panel::make('Variations', [
                Anchor::make('variations'),
                \CapitalC\Variations\Variations::make('Variations')
                    ->withMeta([
                        'variation_info' => $this->variationInfos,
                        'default_variation' => $this->default_variation,
                        'media' => $this->getMedia('variations')->toArray(),
                    ])
                    ->dependsOn([
                        "price",
                        "store_title",
                        "sku",
                        "vendor_sku",
                        "cost_price",
                        "list_price",
                        "store_price",
                        "add_online_price",
                        "online_price",
                        "online_price_based_on",
                        "online_price_percent",
                        "sale_price",
                        "add_sale",
                        "sale_type",
                        "sale_from",
                        "sale_amount",
                        "start_sale",
                        "end_sale",
                        "barcode",
                        "gtni",
                        "track_inventory",
                        "store_quantity",
                        "website_quantity",
                        "max_quantity",
                        "item_type",
                        "width",
                        "height",
                        "length",
                        "weight",
                        "boxes"
                    ], true)
                    ->onlyOnForms(),
            ]),

            Panel::make('Previews', [
                Anchor::make('previews'),
                Files::make('PDF Preview')
                    ->customPropertiesFields([
                        Checkbox::make('Right To Left', 'rtl'),
                    ])->onlyOnForms(),
                Files::make('MP3 Preview')->onlyOnForms(),
                Media::make('Video Preview')->onlyOnForms(),
            ]),

           Panel::make('Product Relations', [
                Anchor::make('product_relations'),
                ProductSearch::make('Add Ons', 'formatted_add_ons')->onlyOnForms(),

                Tags::make('Tags')
                    ->onlyOnForms(),

                Link::make('', 'id')
                    ->onlyOnForms()
                    ->withMeta(['value' => $this->linkedItems()]),
            ]),

            Panel::make('SEO', [
                Anchor::make('seo'),
                Text::make('Title', 'seo_title')
                    ->onlyOnForms()
                    ->help('Will use default title, if left empty'),
                Textarea::make('Description', 'seo_desc')
                    ->onlyOnForms()
                    ->help('Will use default description, if left empty'),
            ]),

            Panel::make('Publishing', [
                Anchor::make('publishing'),
                Checkbox::make('Visibility')
                    ->hideFromIndex(),
                DependencyContainer::make([
                    Date::make('Schedule', 'publish')
                        ->hideFromIndex()
                        ->rules('date', 'nullable') //, 'before:expire'
                        ->sortable(),
                ])->dependsOn('visibility', true),
                ...$this->notifications(),
                Checkbox::make('Active')
                    ->exceptOnForms(),
                Checkbox::make('Preview')
                    ->exceptOnForms(),
            ]),
        ];
    }

    public function cards(NovaRequest $request): array
    {
        return auth()->user()->role != 'Product Viewer' ? [
            new \App\Nova\Metrics\ActiveProducts(),
            new \App\Nova\Metrics\ActiveVariations(),
            new \Capitalc\Reruns\Reruns,
        ] : [];
    }

    public function filters(NovaRequest $request): array
    {
        return [
            new \Capitalc\WhereinFilter\WhereinFilter,
            new Filters\ProductActive,
            new Filters\capitalc\Media,
            new UpdatedFromFilter,
            new UpdatedToFilter,
            new Category,
            new Filters\Filter,
            new Vendor,
            new \App\Nova\Filters\TrackInventory,
            new StoreCategory,
            new StoreSubCategory,
            new Photographed,
            new StorePriceRangeFilter,
        ];
    }

    public function actions(NovaRequest $request): array
    {
        return [
            (new Actions\TrackInventory),
            (new SetVisible),
            (new \App\Nova\Actions\AddSale),
            (new Actions\ApplyTaxCode),
            new \App\Nova\Actions\MarkAsExcludeFromFreeShipping,
            new ApplyOnlinePrice,
            new MarkAsPhotographed,
            new AddHsCode,
            new AddCountryOrigin,
            new \App\Exports\ProductEmailExport,
            \Laravel\Nova\Actions\ExportAsCsv::make()->withFormat(function ($model) {
                return [
                    'ID' => $model->getKey(),
                    'Name' => $model->title,
                    'Sku' => $model->sku,
                    'Barcode' => $model->barcode,
                    'ISBN' => data_get($model, 'meta.isbn 13') ?? data_get($model, 'meta.ISBN 13'),
                    // 'Link' => env('APP_URL') . "/admin/resources/products/$model->id/edit",
                    // 'Price' => $model->price,
                ];
            }),
        ];
    }

    public function linkedItems(): array
    {
        if (!request()->is('nova-api/products/*/update-fields')) {
            return [];
        }
        $links = $this->morphMany(\App\LinkItem::class, 'model')
            ->get()
            ->map
            ->link
            ->unique()
            ->values();

        $subscriptions = $this->hasMany(\App\SubscriptionGroupItem::class)
            ->get()
            ->map
            ->subscriptionGroup
            ->unique()
            ->values();

        return compact('links', 'subscriptions');
    }

    public function notifications(): array
    {
        if (!request()->is('nova-api/products/*/update-fields')) {
            return [];
        }
        if (data_get($this, 'notification.sent')) {
            $count = data_get($this, 'notification.amount');
            $date = now()->parse(data_get($this, 'notification.sent_on'))->calendar();
            return [
                Text::make('Follow Notification', 'notification')
                    ->withMeta([
                        'value' => "Sent: $date - {$count} " . Str::plural('follower', $count),
                        'extraAttributes' => ['readonly' => true],
                    ])
                    ->hideFromIndex(),
            ];
        }
        $count = $this->followers->count();
        $help_text = 'Send to ' . $count . ' ' . Str::plural('follower', $count);

        return [
            Checkbox::make('Follow Notification', 'notification')
                ->withMeta([
                    'hidden' => !$this->release_date
                        || $this->release_date->isBefore(now()->subDays(30))
                        || $this->release_date->isAfter(now())
                ])
                ->help($help_text)
                ->hideFromIndex(),
        ];
    }
    public function freeShippingMessage(): string
    {
        return '<p>Does not apply to '
            . implode(' & ', \App\ShippingOption::find(explode(',', settings()->getValue('exclude_from_free_shipping_does_not_apply_to')))->map->internal_name->toArray())
            . ' '
            . '<a class="dim" href="/admin/resources/settings/44/edit">Settings</a></p>';
    }

    public function replicate()
    {
        return tap(parent::replicate(), function ($resource) {
            $model = $resource->model();
            $model->title = 'Duplicate of ' . $model->title;
            $model->sku = 'DUP-' . $model->sku;
        });
    }

}
