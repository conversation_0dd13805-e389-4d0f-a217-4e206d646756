<?php

namespace App\Nova\Layouts;

use Capitalc\Creators\Creators;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class BrandsLayout extends Layout
{
    protected $name = 'brands';
    protected $title = 'Brands';

    public function fields(): array
    {
        return [
            Creators::make('Brands')
                ->withMeta(['extraAttributes' => [
                    'hide' => false,
                    'label' => 'Brands',
                    'type' => '',
                    'availableResources' => \App\Http\Controllers\Admin\PathsController::vendors(),
                ]])->help(
                    'what text should i display here?'
                ),
        ];
    }
}
