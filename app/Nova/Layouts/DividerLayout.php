<?php

namespace App\Nova\Layouts;

use Laravel\Nova\Fields\Number;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class DividerLayout extends Layout
{
    protected $name = 'divider';
    protected $title = 'Divider';

    public function fields(): array
    {
        return [
            Number::make('Margin Top'),
            Number::make('Margin Bottom'),
            Number::make('Border Thickness'),
        ];
    }
}
