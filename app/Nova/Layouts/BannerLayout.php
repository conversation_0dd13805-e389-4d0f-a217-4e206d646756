<?php

namespace App\Nova\Layouts;

use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Layouts\Layout;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;

class BannerLayout extends Layout implements HasMedia
{
    use HasMediaLibrary;

    protected $name = 'banner';
    protected $title = 'Banner';

    public function fields(): array
    {
        return [
            Images::make('Images'),
            Images::make('Mobile'),
            ...\App\Http\Controllers\Admin\PathsController::novaComponents(),
        ];
    }
}
