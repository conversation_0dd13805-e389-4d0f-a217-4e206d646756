<?php

namespace App\Nova\Layouts;

use Capitalc\Checkbox\Checkbox;
use Capitalc\Currencyfield\Currencyfield;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Laravel\Nova\Fields\Text;
use Spatie\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Flexible;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class FieldsLayout extends Layout implements HasMedia
{
    use HasMediaLibrary;

    protected $name = 'options';
    protected $title = 'Fields';

    public function fields(): array
    {
        return [
            Text::make('Name'),
            Images::make('Images'),
        ];
    }
}
