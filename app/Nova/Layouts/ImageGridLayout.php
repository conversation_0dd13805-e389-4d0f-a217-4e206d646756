<?php

namespace App\Nova\Layouts;

use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Flexible;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class ImageGridLayout extends Layout implements HasMedia
{
    use HasMediaLibrary;

    protected $name = 'image-grid';
    protected $title = 'Image Grid';

    public function fields(): array
    {
        return [
            Flexible::make('Images')
                ->addLayout(ImagesLayout::class)
                ->help('1366 pixles')
                ->button('Add Banner')
        ];
    }
}
