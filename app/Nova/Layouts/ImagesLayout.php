<?php

namespace App\Nova\Layouts;

use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Spatie\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class ImagesLayout extends Layout implements HasMedia
{
    use HasMediaLibrary;

    protected $name = 'images';
    protected $title = 'Images';

    public function fields(): array
    {
        return [
            Images::make('Images'),
            Images::make('Mobile'),
            ...\App\Http\Controllers\Admin\PathsController::novaComponents(),
        ];
    }
}
