<?php

namespace App\Nova\Layouts;

use Ebess\AdvancedNovaMediaLibrary\Fields\Media;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\Downloaders\DefaultDownloader;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Exceptions\InvalidUrl;
use Whitecube\NovaFlexibleContent\FileAdder\FileAdderFactory;
use Whitecube\NovaFlexibleContent\Flexible;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

trait HasMediaLibrary
{
    use InteractsWithMedia;

    protected function getUnderlyingMediaModel(): HasMedia
    {
        $model = Flexible::getOriginModel() ?? $this->model;

        while ($model instanceof Layout) {
            $model = $model->getMediaModel();
        }

        if (is_null($model) || !($model instanceof HasMedia)) {
            throw new \Exception('Origin HasMedia model not found.');
        }

        return $model;
    }

    public function addMedia($file): \Spatie\MediaLibrary\MediaCollections\FileAdder
    {
        return app(FileAdderFactory::class)
            ->create($this->getUnderlyingMediaModel(), $file, '')
            ->preservingOriginal()
            ->usingName($this->generateMediaName($file->getClientOriginalName()))
            ->usingFileName($this->generateFileName($file->getClientOriginalName()));
    }

    public function addMediaFromUrl($url, ...$allowedMimeTypes): \Spatie\MediaLibrary\MediaCollections\FileAdder
    {
        if (!Str::startsWith($url, ['http://', 'https://'])) {
            throw InvalidUrl::doesNotStartWithProtocol($url);
        }

        $downloader = config(
            'media-library.media_downloader',
            DefaultDownloader::class
        );
        $temporaryFile = (new $downloader())->getTempFile($url);
        $this->guardAgainstInvalidMimeType($temporaryFile, $allowedMimeTypes);

        $filename = basename(parse_url($url, PHP_URL_PATH));
        $filename = urldecode($filename);

        if ($filename === '') {
            $filename = 'file';
        }

        $mediaExtension = explode('/', mime_content_type($temporaryFile));

        if (!Str::contains($filename, '.')) {
            $filename = "{$filename}.{$mediaExtension[1]}";
        }

        return app(FileAdderFactory::class)
            ->create($this->getUnderlyingMediaModel(), $temporaryFile, '')
            ->usingName($this->generateMediaName($filename))
            ->usingFileName($this->generateFileName($filename));
    }

    public function getMedia(string $collectionName = 'default', $filters = []): Collection
    {
        $model = $this->getUnderlyingMediaModel();
        if(!$this->inUseKey()) {
            return collect();
        }
        return config('media-library.media_model')::
        where('model_id', $model->getKey())
            ->where('model_type', get_class($model))
            ->where('collection_name', $collectionName)
            ->where('name', 'like', $this->inUseKey() . '__%')
            ->get();
    }

    protected function generateMediaName(string $originalName): string
    {
        return $this->inUseKey() . '__' . pathinfo($originalName, PATHINFO_FILENAME);
    }

    protected function generateFileName(string $originalName): string
    {
        return $this->inUseKey() . '__' . $originalName;
    }

    public function resolveForDisplay(array $attributes = []): array
    {
        $this->fields->each(function ($field) use ($attributes) {
            if (is_a($field, Media::class)) {
                $field->value = $this->getMedia($field->attribute)->map(function ($media) use ($field) {
                    return $media->toArray() + [
                            '__media_urls__' => $field->getConversionUrls($media),
                        ];
                });
            } else {
                $field->resolveForDisplay($attributes);
            }
        });

        $resolved = $this->getResolvedValue();
        return $resolved;
    }

    protected function removeCallback(Flexible $flexible, $layout)
    {
        if ($this->shouldDeletePreservingMedia()) {
            return;
        }

        $model = $this->getUnderlyingMediaModel();
        config('media-library.media_model')::where('model_id', $model->getKey())
            ->where('model_type', get_class($model))
            ->where('name', 'like', $this->inUseKey() . '__%')
            ->delete();
    }
}
