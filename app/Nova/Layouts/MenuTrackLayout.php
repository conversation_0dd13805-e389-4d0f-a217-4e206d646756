<?php

namespace App\Nova\Layouts;

use Capitalc\Menutrack\Menutrack;
use Laravel\Nova\Fields\Text;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class MenuTrackLayout extends Layout
{
    protected $name = 'menu-track';
    protected $title = 'Menu Track';

    public function fields(): array
    {
        return [
            Menutrack::make('Menu'),
            Text::make('Title'),
        ];
    }
}
