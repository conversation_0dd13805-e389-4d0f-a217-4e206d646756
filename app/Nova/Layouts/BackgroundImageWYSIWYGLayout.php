<?php

namespace App\Nova\Layouts;

use Capitalc\Froala\Froala;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Spatie\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class BackgroundImageWYSIWYGLayout extends Layout implements HasMedia
{
    use HasMediaLibrary;

    protected $name = 'background-image-wysiwyg';
    protected $title = 'Background Image + WYSIWYG';

    public function fields(): array
    {
        return [
            Images::make('Images'),
            Froala::make('Text'),
        ];
    }
}
