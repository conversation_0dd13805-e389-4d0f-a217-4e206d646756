<?php

namespace App\Nova\Layouts;

use Capitalc\Checkbox\Checkbox;
use Capitalc\Currencyfield\Currencyfield;
use E<PERSON>s\AdvancedNovaMediaLibrary\Fields\Images;
use Laravel\Nova\Fields\Text;
use Spatie\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Flexible;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class OptionsLayout extends Layout implements HasMedia
{
    use HasMediaLibrary;

    protected $name = 'images';
    protected $title = 'Options';

    public function fields(): array
    {
        return [
            Text::make('Name')->required(),
            Text::make('Instructions')->required(),
            Currencyfield::make('Price')->required(),
            Flexible::make('Fields', 'options')
                ->addLayout(FieldsLayout::class)
                ->button('Add Options'),
            Checkbox::make('Required')
        ];
    }
}
