<?php

namespace App\Nova\Layouts;

use Capitalc\Checkbox\Checkbox;
use Capitalc\Currencyfield\Currencyfield;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class MultiLineLayout extends Layout
{
    protected $name = 'multi-line';
    protected $title = 'Multi Line';

    public function fields(): array
    {
        return [
            Text::make('Name')->required(),
            Text::make('Instructions')->required(),
            Number::make('Character Limit'),
            Number::make('Max Lines'),
            Currencyfield::make('Price')->required(),
            Text::make('Place Holder'),
            Checkbox::make('Required')
        ];
    }
}
