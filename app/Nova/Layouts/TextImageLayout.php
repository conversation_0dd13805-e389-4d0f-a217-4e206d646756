<?php

namespace App\Nova\Layouts;

use Capitalc\Froala\Froala;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Laravel\Nova\Fields\Select;
use Spatie\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class TextImageLayout extends Layout implements HasMedia
{
    use HasMediaLibrary;

    protected $name = 'text-image';
    protected $title = 'Text + Image';

    public function fields(): array
    {
        return [
            Froala::make('Text'),
            Select::make('Image Side')->options([
                'right' => 'Right',
                'left' => 'Left',
            ]),
            Images::make('Images'),
        ];
    }
}
