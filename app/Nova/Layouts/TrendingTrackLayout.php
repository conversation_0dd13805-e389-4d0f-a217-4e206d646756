<?php

namespace App\Nova\Layouts;

use Capitalc\Froala\Froala;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Laravel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use Spatie\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class TrendingTrackLayout extends Layout implements HasMedia
{
    use HasMediaLibrary;

    protected $name = 'trending-track';
    protected $title = 'Trending Track';

    public function fields(): array
    {
        return [
            Select::make('Image Side')->options([
                'right' => 'Right',
                'left' => 'Left',
            ]),
            Images::make('Images'),
            Text::make('Image Path'),
            Images::make('Mobile')
        ];
    }
}
