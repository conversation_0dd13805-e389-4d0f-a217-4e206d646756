<?php

namespace App\Nova\Layouts;

use Capitalc\Checkbox\Checkbox;
use Capitalc\Currencyfield\Currencyfield;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class SingleLineLayout extends Layout
{
    protected $name = 'single-line';
    protected $title = 'Single Line';

    public function fields(): array
    {
        return [
            Text::make('Name')->required(),
            Text::make('Instructions')->required(),
            Number::make('Character Limit'),
            Currencyfield::make('Price')->required(),
            Text::make('Place Holder'),
            Checkbox::make('Required')
        ];
    }
}
