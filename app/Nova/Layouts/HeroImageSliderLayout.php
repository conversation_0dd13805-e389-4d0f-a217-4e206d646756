<?php

namespace App\Nova\Layouts;

use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Flexible;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class HeroImageSliderLayout extends Layout implements HasMedia
{
    use HasMediaLibrary;

    protected $name = 'hero-image-slider';
    protected $title = 'Hero Image Slider';

    public function fields(): array
    {
        return [
            Flexible::make('Banners')
                ->addLayout(BannerLayout::class)
                ->button('Add Banner'),
        ];
    }
}
