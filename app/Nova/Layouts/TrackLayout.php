<?php

namespace App\Nova\Layouts;

use Laravel\Nova\Fields\Text;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class TrackLayout extends Layout
{
    protected $name = 'track-component';
    protected $title = 'Track';

    public function fields(): array
    {
        return [
            Text::make('Title'),
            ...\App\Http\Controllers\Admin\PathsController::novaComponents(),
        ];
    }
}
