<?php

namespace App\Nova\Layouts;

use Capitalc\Menutrack\Menutrack;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Laravel\Nova\Fields\Text;
use Spatie\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class DepartmentHeaderLayout extends Layout implements HasMedia
{
    use HasMediaLibrary;

    protected $name = 'department-header';
    protected $title = 'Department Header';

    public function fields(): array
    {
        return [
            Menutrack::make('Menu'),
            Text::make('Title'),
            Images::make('Images')
        ];
    }
}
