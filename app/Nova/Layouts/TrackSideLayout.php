<?php

namespace App\Nova\Layouts;

use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Laravel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use Spatie\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Layouts\Layout;

class TrackSideLayout extends Layout implements HasMedia
{
    use HasMediaLibrary;

    protected $name = 'track-side-component';
    protected $title = 'Track + Side Banner';

    public function fields(): array
    {
        return [
            Text::make('Title'),
            ...\App\Http\Controllers\Admin\PathsController::novaComponents(),
            Select::make('Image Side')->options([
                'right' => 'Right',
                'left' => 'Left',
            ]),
            Images::make('Images'),
            Text::make('Image Path'),
            Images::make('Mobile'),
        ];
    }
}
