<?php

namespace App\Nova;

use Capitalc\Currencyfield\Currencyfield;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Laravel\Nova\Fields\Currency;
use <PERSON>vel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class GiftOption extends Resource
{
    use SuperAdmin;

    public static string $model = \App\GiftOption::class;
    public static $title = 'id';
    public static $search = [
        'id',
    ];

    public static function label(): string
    {
        return 'Gift Options';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->rules('max:255'),

            Text::make('Description')
                ->rules('max:255', 'nullable'),

            Currency::make('Price')->onlyOnIndex(),
            Currencyfield::make('Price')
                ->hideFromIndex(),

            Images::make('Media')
                ->conversionOnIndexView('thumb')
                ->singleImageRules('max:10240')
                ->conversionOnDetailView('thumb'),

        ];
    }
}
