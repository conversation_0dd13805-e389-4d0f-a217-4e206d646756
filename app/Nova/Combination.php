<?php

namespace App\Nova;

use Capitalc\Comboproducts\Comboproducts;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Date;
use Laravel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Combination extends Resource
{
    public static $displayInNavigation = false;

    public static string $model = \App\Combination::class;

    public static $search = [
        'id',
    ];

    public static function authorizedToCreate(Request $request): bool
    {
        return false;
    }

    public function authorizedToDelete(Request $request): bool
    {
        return false;
    }

    public function authorizedToUpdate(Request $request): bool
    {
        return false;
    }

    public function title(): string
    {
        return $this->type->name . ' - ' . $this->group->name;
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
            BelongsTo::make('Subscription Type', 'type'),
            BelongsTo::make('Subscription Group', 'group'),
            Comboproducts::make('Upcoming Products')->onlyOnDetail(),
            Text::make('Upcoming String'),
            Text::make('Filter Name'),
            Date::make('Upcoming Date')->displayUsing(function ($date) {
                return $date ? $date->format('F j, Y') : null;
            }),
            Text::make('From Price')->onlyOnDetail()->resolveUsing(function ($value) {
                return '$' . number_format($value, 2);
            }),
            Text::make('To Price')->onlyOnDetail()->resolveUsing(function ($value) {
                return '$' . number_format($value, 2);
            }),
        ];
    }
}
