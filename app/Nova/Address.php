<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\Country;
use Laravel\Nova\Fields\ID;
use Lara<PERSON>\Nova\Fields\MorphTo;
use Laravel\Nova\Fields\Place;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Address extends Resource
{
    public static $displayInNavigation = false;

    public static string $model = \App\Address::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name'),

            Place::make('Address', 'address_line_1')
                ->hideFromIndex(),

            Text::make('Address Line 2')
                ->hideFromIndex(),

            Text::make('City')
                ->hideFromIndex(),

            Text::make('State')
                ->hideFromIndex(),

            Text::make('Postal Code')
                ->hideFromIndex(),

            Country::make('Country')
                ->hideFromIndex(),

            MorphTo::make('Model')
                ->exceptOnForms(),
        ];
    }

    public function cards(NovaRequest $request): array
    {
        return [];
    }

    public function filters(NovaRequest $request): array
    {
        return [];
    }

    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
