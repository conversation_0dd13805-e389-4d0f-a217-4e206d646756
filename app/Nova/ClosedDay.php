<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\Date;
use Laravel\Nova\Fields\ID;
use Lara<PERSON>\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class ClosedDay extends Resource
{
    use SuperAdmin;

    public static string $model = \App\ClosedDay::class;

    public static $title = 'name';
    public static $search = [
        'name',
        'date',
        'source',
    ];

    public static function label(): string
    {
        return 'Closed Days';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
            Text::make('Name'),
            Date::make('Date'),
            Text::make('Source'),

            Text::make('Open')->onlyOnForms(),
            Text::make('Close')->onlyOnForms(),

            Select::make('Delivery / Pickup', 'option')->options([
                'both' => 'Both',
                'delivery' => 'Delivery',
                'pickup' => 'Pickup',
            ])
                ->withMeta(['value' => $this->option ?? 'both'])
                ->displayUsingLabels()
                ->onlyOnForms()
        ];
    }
}
