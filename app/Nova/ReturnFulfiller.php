<?php

namespace App\Nova;

use Illuminate\Http\Request;


trait ReturnFulfiller
{

    public static function authorizedToViewAny(Request $request)
    {
        $role = auth()->user()->role;
        return $role == 'Super Admin' || $role == 'Return Fulfiller' || $role == 'Order Fulfiller';
    }

    public function authorizedToView(Request $request)
    {
        $role = auth()->user()->role;
        return true;// $role == 'Super Admin' || $role == 'Product Lister';
    }
}
