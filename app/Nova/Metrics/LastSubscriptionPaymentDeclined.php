<?php

namespace App\Nova\Metrics;

use App\Subscription;
use DateInterval;
use DateTimeInterface;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Value;

class LastSubscriptionPaymentDeclined extends Value
{
    /**
     * Calculate the value of the metric.
     *
     * @param NovaRequest $request
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        return [
            'value' => Subscription::all()->map(function ($subscription) {
                return optional($subscription->orders->sortByDesc('created_at')->first());
            })->filter(function ($order) {
                return $order->payment_status == 'declined';
            })->count()
        ];
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            // 30 => '30 Days',
            // 60 => '60 Days',
            // 365 => '365 Days',
            // 'TODAY' => 'Today',
            // 'MTD' => 'Month To Date',
            // 'QTD' => 'Quarter To Date',
            // 'YTD' => 'Year To Date',
        ];
    }

    /**
     * Determine for how many minutes the metric should be cached.
     *
     * @return  DateTimeInterface|DateInterval|float|int
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'last-subscription-payment-declined';
    }
}
