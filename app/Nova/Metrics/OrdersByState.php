<?php

namespace App\Nova\Metrics;

use App\Order;
use DateInterval;
use DateTimeInterface;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Partition;
use Laravel\Nova\Metrics\PartitionResult;

class OrdersByState extends Partition
{
    public function calculate(NovaRequest $request): PartitionResult
    {
        $orders = Order::get('shipping')
            ->where('shipping.shippingInfo.state', '!=', '')
            ->groupBy(fn($order) => strtoupper(data_get($order, 'shipping.shippingInfo.state')))
            ->map(fn($order) => collect($order)->count())
            ->sortDesc()
            ->toArray();

        return $this->result($orders);
    }

    public function uriKey(): string
    {
        return 'orders-by-state';
    }
}
