<?php

namespace App\Nova\Metrics;

use App\Customer;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Partition;
use Laravel\Nova\Metrics\PartitionResult;

class CustomersByState extends Partition
{
    public function calculate(NovaRequest $request): PartitionResult
    {
        $customers = Customer::with('addresses')
        ->get()
            ->filter(fn($customer) => !empty($customer->address?->state))
            ->groupBy(fn($customer) => strtoupper($customer->address->state))
            ->mapWithKeys(fn($group, $state) => [$state => $group->count()])
            ->sortDesc()
            ->toArray();

        return $this->result($customers);
    }


    public function uriKey(): string
    {
        return 'customers-By-state';
    }
}
