<?php

namespace App\Nova;

use Capitalc\Filteritem\Filteritem;
use <PERSON>vel\Nova\Fields\ID;
use Capitalc\Select2\Select2;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Http\Requests\NovaRequest;

class SubscriptionType extends Resource
{
    use OrderFulfiller;

    public static string $model = \App\SubscriptionType::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')->sortable(),

            Select2::make('Filter')
                ->nullable()
                ->onlyOnForms(),

            Filteritem::make('Dates')
                ->dependsOn('filter', [])
                ->onlyOnForms(),

            HasMany::make('Combinations')
        ];
    }
}
