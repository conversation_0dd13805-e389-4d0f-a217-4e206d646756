<?php

namespace App\Nova;

use <PERSON><PERSON>zel\AjaxSelect\AjaxSelect;
use App\Nova\Metrics\ActiveRecurrings;
use App\Nova\Metrics\CancelledRecurrings;
use App\Nova\Metrics\LastRecurringPaymentDeclined;
use Capitalc\RecurringInfo\RecurringInfo;
use Illuminate\Support\Str;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\MorphMany;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class Recurring extends Resource
{
    use OrderFulfiller;

    public static string $model = \App\Recurring::class;

    public static $title = 'id';

    public static $search = [
        'id',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
            Number::make('Quantity'),

            Select::make('Type')
                ->onlyOnForms()
                ->options([
                    'Product' => 'Product',
                    'Variation' => 'Variation'
                ]),

            AjaxSelect::make('Product', 'model_id')
                ->get('/nova-custom-api/products_type/{type}')
                ->parent('type'),

            Select::make('Recurring')->options($this->recurringOptions())
                ->onlyOnForms()
                ->hideWhenUpdating(),

            BelongsTo::make('Customer')->withMeta([
                'extraAttributes' => [
                    'readonly' => true
                ]
            ])->onlyOnForms()->hideWhenCreating(),
            BelongsTo::make('Customer')->searchable()->onlyOnForms()->hideWhenUpdating(),
            BelongsTo::make('Customer')->exceptOnForms(),

            Text::make('Last Payment')->onlyOnIndex(),
            Text::make('Status')
                ->onlyOnIndex()
                ->resolveUsing(function ($key) {
                    return Str::title($key);
                })
                ->fillUsing(function ($key) {
                    return strtolower($key);
                })
                ->sortable(),
            DateTime::make('Created At')->displayUsing(function ($date) {
                return $date ? $date->format('m/d/Y h:i A') : null;
            })->exceptOnForms(),
            RecurringInfo::make('Shipping', 'shipping')
                ->resolveUsing(function ($value) {
                    return [
                        'name' => data_get($value, 'name'),
                        'phone' => data_get($value, 'phone'),
                        'address_line_1' => data_get($value, 'address_line_1'),
                        'address_line_2' => data_get($value, 'address_line_2') ?? '',
                        'city' => data_get($value, 'city'),
                        'postal_code' => data_get($value, 'postal_code'),
                        'state' => data_get($value, 'state'),
                        'country' => data_get($value, 'country')
                    ];
                })
                ->hideFromIndex(),
            AjaxSelect::make('Payment', 'payment_id')
                ->get('/nova-custom-api/customer/{customer}/payments')
                ->parent('customer'),
            MorphMany::make('Orders')

        ];
    }

    public function recurringOptions()
    {
        return \App\RecurringSetting::get(['id', 'name'])->map(function ($setting) {
            return [
                'value' => $setting->id,
                'label' => $setting->name
            ];
        });
    }

    public function cards(NovaRequest $request): array
    {
        return [
            new ActiveRecurrings,
            new CancelledRecurrings,
            new LastRecurringPaymentDeclined,
        ];
    }
}
