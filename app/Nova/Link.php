<?php

namespace App\Nova;

use Capitalc\GeneralSearch\GeneralSearch;
use Capitalc\Select2\Select2;
use Laravel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Link extends Resource
{
    use ProductLister;

    public static string $model = \App\Link::class;

    public static $title = 'name';
    public static $search = [
        'id',
        'name',
    ];

    public static function label(): string
    {
        return 'Linked';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name'),

            Select2::make('Filter')
                ->nullable()
                ->onlyOnForms(),

            GeneralSearch::make('Products', 'front_end_items')
                ->withMeta([
                    'url' => 'only_products',
                    'label' => 'products',
                ])
                ->onlyOnForms()
                ->help(
                    '<p style="color:red;">If key difference is not defined, product link will not be displayed</p>'
                ),
        ];
    }
}
