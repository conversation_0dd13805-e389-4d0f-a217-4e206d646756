<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\MorphTo;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Variation extends Resource
{
    public static $displayInNavigation = false;

    public static string $model = \App\Variation::class;

    public static $title = 'name';


    public static $search = [
        'name',
        'description',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('name'),

            Textarea::make('description'),

            MorphTo::make('Model')
        ];
    }
}
