<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\MorphOne;
use <PERSON><PERSON>\Nova\Fields\Heading;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class CreditCardPayment extends Resource
{
    public static $displayInNavigation = false;

    public static string $model = \App\CreditCardPayment::class;

    public static $title = 'id';

    public static $search = [
        'id',
    ];

    public function fields(NovaRequest $request): array
    {
        return array_merge(
            [
                ID::make()->sortable()->onlyOnDetail(),
                DateTime::make('Created At', 'created_at')->displayUsing(function ($date) {
                    return $date ? $date->format('m/d/Y h:i A') : null;
                })->onlyOnDetail(),
                Text::make('Refnum')->onlyOnDetail(),
                Text::make('Status')->onlyOnDetail(),
                Text::make('Amount')->resolveUsing(
                    function ($value) {
                        return '$' . $value;
                    }
                )->onlyOnDetail(),


            ],
            $this->authorizeHead(),
            $this->authorizeFields(),
            $this->chargesHead(),
            $this->chargesFields(),
            $this->capturesHead(),
            $this->capturesFields(),
            $this->refundsHead(),
            $this->refundsFields(),
            [
                MorphOne::make('Order')->onlyOnDetail(),

            ]
        );
    }

    public function authorizeHead(): array
    {
        if (!$this->authorized) {
            return [];
        }
        return [
            Heading::make('Authorization')->onlyOnDetail(),
        ];
    }

    public function chargesHead(): array
    {
        if (!collect($this->charges)->count()) {
            return [];
        }
        return [
            Heading::make('Charges')->onlyOnDetail(),
        ];
    }

    public function capturesHead(): array
    {
        if (!collect($this->captures)->count()) {
            return [];
        }
        return [
            Heading::make('Captures')->onlyOnDetail(),
        ];
    }

    public function refundsHead(): array
    {
        if (!collect($this->refunds)->count()) {
            return [];
        }
        return [
            Heading::make('Refunds')->onlyOnDetail(),
        ];
    }

    public function authorizeFields(): array
    {
        if (!$this->authorized) {
            return [];
        }
        return [
            DateTime::make('Date', 'authorized')->resolveUsing(
                function ($value) {
                    return data_get($value, 'Date');
                }
            )->displayUsing(function ($value) {
                $date = data_get($value, 'Date');
                return $date ? (new \DateTime($date))->format('m/d/Y h:i A') : null;
            })->onlyOnDetail(),
            Text::make('Amount', 'authorized')->resolveUsing(
                function ($value) {
                    return '$' . data_get($value, 'Amount');
                }
            )->onlyOnDetail(),
            Text::make('Message', 'authorized')->resolveUsing(
                function ($value) {
                    return data_get($value, 'Message');
                }
            )->onlyOnDetail(),
            Text::make('Authcode', 'authorized')->resolveUsing(
                function ($value) {
                    return data_get($value, 'Authcode');
                }
            )->onlyOnDetail(),
        ];
    }

    public function chargesFields(): array
    {
        return collect($this->charges)->map(function ($charge) {
            return [
                DateTime::make('Date')->resolveUsing(
                    function ($value) use ($charge) {
                        return data_get($charge, 'Date');
                    }
                )->displayUsing(function ($value) use ($charge) {
                    $date = data_get($charge, 'Date');
                    return $date ? (new \DateTime($date))->format('m/d/Y h:i A') : null;
                })->onlyOnDetail(),
                Text::make('Amount')->resolveUsing(
                    function ($value) use ($charge) {
                        return '$' . data_get($charge, 'Amount');
                    }
                )->onlyOnDetail(),
                Text::make('Authcode')->resolveUsing(
                    function ($value) use ($charge) {
                        return data_get($charge, 'Authcode');
                    }
                )->onlyOnDetail(),
                Heading::make('')->onlyOnDetail(),
            ];
        })->flatten(1)->toArray();
    }

    public function capturesFields(): array
    {
        return collect($this->captures)->map(function ($charge) {
            return [
                DateTime::make('Date')->resolveUsing(
                    function ($value) use ($charge) {
                        return data_get($charge, 'Date');
                    }
                )->displayUsing(function ($value) use ($charge) {
                    $date = data_get($charge, 'Date');
                    return $date ? (new \DateTime($date))->format('m/d/Y h:i A') : null;
                })->onlyOnDetail(),
                Text::make('Authcode')->resolveUsing(
                    function ($value) use ($charge) {
                        return data_get($charge, 'Authcode');
                    }
                )->onlyOnDetail(),
                Heading::make('')->onlyOnDetail(),
            ];
        })->flatten(1)->toArray();
    }

    public function refundsFields(): array
    {
        return collect($this->refunds)->map(function ($charge) {
            return [
                DateTime::make('Date')->resolveUsing(
                    function ($value) use ($charge) {
                        return data_get($charge, 'date');
                    }
                )->displayUsing(function ($date) use ($charge) {
                    $date = data_get($charge, 'date');
                    return $date ? (new \DateTime($date))->format('m/d/Y h:i A') : null;
                })->onlyOnDetail(),
                Text::make('Amount')->resolveUsing(
                    function ($value) use ($charge) {
                        return '$' . data_get($charge, 'Amount');
                    }
                )->onlyOnDetail(),
                Text::make('Authcode')->resolveUsing(
                    function ($value) use ($charge) {
                        return data_get($charge, 'Authcode');
                    }
                )->onlyOnDetail(),
                Heading::make('')->onlyOnDetail(),
            ];
        })->flatten(1)->toArray();
    }
}
