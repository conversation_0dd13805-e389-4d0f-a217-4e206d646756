<?php

namespace App\Nova;

use <PERSON><PERSON>zel\DependencyContainer\DependencyContainer;
use App\Nova\Actions\SetVisible;
use Capitalc\Categories\Categories;
use Capitalc\Checkbox\Checkbox;
use Capitalc\Currencyfield\Currencyfield;
use Capitalc\Filters\Filters;
use Capitalc\Froala\Froala;
use Capitalc\ProductSearch\ProductSearch;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\Heading;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class Bundle extends Resource
{
    use ProductLister;

    public static string $model = \App\Bundle::class;

    public static $title = 'title';

    public static $search = [
        'id',
        'title',
        'description',
        'short_desc',
        'heb_title',
        'heb_description',
        'heb_short_desc',
        'sku',
        'barcode',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Title')
                ->rules('required', 'min:3')
                ->sortable(),

            Froala::make('Highlights', 'short_desc')
                ->hideFromIndex(),

            Froala::make('Description')
                ->hideFromIndex(),

            Checkbox::make('Hebrew', 'show_hebrew')
                ->hideFromIndex(),

            DependencyContainer::make([
                Text::make('Title', 'heb_title')
                    ->hideFromIndex()
                    ->rules('nullable', 'min:3')
                    ->sortable(),

                Froala::make('Highlights', 'heb_short_desc')
                    ->hideFromIndex(),

                Froala::make('Description', 'heb_description')
                    ->hideFromIndex()
            ])->dependsOn('show_hebrew', true),

            Heading::make('Images')->onlyOnForms(),
            Images::make('Media')
                ->croppingConfigs(['ratio' => 4 / 3])
                ->conversionOnIndexView('thumb')
                ->singleImageRules('max:10240')
                ->conversionOnDetailView('thumb'),
            Checkbox::make('Display product images', 'show_images')
                ->withMeta(['value' => $this->show_images])
                ->onlyOnForms(),

            Heading::make('Pricing')->onlyOnForms(),
            Currencyfield::make('Bundle price', 'price')
                ->hideFromIndex(),
            Currencyfield::make('Bundle price', 'price')
                ->exceptOnForms(),

            Heading::make('Products Included')->onlyOnForms(),
            ProductSearch::make('Products', 'formatted_products')
                ->onlyOnForms()
                ->withMeta(['showQuantity' => true]),

            Checkbox::make('Display included products', 'show_products')
                ->withMeta(['value' => $this->show_products])
                ->onlyOnForms(),

            Heading::make('Inventory')->onlyOnForms(),
            Text::make('SKU Number', 'sku')
                ->hideFromIndex()
                ->rules('nullable'),
            Text::make('Barcode (UPC)', 'barcode')
                ->hideFromIndex()
                ->rules('nullable'),

            Heading::make('Organize')->onlyOnForms(),
            Categories::make('Categories Ids', 'categories_ids')
                ->onlyOnForms(),

            Filters::make('Filters', 'filter_ids')
                ->dependsOn('categories_ids', [])
                ->onlyOnForms(),

            Heading::make('Publishing')->onlyOnForms(),
            Checkbox::make('Visibility')
                ->hideFromIndex(),
            DependencyContainer::make([
                Date::make('Schedule', 'publish')
                    ->hideFromIndex()
                    ->rules('date', 'nullable')
                    ->sortable(),
            ])->dependsOn('visibility', true),

            Checkbox::make('Active')
                ->exceptOnForms(),
        ];
    }

    public function actions(NovaRequest $request): array
    {
        return [
            (new SetVisible),
        ];
    }
}
