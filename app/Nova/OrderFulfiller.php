<?php

namespace App\Nova;

use Illuminate\Http\Request;


trait OrderFulfiller
{

    public static function authorizedToViewAny(Request $request)
    {
        $user = auth('admin')->user();
        return $user && in_array($user->role, ['Super Admin', 'Order Fulfiller']);
    }

    public function authorizedToView(Request $request)
    {
        $role = auth()->user()->role;
        return true;// $role == 'Super Admin' || $role == 'Product Lister';
    }
}
