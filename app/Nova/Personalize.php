<?php

namespace App\Nova;

use App\Nova\Layouts\MultiLineLayout;
use App\Nova\Layouts\OptionsLayout;
use App\Nova\Layouts\SingleLineLayout;
use <PERSON>vel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Whitecube\NovaFlexibleContent\Flexible;

class Personalize extends Resource
{
    use OrderFulfiller;

    public static string $model = \App\Personalize::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name'
    ];

    public static function label(): string
    {
        return 'Personalizations';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')->rules(['required']),

            Number::make('Extended Duration', 'duration')
                ->help('Enter amount of days required to proccess the Personalization'),

            Flexible::make('Fields', 'options')
                ->addLayout(SingleLineLayout::class)
                ->addLayout(MultiLineLayout::class)
                ->addLayout(OptionsLayout::class)
                ->button('Add Fields')
                ->hideFromIndex(),
        ];
    }
}
