<?php

namespace App\Nova;

use Illuminate\Support\Facades\DB;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Fields\Heading;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use Capitalc\Creators\Creators;
use Capitalc\Checkbox\Checkbox;
use Capitalc\Allfilters\Allfilters;
use Capitalc\Currencyfield\Currencyfield;
use Laravel\Nova\Fields\Select;
use Illuminate\Support\Facades\Cache;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Laravel\Nova\Http\Requests\NovaRequest;

class AutomatedCategory extends Resource
{
    use ProductLister;

    public static string $model = \App\AutomatedCategory::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name'
    ];

    public function fields(NovaRequest $request): array
    {
        return array_merge(
            [
                ID::make()->sortable(),

                Text::make('Name'),

                Textarea::make('Internal Description', 'description')->alwaysShow()->rows(2),
                Text::make('Internal Description', 'description')->onlyOnIndex()->resolveUsing(function ($value) {
                    $limit = 40;
                    $result = substr($value, 0, $limit);
                    $result .= strlen($value) > $limit - 1 ? '...' : '';
                    return $result;
                }),

                Images::make('Image', 'media'),

            ],

            $this->getSelect('Categories', 'Categories All', 'categories_and', 'categories'),
            $this->getSelect('', 'Categories Either', 'categories_or', 'categories'),
            $this->getSelect('', 'Categories Exclude', 'categories_exclude', 'categories'),

            [
                Heading::make('Filters')->onlyOnForms(),
                Allfilters::make('Filters All', 'filters_and')->onlyOnForms(),
                Allfilters::make('Filters Either', 'filters_or')->onlyOnForms(),
                Allfilters::make('Filters Exclude', 'filters_exclude')->onlyOnForms(),
            ],

            $this->getSelect('Vendors', 'Vendors All', 'vendors_and', 'vendors'),
            $this->getSelect('', 'Vendors Either', 'vendors_or', 'vendors'),
            $this->getSelect('', 'Vendors Exclude', 'vendors_exclude', 'vendors'),

            $this->getSelect('Creators', 'Creator All', 'creators_and', 'creators'),
            $this->getSelect('', 'Creator Either', 'creators_or', 'creators'),
            $this->getSelect('', 'Creator Exclude', 'creators_exclude', 'creators'),

            $this->getSelect('Tags', 'Tags All', 'tags_and', 'tags'),
            $this->getSelect('', 'Tags Either', 'tags_or', 'tags'),
            $this->getSelect('', 'Tags Exclude', 'tags_exclude', 'tags'),

            [
                Heading::make('Price')->onlyOnForms(),
                Currencyfield::make('From Price', 'from_price')
                    ->onlyOnForms(),
                Currencyfield::make('To Price', 'to_price')
                    ->onlyOnForms(),
                Heading::make('')->onlyOnForms(),
                Select::make('Status')->options([
                    'new' => 'New',
                    'sale' => 'Sale',
                ])
                    ->displayUsingLabels()
                    ->nullable()
                    ->onlyOnForms(),
                Select::make('Sort By')->options([
                    '' => 'Most Relavent',
                    'price-asc' => 'Price: Low to High',
                    'price-desc' => 'Price: High to Low',
                    'release_date-desc' => 'Newest'
                ])
                    ->displayUsingLabels()
                    ->nullable()
                    ->onlyOnForms(),

                Text::make('Contains')->onlyOnForms(),
                CheckBox::make('Search Query Redirect', 'redirect')
                    ->help(
                        "When set as active, search queries that match " . $this->help_name(
                        ) . " will be redirected to the " . $this->help_name() . " page"
                    ),

                Heading::make('SEO')->onlyOnForms(),
                Text::make('Title', 'seo_title')
                    ->onlyOnForms(),

                Textarea::make('Description', 'seo_desc')
                    ->onlyOnForms(),

                HasMany::make('Products'),
            ]
        );
    }

    public function help_name(): string
    {
        return $this->name ? "'" . $this->name . "'" : 'categories name';
    }

    private $categories_options = [];
    private $filter_items_options = [];
    private $vendors_options = [];
    private $creators_options = [];
    private $tags_options = [];

    public function options($resource)
    {
        if (request()->is('nova-api/automated-categories')) {
            return [];
        }
        return Cache::tags(['search'])->remember("nova_{$resource}", 60 * 60 * 24 * 3, function () use ($resource) {
            $collection = $resource . '_options';

            if (collect($this->$collection)->isEmpty()) {
                if ($resource != 'tags') {
                    $this->$collection = DB::table($resource)->select(['id', 'name'])->get()->map(function ($obj) {
                        return ['display' => $obj->name, 'value' => $obj->id];
                    });
                } else {
                    $this->$collection = DB::table($resource)->select(['id', 'name'])->get()->map(function ($obj) {
                        return ['display' => json_decode($obj->name)->en, 'value' => $obj->id];
                    });
                }
            }
            return $this->$collection;
        });
    }

    public function getSelect($heading, $label, $attr, $option)
    {
        $array = [];
        $heading ? $array[] = Heading::make($heading)->onlyOnForms() : [];
        $array[] = Creators::make($label, $attr)
            ->withMeta([
                'extraAttributes' => [
                    'hide' => false,
                    'label' => $label,
                    'type' => '',

                    'availableResources' => $this->options($option)
                ]
            ])
            ->onlyOnForms();
        return $array;
    }
}
