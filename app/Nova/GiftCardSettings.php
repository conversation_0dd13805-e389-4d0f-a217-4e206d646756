<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class GiftCardSettings extends Resource
{
    use SuperAdmin;

    public static string $model = \App\GiftCardSettings::class;

    public static $title = 'id';

    public static $search = [
        'id',
    ];

    public static function label(): string
    {
        return 'eGift Cards';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Amount')
                ->help('comma seperated lins. (ex. 25,36,50,72,100'),

            Images::make('Media')
                ->help('576 width 320 height')
                ->conversionOnIndexView('thumb')
                ->singleImageRules('max:10240')
                ->conversionOnDetailView('thumb'),
        ];
    }
}
