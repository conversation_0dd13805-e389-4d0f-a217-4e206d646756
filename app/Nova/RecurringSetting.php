<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class RecurringSetting extends Resource
{
    use OrderFulfiller;

    public static string $model = \App\RecurringSetting::class;

    public static $title = 'name';

    public static $search = [
        'id',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
            Text::make('Name')->sortable(),
            Number::make('Amount')->sortable(),
            Select::make('Cycle')->options([
                'week' => 'Week',
                'month' => 'Month',
                'year' => 'Year',
            ]),
        ];
    }
}
