<?php

namespace App\Nova\Flexible\Resolvers;

use Illuminate\Support\Facades\DB;
use Whitecube\NovaFlexibleContent\Value\ResolverInterface;

class FilterItems implements ResolverInterface
{
    public function get($resource, $attribute, $layouts)
    {
        $items = $resource->items()->orderBy('order')->get();

        return $items->map(function ($item) use ($layouts, $resource) {
            $layout = $layouts->find('items');
            if (!$layout) {
                return;
            }

            return $layout->duplicateAndHydrate($item->id, [
                'name' => $item->name,
                'id' => $item->id,
                'items' => DB::table('filter_item_product')->where('filter_item_id', $item->id)->count()
            ]);
        })->filter();
    }

    public function set($model, $attribute, $groups)
    {
        $class = get_class($model);

        $class::saved(function ($model) use ($groups) {
            $items = $groups->map(function ($group, $index) {
                return [
                    'name' => $group->getAttributes()['name'],
                    'order' => $index,
                    'id' => array_key_exists('id', $group->getAttributes())
                        ? $group->getAttributes()['id']
                        : null,
                ];
            });

            $ids = [];
            foreach ($items as $item) {
                if ($item['id']) {
                    $new = tap($model->items()->find($item['id']))->update($item);
                } else {
                    $new = $model->items()->create($item);
                }
                $ids[] = $new->id;
            }

            $model->items()->whereNotIn('id', $ids)->delete();
        });
    }
}
