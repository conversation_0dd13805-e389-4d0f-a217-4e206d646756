<?php

namespace App\Nova;

use <PERSON><PERSON><PERSON>\DependencyContainer\DependencyContainer;
use <PERSON><PERSON><PERSON>\DependencyContainer\HasDependencies;
use Capitalc\Checkbox\Checkbox;
use <PERSON>vel\Nova\Fields\Heading;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Redirect extends Resource
{
    use HasDependencies, SuperAdmin;

    public static string $model = \App\Redirect::class;

    public static $title = 'id';

    public static $search = [
        'id',
        'from',
        'to'
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name'),

            CheckBox::make('Active'),

            Heading::make('Redirect From')->onlyOnForms(),

            Select::make('From Type')->options([
                'url' => 'Url',
                'search_term' => 'Search Term',
            ])
                ->withMeta(["value" => $this->from_type ?? "search_term"])
                ->onlyOnForms(),

            DependencyContainer::make([

                Text::make('Url', 'from')

            ])
                ->dependsOn('from_type', 'url')
                ->onlyOnForms(),

            DependencyContainer::make([

                Text::make('Search Term', 'from')
                    ->help('Enter terms comma seperated.. (book, books)'),

            ])
                ->dependsOn('from_type', 'search_term')
                ->onlyOnForms(),

            Text::make('From')->exceptOnForms(),

            Text::make('To')->exceptOnForms(),


            Heading::make('Redirect To')->onlyOnForms(),

            Select::make('To Type')->options([
                'url' => 'Url',
                'search_term' => 'Search Term',
            ])
                ->withMeta(["value" => $this->to_type ?? "search_term"])
                ->onlyOnForms(),

            DependencyContainer::make([

                Text::make('Url', 'to'),

            ])
                ->dependsOn('to_type', 'url')
                ->onlyOnForms(),

            DependencyContainer::make([

                Text::make('Search Term', 'to'),

            ])
                ->dependsOn('to_type', 'search_term')
                ->onlyOnForms(),


        ];
    }
}
