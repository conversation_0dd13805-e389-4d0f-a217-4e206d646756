<?php

namespace App\Nova;

use Capitalc\Checkbox\Checkbox;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\MorphMany;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class Group extends Resource
{
    use OrderFulfiller;

    public static string $model = \App\Group::class;

    public static $title = 'name';

    public static $search = ['id'];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->rules('max:255'),

            Text::make('Description')
                ->rules('max:255'),

            Checkbox::make('Allow Rewards'),
            MorphMany::make('Discounts'),

        ];
    }
}
