<?php

namespace App\Nova\Traits;

use Illuminate\Http\Request;

trait RestrictsCsvImport
{
    /**
     * Determine if this resource can be imported via CSV.
     *
     * @param Request $request
     * @return bool
     */
    public static function canImportResource(Request $request): bool
    {
        $allowedResources = config('custom-csv-import.allowed_resources', []);
        
        // If no allowed resources are configured, allow all
        if (empty($allowedResources)) {
            return true;
        }
        
        // Check if this resource is in the allowed list
        return in_array(static::class, $allowedResources);
    }
}
