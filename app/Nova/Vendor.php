<?php

namespace App\Nova;

use <PERSON><PERSON><PERSON>\DependencyContainer\DependencyContainer;
use Capitalc\Checkbox\Checkbox;
use Capitalc\Froala\Froala;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Fields\ID;
use Laravel\Nova\Fields\MorphToMany;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Maatwebsite\LaravelNovaExcel\Actions\DownloadExcel;

class Vendor extends Resource
{
    use ProductLister;

    public static string $model = \App\Vendor::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
        'description',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->rules('required', 'min:2'),

            Checkbox::make('Visible', 'is_visible'),
            Checkbox::make('Enable Sell Out of Stock', 'enable_sell_out_of_stock')
                ->help('Allow products from this vendor to be sold even if they are out of stock.'),
            DependencyContainer::make([
                Number::make('Out of Stock Delivery Days', 'out_of_stock_delivery_days')
                    ->help('Set the number of days it takes to deliver out-of-stock products from this vendor.')
                ]
            )->dependsOn('enable_sell_out_of_stock', true),

            Number::make('Products', '')
                ->resolveUsing(function () {
                    return DB::table('products')->select('vendor_id')->where('vendor_id', $this->id)->count();
                })->exceptOnForms(),


            Froala::make('Description')
                ->hideFromIndex(),

            Images::make('Images')
                ->conversionOnIndexView('thumb')
                ->conversionOnDetailView('thumb'),
            HasMany::make('Products'),
            MorphToMany::make('Followers', 'customers', '\App\Nova\Customer')
        ];
    }

    public function actions(NovaRequest $request): array
    {
        return [
            new DownloadExcel(),
        ];
    }
}
