<?php

namespace App\Nova;

use App\Nova\Filters\InventorySearch;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class InventoryTrack extends Resource
{
    public static $displayInNavigation = false;

    public static string $model = \App\InventoryTrack::class;

    public static $title = 'id';

    public static $search = [
        'id',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            DateTime::make('Recieved at', 'created_at')->displayUsing(function ($date) {
                return $date ? $date->format('m/d/Y h:i A') : null;
            })->sortable(),
            Text::make('sku'),
            Text::make('type')->sortable(),
            Text::make('Store Quantity', 'quantity')->sortable(),
            Text::make('Online Quantity')->sortable(),
            Text::make('Cost Price', 'result')->resolveUsing(function ($value) {
                $value = forceArray($value);
                return '$' . data_get($value, 'CostPrice');
            }),
            Text::make('Store Price', 'result')->resolveUsing(function ($value) {
                $value = forceArray($value);
                return '$' . data_get($value, 'StorePrice');
            }),
        ];
    }

    public function filters(NovaRequest $request): array
    {
        return [
            (new InventorySearch)->canSee(function () {
                return true;
            })
        ];
    }

}
