<?php

namespace App\Nova;

use Capitalc\ProductSearch\ProductSearch;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Capitalc\Select2\Select2;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Http\Requests\NovaRequest;

class SubscriptionGroup extends Resource
{
    use OrderFulfiller;

    public static string $model = \App\SubscriptionGroup::class;

    public static $title = 'name';

    public static $search = [
        'name',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')->sortable()
                ->rules('required'),

            Select2::make('Filter')
                ->rules('required')
                ->onlyOnForms(),

            ProductSearch::make('Products', 'formatted_products')
                ->onlyOnForms()
                ->withMeta([
                    'showQuantity' => false,
                ]),

            HasMany::make('Combinations'),

        ];
    }
}
