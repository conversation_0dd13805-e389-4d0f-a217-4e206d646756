<?php

namespace App\Nova;

use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Select;
use Capitalc\Creators\Creators;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class ShippingOption extends Resource
{
    use SuperAdmin;

    public static string $model = \App\ShippingOption::class;

    public static function label(): string
    {
        return 'Shipping Option';
    }

    public static $title = 'visible_name';

    public static $search = [
        'visible_name',
        'internal_name',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Internal Name'),

            Text::make('Visible Name'),

            Select::make('Delivery or Pickup', 'delivery')
                ->options([
                    1 => 'Delivery',
                    0 => 'Pickup',
                ]),
            Creators::make('Operational Days', 'operational_days')
                ->withMeta([
                    'extraAttributes' => [
                        'label' => 'Operational Days',
                        'type' => '',
                        'availableResources' => collect([
                            ['value' => '0', 'display' => 'Sunday'],
                            ['value' => '1', 'display' => 'Monday'],
                            ['value' => '2', 'display' => 'Tuesday'],
                            ['value' => '3', 'display' => 'Wedsnday'],
                            ['value' => '4', 'display' => 'Thursday'],
                            ['value' => '5', 'display' => 'Friday'],
                            ['value' => '6', 'display' => 'Saturday'],
                        ])
                    ]
                ])
                ->onlyOnForms(),
        ];
    }
}
