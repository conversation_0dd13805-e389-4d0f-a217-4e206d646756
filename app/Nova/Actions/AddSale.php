<?php

namespace App\Nova\Actions;

use <PERSON><PERSON>zel\DependencyContainer\DependencyContainer;
use App\Jobs\ProcessAddSale;
use App\Product;
use Capitalc\Checkbox\Checkbox;
use Capitalc\Currencyfield\Currencyfield;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\Heading;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class AddSale extends Action
{
    // public static $chunkCount = 100;
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $saleData = [
            'add_sale' => $fields->add_sale,
            'sale_type' => $fields->sale_type,
            'sale_amount' => $fields->sale_amount,
            'sale_from' => $fields->sale_from,
            'start_sale' => $fields->start_sale,
            'end_sale' => $fields->end_sale,
        ];

        $productIds = $models->pluck('id')->toArray();

        ProcessAddSale::dispatch($saleData, $productIds);

        $message = "Sale processing has been queued for all products.";

        return Action::message($message);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Heading::make(
                'Sale price will apply to all selected products and all its variations. This action will override all online sale pricing on all selected products and variations.'
            ),

            Checkbox::make('Online Sale Price', 'add_sale')
                ->hideFromIndex()
                ->withMeta(['value' => true]),
            DependencyContainer::make([
                Select::make('Sale Type')->options((new Product)->saleOptions),

                DependencyContainer::make([
                    Currencyfield::make('Percent', 'sale_amount')
                        ->withMeta(['side' => 'right', 'symbol' => '%']),
                    Select::make('Deduct From', 'sale_from')->options([
                        'list_price' => 'List Price',
                        'store_price' => 'Store Price',
                        'online_price' => 'Online Price',
                        'cost_price' => 'Cost Price',
                        'sale_price' => 'Sale Price',
                    ]),
                ])->dependsOn('sale_type', 'percent'),

                DependencyContainer::make([
                    Currencyfield::make('Sale Price', 'sale_amount')
                ])->dependsOn('sale_type', 'fixed'),

                Date::make('Start Sale'),
                Date::make('End Sale'),
            ])->dependsOn('add_sale', true)->hideFromIndex(),
        ];
    }
}
