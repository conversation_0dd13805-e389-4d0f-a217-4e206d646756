<?php

namespace App\Nova\Actions;

use App\Product;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON><PERSON>\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Fields\Country;
use <PERSON>vel\Nova\Fields\Heading;
use Laravel\Nova\Http\Requests\NovaRequest;

class AddCountryOrigin extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            $model->withoutEvents(function () use ($model, $fields) {
                $model->update(['origin' => $fields->origin]);
            });
            foreach ($model->variationInfos as $variationInfo) {
                $variationInfo->withoutEvents(function () use ($variationInfo, $fields) {
                    $variationInfo->update(['origin' => $fields->origin]);
                });
            }
            Cache::forget("product_{$model->id}");
        }
        $updateIds = Product::setEagerLoads([])->isSearchable()->whereIn('id', $models->pluck('id'))->get(['id']
        )->map->id;

        Product::find($updateIds)->each->updateSearchFeild();

        $ids = collect(json_decode(settings()->getValue('swiftype_update_ids')));
        settings()->setValue('swiftype_update_ids', json_encode($ids->push($updateIds)->flatten()->values()->unique()));
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Heading::make(
                'Country Origin will apply to all selected products and all its variations. This action will override all Country Origins on all selected products and variations.'
            ),
            Country::make('Country Origin', 'origin')
                ->hideFromIndex(),
        ];
    }
}
