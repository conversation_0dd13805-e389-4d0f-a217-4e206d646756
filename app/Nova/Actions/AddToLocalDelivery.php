<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class AddToLocalDelivery extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        \App\Order::whereIn('id', $models->modelKeys())->update([
            'shipping->shippingType->name' => 'Local Delivery'
        ]);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(\Laravel\Nova\Http\Requests\NovaRequest $request)
    {
        return [];
    }
}
