<?php

namespace App\Nova\Actions;

use Capitalc\Checkbox\Checkbox;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class MarkAsPhotographed extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $photographed = $fields->photographed;
        $models->each(function ($model) use ($photographed) {
            $model->withOutEvents(function () use ($model, $photographed) {
                $model->update(['not_photographed' => $photographed]);
            });
        });
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Checkbox::make('Not Photographed')
        ];
    }
}
