<?php

namespace App\Nova\Actions;

use App\Http\Controllers\RouteForMeController;
use App\Route;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Route4Me\Address;
use Route4Me\OptimizationProblem;
use Route4Me\OptimizationProblemParams;
use Route4Me\Route4Me;
use Route4Me\RouteParameters;

class CreateRoute extends Action
{
    use InteractsWithQueue, Queueable;

    public static $chunkCount = 1000;

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        Route4Me::setApiKey('********************************');

        $addresses = [];

        if ($fields->start) {
            $start_address = RouteForMeController::geocodeAddress($fields->start);
            $addresses[] = Address::fromArray(
                array_merge(
                    collect($start_address)->only('lat', 'lng', 'address')->toArray(),
                    ['alias' => 'Start Address']
                )
            );
        }

        $route = Route::create();

        $models->each(function ($order) use (&$addresses, &$route) {
            $address = collect(data_get($order->shipping, 'route_4_me_address'))->only(
                    'lat',
                    'lng',
                    'address'
                )->toArray() + [
                    'alias' => $order->id,
                    'order_no' => $order->id,
                ];
            $route->orders()->attach([$order->id => collect($address)->only('lat', 'lng', 'address')->toArray()]);
            $addresses[] = Address::fromArray($address);


            // ADDED TO ROUTE
            $route_4_me_address = array_merge(
                data_get($order->shipping, 'route_4_me_address'),
                ['created_route' => true]
            );
            $order->shipping = array_merge(
                $order->shipping ?? [],
                ['route_4_me_address' => $route_4_me_address]
            );

            $order->withoutEvents(function () use ($order) {
                $order->save();
            });
        });


        if ($fields->end) {
            $end_address = RouteForMeController::geocodeAddress($fields->end);
            $addresses[] = Address::fromArray(
                array_merge(collect($end_address)->only('lat', 'lng', 'address')->toArray(), ['alias' => 'End Address'])
            );
        }

        $parameters = RouteParameters::fromArray(array(
            'route_name' => $name = $fields->name,
            'optimization_engine' => 1,
            'optimization_quality' => 3,
            'optimize' => 'Time',
            'disable_optimization' => false,
            // 'algorithm_type' => \Route4Me\Enum\AlgorithmType::ADVANCED_CVRP_TW,
            'algorithm_type' => 1,
        ));

        $optimizationParams = new OptimizationProblemParams;
        $optimizationParams->setAddresses($addresses);
        $optimizationParams->setParameters($parameters);

        $problem = OptimizationProblem::optimize($optimizationParams);
        $route_id = data_get(collect($problem), 'routes.0.route_id');

        $addresses = data_get(collect($problem), 'addresses');

        $orderRoutes = $route->orderRoutes;
        collect($addresses)->each(function ($address) use (&$orderRoutes) {
            optional($orderRoutes->firstWhere('order_id', data_get($address, 'order_no')))->update(
                ['route_destination_id' => data_get($address, 'route_destination_id')]
            );
        });

        $route->update(['route_id' => $route_id, 'name' => $name]);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        $address = settings()->getValue('store_address_line_1');
        if (!empty(settings()->getValue('store_address_line_2'))){
            $address .= ' ' . settings()->getValue('store_address_line_2');
        }
        $address .= ', ' . settings()->getValue('store_city') . ', ' . settings()->getValue('store_state') . ' ' . settings()->getValue('store_postal_code');

        return [
            Text::make('Route Name', 'name')->withMeta(['value' => 'Route: ' . now()->toDateString()]),
            Text::make('Start Address', 'start')
                ->default($address)
                ->help('Address, City, Two letter State Postalcode'),
            Text::make('End Address', 'end')->help('Address, City, Two letter State Postalcode'),
        ];
    }
}
