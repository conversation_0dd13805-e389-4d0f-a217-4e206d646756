<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class ProductExport extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $models
     * @return mixed
     */
    public $withoutConfirmation = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        //     dispatch(function() use($models) {
        //         (new FastExcel($this->getUsersOneByOne($models)))->export('storage/app/exports.csv');
        //         (new \App\Http\Controllers\Api\ReportGeneratorController)->create('Product Export');
        //    });
        return Action::message('Your export has started! When done, you will find it by exports');
    }

    public function getUsersOneByOne($models)
    {
        $eachChunk = 100;
        $productCount = $models->count();
        $timesToLoop = $productCount / $eachChunk;

        for ($i = 0; $i <= $timesToLoop; $i++) {
            $users = $models->skip($i * $eachChunk)->take($eachChunk);

            foreach ($users as $model) {
                yield [
                    'id' => $model->id,
                    'title' => $model->title,
                    'description' => $model->description,
                    'short desc' => $model->short_desc,
                    'heb title' => $model->heb_title,
                    'heb description' => $model->heb_description,
                    'heb short desc' => $model->heb_short_desc,
                    'list price' => $model->list_price,
                    'store price' => $model->store_price,
                    'online price' => $model->online_price,
                    'sale price' => $model->sale_price,
                    'store quantity' => $model->store_quantity,
                    'website quantity' => $model->website_quantity,
                    'barcode' => $model->barcode,
                    'sku' => $model->sku,
                    'vendor_sku' => $model->vendor_sku,
                    'width' => $model->width,
                    'height' => $model->height,
                    'length' => $model->length,
                    'weight' => $model->weight,
                    'origin' => $model->origin,
                    'system code' => $model->system_code,
                    'track inventory' => $model->track_inventory,
                    'vendor id' => $model->vendor_id,
                    'tax code' => $model->tax_code,
                    'publish' => $model->publish,
                    'visibility' => $model->visibility,
                    'exclude free shipping' => $model->exclude_free_shipping,
                ];
            }
            // yield [
            //     'ID' => $model->id,
            //     'SUB_TOTAL' => $model->sub_total,
            //     'TAX_AMOUNT' => $model->tax_amount,
            //     'SHIPPING_AMOUNT' => $model->shipping_amount,
            //     'DISCOUNT_AMOUNT' => $model->discount_amount ,
            //     'GRAND_TOTAL' => $model->grand_total ,
            //     'STATUS' => $model->status ,
            //     'PAYMENT STATUS' => $model->payment_status ,
            //     'CUSTOMER' => optional($model->customer)->name ,
            //     'SUBSCRIPTION' => $model->subscription ,
            //     'PRODUCT COUNT' => collect($model->products)->count(),
            // ];
        }
    }

    /**
     * Get the fields available on the action.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [];
    }
}
