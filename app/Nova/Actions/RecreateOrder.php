<?php

namespace App\Nova\Actions;

use App\Http\Controllers\Api\BagController;
use App\ZipCode;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Date;
use Laravel\Nova\Http\Requests\NovaRequest;

class RecreateOrder extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $date = $fields->date;
        $models->each(function ($subscription) use ($date) {
            $type = $subscription->subscriptionType;
            $group = $subscription->subscriptionGroup;
            $date = now()->parse($date);

            $nexts = $type->items()
                ->whereDate('date', $date)->get();

            if (!$nexts) {
                return;
            }

            $products = $type->groups->find($group->id)->items->map->model;

            $products = $products->map(function ($product) use ($nexts, $type) {
                if (!$product) {
                    return;
                }
                if ($product->product) {
                    $parent = $product->product;
                } else {
                    $parent = $product;
                }
                return $nexts->map(function ($next) use ($parent, $product, $type) {
                    if ($filter = $parent->filters->find($next->filter_item_id)) {
                        return [
                            'date' => $next->date,
                            'name' => $filter->name,
                            'product' => $product->front_end,
                            'filter_name' => $type->filter->name,
                        ];
                    }
                })->filter();
            })->flatten(1)->filter()->values();

            $products = collect($products)
                ->map(function ($product) use ($subscription) {
                    return ['quantity' => $subscription->quantity] + $product['product'];
                });

            $address = $subscription->address ?? $subscription->customer->address;
            $payment = $subscription->payment ?? $subscription->customer->payment;

            $totals = (new BagController)->GetBreakDown(request()->merge([
                'products' => $products,
                'address' => $address,
            ]));

            $local = optional(
                optional(
                    optional(
                        optional(
                            optional(
                                optional(
                                    optional(
                                        optional(ZipCode::getCode(optional($address->postal_code)))->shippingZone
                                    )->options
                                )->where('active', true)
                            )->map
                        )->shippingOption
                    )->values()
                )->pluck('visible_name')
            )->contains('Local Delivery');

            $bag = collect($totals['bag'])->map(function ($item) {
                if (collect(explode(',', settings()->getValue('skus_that_are_free_in_subscriptions')))->contains(
                    data_get($item, 'sku')
                )) {
                    $item['price'] = 0;
                    $item['total'] = 0;
                    $item['total_amount'] = 0;
                    $item['grand_total'] = 0;
                    $item['tax_amount'] = 0;
                }
                return $item;
            });
            $totals = [
                'sub_total' => $bag->sum('price'),
                'tax' => $bag->sum('tax_amount'),
                'shipping' => $totals['shipping'],
                'total' => $bag->sum('price') + $bag->sum('tax_amount'),
                'bag' => $bag
            ];

            return $subscription->customer->orders()->create([
                'sub_total' => $totals['sub_total'],
                'tax_amount' => $totals['tax'],
                'shipping_amount' => $totals['shipping'],
                'meta->subscription' => [
                    'id' => $subscription->id,
                    'type_name' => $subscription->subscriptiontype->name,
                    'name' => $subscription->subscriptiontype->name,
                    'type_id' => $subscription->subscriptiontype->id,
                    'group' => $subscription->subscriptionGroup->name,
                    'group_id' => $subscription->subscriptionGroup->id,
                    'date' => $date->toDateString(),
                ],
                'subscription' => true,
                'grand_total' => $totals['total'],
                'products' => $totals['bag'],
                'shipping' => [
                    'shippingInfo' => $subscription->shipping, //$address,
                    'shippingType' => $local ? [
                        'name' => 'Local Delivery',
                        'estimated_arrival' => now()->parse($date)->copy()->subWeek()->toDateTimeString()
                    ] :
                        ['estimated_arrival' => now()->parse($date)->copy()->subWeek()->toDateTimeString()],
                    'tracking' => null
                ],
                'payments' => [
                    'giftCard' => [],
                    'creditInfo' => $subscription->payments, //$payment,
                ],
                'payments->creditInfo->payment_type' => 'creditCard',
                'payment_status' => 'unpaid',
                'status' => 'unpaid',
                'product_ids' => $totals['bag']->pluck('product_id')->filter(),
                'recurring_type' => 'App\Subscription',
                'recurring_id' => $subscription->id,
            ]);
        });
        return Action::message('The order was created');
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Date::make('Date')->rules('required')
        ];
    }
}
