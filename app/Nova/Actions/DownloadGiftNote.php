<?php

namespace App\Nova\Actions;

use Barryvdh\Snappy\Facades\SnappyPdf;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Actions\ActionResponse;
use <PERSON><PERSON>\Nova\Fields\ActionFields;

class DownloadGiftNote extends Action
{
    use InteractsWithQueue, Queueable;

    public $withoutConfirmation = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        $giftNote = $models->first();

        $content = explode("\n", $giftNote->occasionText);
        $order_sku = $giftNote->order_id . ' - ' . 'Item Number ' . $giftNote->sku;
        $img = optional($giftNote->giftNotesSetting)->picture;

        $data = [
            'content' => $content,
            'text_direction' => $giftNote->textDirection,
            'order' => $order_sku,
            'img' => $img
        ];
        $giftNote->update([
            'downloaded' => true
        ]);
        $filePath = public_path("/pdfs/giftnote.pdf");

        if (file_exists($filePath)) {
            unlink($filePath);
        }

        $pdf = SnappyPdf::loadView('gift_notes.gift_note', $data)
            ->setOption('page-width', 101.6)
            ->setOption('page-height', 152.4);
        $pdf->save($filePath);

        return ActionResponse::download("Gift Note Order #{$giftNote->order_id}.pdf", url("/pdfs/giftnote.pdf"));
    }
}
