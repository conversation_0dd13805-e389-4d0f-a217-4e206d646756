<?php

namespace App\Nova\Actions;

use App\Tax;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class ApplyTaxCode extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $models->each->update(['tax_code' => $fields->tax_code]);
        // foreach ($models as $model) {
        //     $model->update(['tax_code' => $fields->tax_code]);
        // }
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Select::make('Tax Code')->options(Tax::$options),
        ];
    }
}
