<?php

namespace App\Nova\Actions;

use App\Http\Controllers\Api\ReportGeneratorController;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use Rap2hpoutre\FastExcel\FastExcel;


class CustomerExport extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $models
     * @return mixed
     */

    public $withoutConfirmation = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        dispatch(function () use ($models) {
            (new FastExcel($this->getUsersOneByOne($models)))->export('storage/app/exports.csv');
            (new ReportGeneratorController)->create('Customer Export');
        });
        return Action::message('Your export has started! When done, you will find it by exports');
    }

    public function getUsersOneByOne($models)
    {
        foreach ($models as $model) {
            $address = $model->address;

            yield [
                'ID' => $model->id,
                'NAME' => $model->name,
                'EMAIL' => $model->email,
                'PHONE' => data_get($address, 'phone'),
                'ADDRESS LINE 1' => data_get($address, 'address_line_1'),
                'ADDRESS LINE 2' => data_get($address, 'address_line_2'),
                'CITY' => data_get($address, 'city'),
                'STATE' => data_get($address, 'state'),
                'POSTAL CODE' => data_get($address, 'postal_code'),
                'COUNTRY' => data_get($address, 'country'),
            ];
        }
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [];
    }
}
