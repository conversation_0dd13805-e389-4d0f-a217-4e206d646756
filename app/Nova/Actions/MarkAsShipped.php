<?php

namespace App\Nova\Actions;

use App\Http\Controllers\ShippingController;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class MarkAsShipped extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        collect($models)->each(function ($model) {
            $request = [
                'SS-UserName' => env('SHIP_STATION_USERNAME'),
                'SS-Password' => env('SHIP_STATION_PASSWORD'),
                'order_number' => $model->id,
                'carrier' => 'Other',
                'service' => '',
                'tracking_number' => '',
                'source' => 'nova'
            ];
            (new ShippingController)->post(request()->merge($request));
        });
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [];
    }
}
