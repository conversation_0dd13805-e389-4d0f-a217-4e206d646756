<?php

namespace App\Nova\Actions;

use App\Http\Controllers\Api\ReportGeneratorController;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Rap2hpoutre\FastExcel\FastExcel;

class OrderExport extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $models
     * @return mixed
     */
    public $withoutConfirmation = true;

    public function handle(ActionFields $fields, Collection $models)
    {
        dispatch(function () use ($models) {
            (new FastExcel($this->getUsersOneByOne($models)))->export('storage/app/exports.csv');
            (new ReportGeneratorController)->create('Order Export');
        });
        return Action::message('Your export has started! When done, you will find it by exports');
    }

    public function getUsersOneByOne($models)
    {
        foreach ($models as $model) {
            yield [
                'ID' => $model->id,
                'SUB_TOTAL' => $model->sub_total,
                'TAX_AMOUNT' => $model->tax_amount,
                'SHIPPING_AMOUNT' => $model->shipping_amount,
                'DISCOUNT_AMOUNT' => $model->discount_amount,
                'GRAND_TOTAL' => $model->grand_total,
                'STATUS' => $model->status,
                'PAYMENT STATUS' => $model->payment_status,
                'CUSTOMER' => optional($model->customer)->name,
                'SUBSCRIPTION' => $model->subscription,
                'PRODUCT COUNT' => collect($model->products)->count(),
            ];
        }
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [];
    }
}
