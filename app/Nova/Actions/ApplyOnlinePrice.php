<?php

namespace App\Nova\Actions;

use <PERSON><PERSON>zel\DependencyContainer\DependencyContainer;
use App\Product;
use Capitalc\Currencyfield\Currencyfield;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Heading;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class ApplyOnlinePrice extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            if ($fields->add_online_price == 'set') {
                $model->onlinePrice()->delete();
                $model->withoutEvents(function () use ($model, $fields) {
                    $model->update(['online_price' => $fields->online_price]);
                });
            } else {
                $model->onlinePrice()->updateOrCreate(['model_type' => 'App\Product', 'model_id' => $model->id], [
                    'percent' => $fields->online_price_percent,
                    'based_on' => $fields->online_price_based_on,
                ]);
            }
            foreach ($model->variationInfos as $variationInfo) {
                if ($fields->add_online_price == 'set') {
                    $variationInfo->onlinePrice()->delete();
                    $variationInfo->withoutEvents(function () use ($variationInfo, $fields) {
                        $variationInfo->update(['online_price' => $fields->online_price]);
                    });
                } else {
                    $variationInfo->onlinePrice()->updateOrCreate(
                        ['model_type' => 'App\VariationInfo', 'model_id' => $variationInfo->id],
                        [
                            'percent' => $fields->online_price_percent,
                            'based_on' => $fields->online_price_based_on,
                        ]
                    );
                }
            }
            Cache::forget("product_{$model->id}");
        }

        $updateIds = Product::setEagerLoads([])->isSearchable()->whereIn('id', $models->pluck('id'))->get(['id']
        )->map->id;

        Product::find($updateIds)->each->updateSearchFeild();

        $ids = collect(json_decode(settings()->getValue('swiftype_update_ids')));
        settings()->setValue('swiftype_update_ids', json_encode($ids->push($updateIds)->flatten()->values()->unique()));
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Heading::make(
                'Online price will apply to all selected products and all its variations. This action will override all online pricing on all selected products and variations.'
            ),
            Select::make('Online Price', 'add_online_price')->options(['set' => 'Set Price', 'rules' => 'Pricing Rule']
            ),

            DependencyContainer::make([
                Select::make('Based On', 'online_price_based_on')->options([
                    'list_price' => 'List Price',
                    'store_price' => 'Store Price',
                    'online_price' => 'Online Price',
                    'cost_price' => 'Cost Price',
                    'sale_price' => 'Store Sale Price',
                ]),
                Currencyfield::make('Percent', 'online_price_percent')
                    ->withMeta(['side' => 'right', 'symbol' => '%']),
            ])->dependsOn('add_online_price', 'rules'),
            DependencyContainer::make([
                Currencyfield::make('Online Price')
                    ->hideFromIndex(),
            ])->dependsOn('add_online_price', 'set'),
        ];
    }
}
