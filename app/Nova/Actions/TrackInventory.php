<?php

namespace App\Nova\Actions;

use Capitalc\Checkbox\Checkbox;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class TrackInventory extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $models->each(function ($product) use ($fields) {
            $product->update([
                'track_inventory' => !!$fields->track_inventory
            ]);
            if ($product->has('variationInfos')) {
                $product->variationInfos->each->update([
                    'track_inventory' => !!$fields->track_inventory
                ]);
            }
        });
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Checkbox::make('Track Inventory'),
        ];
    }
}
