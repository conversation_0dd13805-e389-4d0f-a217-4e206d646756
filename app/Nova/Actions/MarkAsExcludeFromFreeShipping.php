<?php

namespace App\Nova\Actions;

use App\ShippingOption;
use Capitalc\Checkbox\Checkbox;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;


class MarkAsExcludeFromFreeShipping extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $models->each->update(['exclude_free_shipping' => !!$fields->exclude]);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Checkbox::make('Exclude From Free Shipping', 'exclude')->help(
                '<p>Does not apply to '
                . implode(
                    ' & ',
                    ShippingOption::find(
                        explode(',', settings()->getValue('exclude_from_free_shipping_does_not_apply_to'))
                    )->map->internal_name->toArray()
                )
                . ' '
                . '<a class="dim" href="/admin/resources/settings/44/edit">Settings</a></p>'
            )
        ];
    }
}
