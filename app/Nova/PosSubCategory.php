<?php

namespace App\Nova;

use Capitalc\Currencyfield\Currencyfield;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class PosSubCategory extends Resource
{
    public static $group = 'POS';
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = 'App\PosSubCategory';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
        'parent',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Parent')
                ->withMeta(['readonly' => true]),

            Text::make('Name')
                ->withMeta(['readonly' => true]),

            Currencyfield::make('Percent Increase', 'increase')
                ->withMeta(['side' => 'right', 'symbol' => '%']),

            Number::make('Amount Of Products', 'products_count')
                ->fillUsing(function(NovaRequest $request, $model, $attribute, $requestAttribute) {
                    return null;
                })
                ->withMeta(['readonly' => true]),

            BelongsTo::make('posCategory')
                ->withMeta(['readonly' => true]),
            HasMany::make('Products'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
