<?php

namespace App\Nova;

use Capitalc\Checkbox\Checkbox;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class ShippingOptionShippingZone extends Resource
{
    public static $displayInNavigation = false;

    public static string $model = \App\ShippingOptionShippingZone::class;

    public static $title = 'id';

    public static function label(): string
    {
        return 'Price & Duration';
    }

    public static $search = [
        'id',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
            BelongsTo::make('shippingOption'),
            BelongsTo::make('shippingZone'),

            Number::make('Price Per Pound'),
            Number::make('Base Rate'),
            Number::make('Duration'),
            Checkbox::make('Duration Is Hourly'),
            Number::make('Free Shipping Above'),
        ];
    }
}
