<?php

namespace App\Nova;

use App\GiftCardTransaction;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Transaction extends Resource
{
    public static $displayInNavigation = false;

    public static string $model = GiftCardTransaction::class;

    public static $title = 'id';

    public static $search = [
        'id',
        'order_id',
        'amount',
        'type'
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
            BelongsTo::make('Order'),
            Text::make('Amount')->resolveUsing(function ($value) {
                return '$' . $value;
            }),
            Text::make('Type'),
            Datetime::make('Created At')->displayUsing(function ($date) {
                return $date ? $date->format('m/d/Y h:i A') : null;
            }),
            Text::make('Pos Message'),
        ];
    }
}
