<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;

class GiftNotesSetting extends Resource
{
    use SuperAdmin;

    public static string $model = \App\GiftNotesSetting::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
        'note',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')->rules('max:255'),

            Textarea::make('Note')
                ->hideFromIndex()
                ->alwaysShow()
                ->help('To get the users name insert {{name}}')
                ->rules('nullable'),

            Images::make('Image', 'image'),

        ];
    }
}
