<?php

namespace App\Nova;

use Capitalc\Checkbox\Checkbox;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>\Nova\Fields\BelongsToMany;
use <PERSON>vel\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;
use NovaAttachMany\AttachMany;

class Category extends Resource
{
    use ProductLister;

    public static string $model = \App\Category::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->rules('required', 'min:2'),

            Number::make('Products', '')
                ->resolveUsing(function () {
                    return DB::table('category_product')->select('category_id')->where(
                        'category_id',
                        $this->id
                    )->count();
                })->exceptOnForms(),

            Images::make('Media')
                ->croppingConfigs(['ratio' => 4 / 3])
                ->conversionOnIndexView('thumb')
                ->conversionOnDetailView('thumb'),
            BelongsToMany::make('Filters'),
            AttachMany::make('Filters')
                ->height('250px'),

            Date::make('Schedule', 'start')
                ->hideFromIndex()
                ->rules('date', 'nullable') //, 'before:end'
                ->sortable(),

            CheckBox::make('Search Query Redirect', 'redirect')
                ->help(
                    "When set as active, search queries that match " . $this->help_name(
                    ) . " will be redirected to the " . $this->help_name() . " page"
                ),

            BelongsToMany::make('Products')
                ->fields(function ($request, $relatedModel) {
                    return [
                        Text::make('Product Actions', function () use ($relatedModel) {
                            return '<a href="/admin/resources/products/' . $relatedModel->id . '/edit" target="_blank" class="link-default">Edit Product</a>';
                        })->asHtml()
                    ];
                })
                ->searchable(),

            Select::make('Default Sort By', 'sort_by')->options([
                'price-asc' => 'Price: Low to High',
                'price-desc' => 'Price: High to Low',
                'release_date-desc' => 'Newest'
            ])
                ->displayUsingLabels()
                ->nullable()
                ->onlyOnForms(),
            Text::make('SEO Title', 'seo_title')
                ->onlyOnForms()
                ->help('Will use default title, if left empty'),
            Textarea::make('SEO Description', 'seo_desc')
                ->onlyOnForms()
                ->help('Will use default description from settings, if left empty'),
        ];
    }

    public function help_name(): string
    {
        return $this->name ? "'" . $this->name . "'" : 'categories name';
    }
}
