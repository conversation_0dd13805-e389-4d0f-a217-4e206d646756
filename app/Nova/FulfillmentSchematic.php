<?php

namespace App\Nova;

use Capitalc\Currencyfield\Currencyfield;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class FulfillmentSchematic extends Resource
{
    use SuperAdmin;

    public static string $model = \App\FulfillmentSchematic::class;

    public static $title = 'name';

    public static $search = [];

    public static function label(): string
    {
        return 'Fulfillment';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            Text::make('Name')->readonly(),

            Currencyfield::make('Cutoff')
                ->withMeta(['side' => 'right', 'symbol' => 'hour'])
                ->rules('nullable'),

            Text::make('Sunday Open')->onlyOnForms(),
            Text::make('Sunday Close')->onlyOnForms(),

            Text::make('Monday Open')->onlyOnForms(),
            Text::make('Monday Close')->onlyOnForms(),

            Text::make('Tuesday Open')->onlyOnForms(),
            Text::make('Tuesday Close')->onlyOnForms(),

            Text::make('Wednesday Open')->onlyOnForms(),
            Text::make('Wednesday Close')->onlyOnForms(),

            Text::make('Thursday Open')->onlyOnForms(),
            Text::make('Thursday Close')->onlyOnForms(),

            Text::make('Friday Open')->onlyOnForms(),
            Text::make('Friday Close')->onlyOnForms(),

            Text::make('Saturday Open')->onlyOnForms(),
            Text::make('Saturday Close')->onlyOnForms(),
        ];
    }
}
