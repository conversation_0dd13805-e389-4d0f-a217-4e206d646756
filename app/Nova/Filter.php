<?php

namespace App\Nova;

use App\Nova\Flexible\Resolvers\FilterItems;
use Capitalc\Checkbox\Checkbox;
use Illuminate\Support\Facades\DB;
use <PERSON>vel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Outl1ne\NovaSortable\Traits\HasSortableRows;
use Whitecube\NovaFlexibleContent\Flexible;

class Filter extends Resource
{
    use ProductLister, HasSortableRows;

    public static string $model = \App\Filter::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
        'info',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->rules('required'),

            Text::make('Information', 'info')
                ->nullable()
                ->onlyOnForms(),

            Checkbox::make('Show In Filters', 'show')->onlyOnForms(),
            Checkbox::make('Show In Assistant', 'assistant')->onlyOnForms(),

            BelongsToMany::make('Categories'),

            Flexible::make('Items')
                ->resolver(FilterItems::class)
                ->addLayout('Filter Items', 'items', [
                    Text::make('Name'),
                    Text::make('Items')->withMeta([
                        'extraAttributes' => [
                            'readonly' => true
                        ]
                    ]),
                ])
                ->button('Add Item')->hideFromIndex(),


            Text::make('Items', 'id')->onlyOnIndex()->resolveUsing(function ($id) {
                $filter_item_ids = DB::table('filter_items')->where('filter_id', $id)->select('id')->get()->pluck(
                    'id'
                );
                return DB::table('filter_item_product')->whereIn('filter_item_id', $filter_item_ids)->count();
            })
        ];
    }
}
