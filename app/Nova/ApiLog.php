<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Code;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Resource;

class ApiLog extends Resource
{
    public static string $model = \App\ApiLog::class;

    public static $title = 'unique_identifier';

    public static $search = [
        'id', 'url', 'provider', 'tag', 'unique_identifier'
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Method')->sortable()->nullable(),

            Text::make('URL', 'url')->sortable()->hideFromIndex(),

            Code::make('Request')->json()->nullable()->hideFromIndex(),

            Code::make('Response')->json()->resolveUsing(function () {
                return json_encode($this->response, JSON_PRETTY_PRINT);
            })->nullable()->hideFromIndex(),

            Text::make('Provider')->sortable()->nullable(),

            Text::make('Tag')->sortable()->nullable(),

            Text::make('Unique Identifier')->sortable()->readonly(),

            Number::make('Status Code')->sortable()->nullable(),

            DateTime::make('Created At')->onlyOnDetail(),

            DateTime::make('Updated At')->onlyOnDetail(),
        ];
    }

    public static function authorizedToCreate(Request $request): bool
    {
        return false;
    }

    public function authorizedToUpdate(Request $request): bool
    {
        return false;
    }

    public function authorizedToDelete(Request $request): bool
    {
        return false;
    }
}
