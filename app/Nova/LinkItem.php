<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class LinkItem extends Resource
{
    public static string $model = \App\LinkItem::class;

    public static $displayInNavigation = false;

    public static $title = 'id';
    public static $search = [
        'id',
    ];

    public static function label(): string
    {
        return 'Link Items';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
        ];
    }
}
