<?php

namespace App\Nova;

use Illuminate\Http\Request;


trait ProductViewer
{

    public static function authorizedToViewAny(Request $request)
    {
        $user = auth('admin')->user();
        return $user && in_array($user->role, ['Super Admin', 'Product Lister', 'Product Viewer']);
    }

    public function authorizedToView(Request $request)
    {
        return true;
    }

    public function authorizedToUpdate(Request $request)
    {
        $user = auth('admin')->user();
        return $user && in_array($user->role, ['Super Admin', 'Product Lister', 'Product Viewer']);
    }

    public function authorizedToDelete(Request $request)
    {
        $user = auth('admin')->user();
        return $user && in_array($user->role, ['Super Admin', 'Product Lister', 'Product Viewer']);
    }

    public static function authorizedToCreate(Request $request)
    {
        $user = auth('admin')->user();
        return $user && in_array($user->role, ['Super Admin', 'Product Lister', 'Product Viewer']);
    }
}
