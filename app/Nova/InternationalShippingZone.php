<?php

namespace App\Nova;

use Laravel\Nova\Fields\Heading;
use Capitalc\InternationPriceDuration\InternationPriceDuration;
use <PERSON>vel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Capitalc\Creators\Creators;
use App\Http\Controllers\CurrencyController;
use App\Http\Controllers\Api\InternationalRatesController;
use Laravel\Nova\Http\Requests\NovaRequest;

class InternationalShippingZone extends Resource
{
    use SuperAdmin;

    public static string $model = \App\InternationalShippingZone::class;

    public static function label(): string
    {
        return 'International Shipping Zone';
    }

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public function fields(NovaRequest $request): array
    {
        return array_merge(
            [
                ID::make()->sortable(),

                Text::make('Name')->rules('required'),

                Creators::make('Countries', 'countries_text')
                    ->withMeta([
                        'extraAttributes' => [
                            'hide' => false,
                            'label' => 'Countries',
                            'type' => '',
                            'availableResources' => CurrencyController::GetAllCountries(false, $this->id)->map(
                                function ($country) {
                                    return ['display' => $country['label'], 'value' => $country['value']];
                                }
                            ),
                        ]
                    ])
                    ->hideFromIndex()
                    ->help('Select countries that are not applied to a different shipping zone.')->rules('required'),

            ],
            $this->priceDuration()
        );
    }

    protected function priceDuration()
    {
        $carriers = InternationalRatesController::GetAllCarriers()->map(function ($carrier) {
            $carrier['services'] = collect($carrier['services'])->map(function ($service) {
                return [
                    'label' => $service['service_name'],
                    'value' => [
                        'label' => $service['service_name'],
                        'value' => $service
                    ]
                ];
            });

            return [
                'label' => $carrier['carrier_name'],
                'value' => [
                    'label' => $carrier['carrier_name'],
                    'value' => $carrier
                ]
            ];
        });

        return \App\ShippingOption::all()->map(function ($item) use ($carriers) {
            return [
                Heading::make($item->visible_name)->onlyOnForms(),
                InternationPriceDuration::make('Active', 'price_duration')
                    ->withMeta([
                        'setting' => $item,
                        'carriers' => $carriers
                    ])
                    ->onlyOnForms()
            ];
        })->flatten(1)->all();
    }
}
