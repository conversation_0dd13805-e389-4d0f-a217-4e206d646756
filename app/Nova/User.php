<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use Laravel\Nova\Http\Requests\NovaRequest;

class User extends Resource
{
    public static $displayInNavigation = false;

    public static string $model = \App\User::class;

    public static $title = 'id';

    public static $search = [
        'id',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
        ];
    }
}
