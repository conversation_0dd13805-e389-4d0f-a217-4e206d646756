<?php

namespace App\Classes;

use App\ProductCollection;
use Outl1ne\MenuBuilder\MenuItemTypes\BaseMenuItemType;

class ProductCollections extends BaseMenuItemType
{
    public static function getIdentifier(): string
    {
        return 'ProductCollection';
    }

    public static function getName(): string
    {
        return 'Product Collection';
    }

    public static function getType(): string
    {
        return 'select';
    }

    public static function getOptions($locale): array
    {
        return ProductCollection::get(['name', 'id'])->pluck('name', 'id')->toArray();
    }

    public static function getDisplayValue($value, ?array $data, $locale)
    {
        return 'Product Collection: ' . optional(ProductCollection::find($value))->name;
    }

    public static function getValue($value, ?array $data, $locale)
    {
        return optional(ProductCollection::find($value))->path;
    }
}
