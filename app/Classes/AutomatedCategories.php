<?php

namespace App\Classes;

use Outl1ne\MenuBuilder\MenuItemTypes\BaseMenuItemType;
use App\AutomatedCategory;

class AutomatedCategories extends BaseMenuItemType
{
    public static function getIdentifier(): string
    {
        return 'AutomatedCategory';
    }

    public static function getName(): string
    {
        return 'Automated Category';
    }

    public static function getType(): string
    {
        return 'select';
    }

    public static function getOptions($locale): array
    {
        return AutomatedCategory::get(['name', 'id'])->pluck('name', 'id')->toArray();
    }

    public static function getDisplayValue($value, ?array $data, $locale)
    {
        return 'Automated Category: ' . optional(AutomatedCategory::find($value))->name;
    }

    public static function getValue($value, ?array $data, $locale)
    {
        return optional(AutomatedCategory::find($value))->path;
    }
}
