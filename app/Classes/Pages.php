<?php

namespace App\Classes;

use Outl1ne\MenuBuilder\MenuItemTypes\BaseMenuItemType;
use App\Page;

class Pages extends BaseMenuItemType
{
    public static function getIdentifier(): string
    {
        return 'page';
    }

    public static function getName(): string
    {
        return 'Page';
    }

    public static function getType(): string
    {
        return 'select';
    }

    public static function getOptions($locale): array
    {
        return Page::get(['name', 'id'])->pluck('name', 'id')->toArray();
    }

    public static function getDisplayValue($value, ?array $data, $locale)
    {
        return 'Page: ' . optional(Page::find($value))->name;
    }

    public static function getValue($value, ?array $data, $locale)
    {
        return optional(Page::find($value))->path;
    }
}
