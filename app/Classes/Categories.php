<?php

namespace App\Classes;

use App\Category;
use Outl1ne\MenuBuilder\MenuItemTypes\BaseMenuItemType;

class Categories extends BaseMenuItemType
{
    public static function getIdentifier(): string
    {
        return 'category';
    }

    public static function getName(): string
    {
        return 'Category';
    }

    public static function getType(): string
    {
        return 'select';
    }

    public static function getOptions($locale): array
    {
        return Category::get(['name', 'id'])->pluck('name', 'id')->toArray();
    }

    public static function getDisplayValue($value, ?array $data, $locale)
    {
        return 'Category: ' . optional(Category::find($value))->name;
    }

    public static function getValue($value, ?array $data, $locale)
    {
        return optional(Category::find($value))->path;
    }
}
