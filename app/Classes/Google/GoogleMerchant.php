<?php

namespace App\Classes\Google;

use App\Product as Listing;
use Illuminate\Support\Facades\Storage;
use <PERSON><PERSON><PERSON><PERSON>\GoogleMerchant\Feed;
use <PERSON><PERSON><PERSON><PERSON>\GoogleMerchant\Product;
use <PERSON><PERSON><PERSON><PERSON>\GoogleMerchant\Product\Availability\Availability;
use <PERSON><PERSON><PERSON><PERSON>\GoogleMerchant\Product\Shipping;

class GoogleMerchant
{
    public static function setUpProducts()
    {
        $listings = Listing::shouldDisplay()->get();

        $feed = new Feed('Google Feed', storage_path('6e2a30a6-5394-442f-b911-53dcedd79ca8.xml'), 'Google feed');

        foreach ($listings as $listing) {
            $item = new Product();

            $sku = $listing?->inventory?->sku;

            $item->setId($sku . '-' . $listing->id ?? '');

            $item->setTitle($listing->title ?? '');

            $item->setDescription($listing->description ?? '');

            $item->setLink(config('app.front_end_url') . $listing->path ?? '');

            $item->setImage($listing->image ?? '');

            if ((bool)$listing->calculateStock()) {
                $item->setAvailability(Availability::IN_STOCK);
            } else {
                $item->setAvailability(Availability::OUT_OF_STOCK);
            }

            // Must be of google categories
            // https://www.google.com/basepages/producttype/taxonomy-with-ids.en-US.xls
            // $item->setGoogleCategory();

            //use breadcrumbs
            //https://support.google.com/merchants/answer/6324406?hl=en&ref_topic=6324338

            $breadCrumbs = collect($listing->breadCrumbs())->filter(function ($breadCrumb) {
                return data_get($breadCrumb, 'path');
            })->pluck('name')->join(' &gt; ');
            if ($breadCrumbs) {
                $item->setProductType('Home  &gt;' . $breadCrumbs);
            }

            $item->setBrand($listing->inventory?->brand?->name ?? '');

            $item->setGtin($listing->inventory?->barcode ?? '');

            $item->setMpn($listing?->inventory?->manufacture_sku);

            // if no gtin or sku or brand
            if (!$listing->inventory?->barcode && !$listing?->inventory?->sku && !$listing->inventory?->brand?->name) {
                $item->setIdentifierExists('no');
            }

            $item->setCondition('new');

            // Some additional properties

            // if multi select the we have to get the name of spec not id try listing 58
            // if ($listing->color) {
            //     $item->setColor($listing->color);
            // }

            // tax
                $item->setAttribute(
                    'tax_category',
                    collect(json_decode($listing?->inventory?->tax_code_info))->first()?->id
                ) ?? '';

            if (($listing->calculatedSalePrice(
                ) ?? $listing->listingSalePrice) && $listing->crossedOutPrice && ($listing->price < $listing->crossedOutPrice)) {
                $item->setPrice("{$listing->crossedOutPrice} USD");

                $item->setAttribute('sale_price', "{$listing->price} USD");
            } else {
                $item->setPrice("{$listing->price} USD");
            }

            // variation parent id
            if ($listing->isChild()) {
                $item->setAttribute('item_group_id', $listing->listing->id, false);
            }

            // shipping
            // if not default shipping template then send in the template name
            if ($listing->shippingTemplate?->id) {
                $item->setShippingLabel($listing->shippingTemplate?->name);
            }

            // returns
            if (!$listing->returnable) {
                $item->setCustomLabel('return_policy_label', 'nonreturnable');
            }
            if ($listing->free_returns) {
                $item->setCustomLabel('return_policy_label', 'free_returns');
            }

            // $item->setSize($listing->size);
            // $item->setMaterial($listing->material);

            // $pos is from 0-4
            // $str is the label
            // $item->setCustomLabel($str, $pos)

            // // Add this product to the feed
            $feed->addProduct($item);
        }

        $feedXml = $feed->build();

        Storage::disk('public')->put('6e2a30a6-5394-442f-b911-53dcedd79ca8.xml', $feedXml);
    }
}
