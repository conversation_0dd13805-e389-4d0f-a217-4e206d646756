<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\ValidationException;

class ShippingZone extends Model
{
    protected $fillable = ['name'];

    public function shippingOptions()
    {
        return $this->belongsToMany(ShippingOption::class);
    }

    public function zipCodes()
    {
        return $this->hasMany('App\ZipCode');
    }

    public function options()
    {
        return $this->hasMany(ShippingOptionShippingZone::class);
    }

    public function setZipCodesTextAttribute($value)
    {
        $this->processZipCodeShippingZones($value, $this->id);

        $this->attributes['zip_codes_text'] = $value;
    }

    public function getPriceDurationAttribute()
    {
        return $this->options;
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($zone) {
            $priceDuration = request()->price_duration;
            if (!is_array($priceDuration)) {
                return;
            }
            $collection = collect(array_map('forceArray', $priceDuration));

            $collection->each(function ($price) use ($zone) {
                $price['duration'] == ''
                    ? $price['duration'] = null
                    : $price['duration'] = $price['duration'];

                $price['base_rate'] == ''
                    ? $price['base_rate'] = null
                    : $price['base_rate'] = $price['base_rate'];

                $price['free_shipping_above'] == ''
                    ? $price['free_shipping_above'] = null
                    : $price['free_shipping_above'] = $price['free_shipping_above'];

                if ($item = $zone->options()->where(['shipping_option_id' => $price['shipping_option_id']])->first()) {
                    $item->update($price);
                } else {
                    $zone->options()->create($price);
                }
            });

            $zone->options()
                ->whereNotIn('shipping_option_id', $collection->pluck('shipping_option_id'))
                ->delete();

            // see ClearShippingCache.php
            cache()->store('file')
                ->flush('shipping_updated_at');

            cache()->store('file')
                ->remember('shipping_updated_at', 24 * 60, function () {
                    return now()->format('Y-m-d\TG:i:s');
                });
        });
    }

    public function setPriceDurationAttribute()
    {
        //     $collection = collect(array_map('forceArray', request()->price_duration));

        //     $collection->each(function ($price) {
        //         if ($item = $this->options()->where(['shipping_option_id' => $price['shipping_option_id']])->first()) {
        //             $item->update($price);
        //         } else {
        //             $this->options()->create($price);
        //         }
        //     });

        //     $this->options()
        //         ->whereNotIn('shipping_option_id', $collection->pluck('shipping_option_id'))
        //         ->delete();
    }


    private function processZipCodeShippingZones($zipCodes, $id)
    {
        if (preg_match_all("/[^-,\s\d]/", $zipCodes) > 0) {
            return response()->json(
                [
                    'success' => false,
                    'message' => 'Format of zip codes submission is incorrect; must be comma-delimited list of numbers with no line breaks'
                ]
            );
        }

        $allZips = collect(explode(",", str_replace(" ", "", $zipCodes)))
            ->each(function ($collection) {
                if (strpos($collection, '-') === false) {
                    return $collection;
                } else {
                    $range = explode("-", $collection);
                    collect(range($range[0], $range[1]))
                        ->map(function ($whatever) {
                            return $whatever;
                        });
                }
            })
            ->filter();
        $conflicts = ZipCode::whereIn('zip_code', $allZips)
            ->where('shipping_zone_id', '!=', $this->id)
            ->where('shipping_zone_id', '>', 0)->get();
        if ($conflicts->isNotEmpty()) {
            $string = $conflicts->map(function ($conflict) {
                return "{$conflict->zip_code} is used in {$conflict->shippingZone->name}";
            })->join(', ');


            abort(500, $string);
            throw ValidationException::withMessages([
                'zip_codes_text' => [$string],
            ]);
        }

        ZipCode::where('shipping_zone_id', $id)->update(['shipping_zone_id' => null]);

        $zipCodesIdentifiers = explode(",", str_replace(" ", "", $zipCodes));

        foreach ($zipCodesIdentifiers as $zipCodeIdentifier) {
            if (strpos($zipCodeIdentifier, '-') === false) {
                if (strlen($zipCodeIdentifier) > 3) {
                    ZipCode::firstOrCreate(['zip_code' => $zipCodeIdentifier])
                        ->update(['shipping_zone_id' => $id]);
                } else {
                    $formattedZipCode = $this->formatZipCode($zipCodeIdentifier);

                    ZipCode::where(['zip_code' => $formattedZipCode])
                        ->update(['shipping_zone_id' => $id]);
                }
            } else {
                $range = explode("-", $zipCodeIdentifier);

                for ($i = intval($range[0]); $i <= intval($range[1]); $i++) {
                    if (strlen($i) > 3) {
                        ZipCode::firstOrCreate(['zip_code' => $i])
                            ->update(['shipping_zone_id' => $id]);
                    } else {
                        $formattedZipCode = $this->formatZipCode($i);

                        ZipCode::where(['zip_code' => $formattedZipCode])
                            ->update(['shipping_zone_id' => $id]);
                    }
                }
            }
        }
        Zipcode::whereRaw('LENGTH(zip_code) > 3')->whereNull('shipping_zone_id')->delete();
    }

    private function formatZipCode($zipCode)
    {
        switch (strlen($zipCode)) {
            case 1:
                return '00' . $zipCode;
            case 2:
                return '0' . $zipCode;
            case 3:
                return $zipCode;
            default:
                return null;
        }
    }
}
