<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class InternationalShippingOptionShippingZone extends Model
{
    protected $table = 'international_shipping_option_shipping_zone';

    protected $guarded = [];

    public function shippingOption()
    {
        return $this->belongsTo(ShippingOption::class);
    }

    public function shippingZone()
    {
        return $this->belongsTo(InternationalShippingZone::class);
    }

    public function scopeActive($query)
    {
        return $query->whereActive(true);
    }
}
