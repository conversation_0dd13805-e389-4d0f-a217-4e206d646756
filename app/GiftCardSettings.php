<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class GiftCardSettings extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $guarded = [];
    protected $appends = [
        'path',
        'media_urls'
    ];

    public function getAmountAttribute($value)
    {
        return explode(',', $value);
    }

    public function getPathAttribute()
    {
        return "/gift_options/" . str_slug($this->name);
    }

    public function getMediaUrlsAttribute()
    {
        return $this->media->sortBy('order_column')->map(function ($item) {
            if ($item->collection_name == 'media') {
                return $item->getUrl();
                // return [
                //     'grid' => $item->getUrl('grid'),
                //     'large' => $item->getUrl('large'),
                //     'lightbox' => $item->getUrl('lightbox'),
                //     'thumbnail' => $item->getUrl('thumbnail'),
                // ];
            }
        })->filter()->values();
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->background('#ffffff')
            ->width(130)
            ->height(130);

        //     $this->addMediaConversion('thumbnail')
        //         ->width(100);

        //     $this->addMediaConversion('grid')
        //         ->width(250);

        //     $this->addMediaConversion('large')
        //         ->width(500);

        //     $this->addMediaConversion('lightbox')
        //         ->width(1500);

        //     // $this->addMediaConversion('thumb')
        //     //     ->width(368)
        //     //     ->height(232)
        //     //     ->extractVideoFrameAtSecond(20)
        //     //     ->performOnCollections('video_preview');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('media');
    }
}
