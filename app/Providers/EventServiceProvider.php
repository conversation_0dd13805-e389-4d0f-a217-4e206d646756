<?php

namespace App\Providers;

use App\Listeners\MediaLogger;
use App\Observers\ProductObserver;
use Illuminate\Support\Facades\Event;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        'Spatie\MediaLibrary\Events\MediaHasBeenAdded' => [
            MediaLogger::class
        ],
        \Illuminate\Http\Client\Events\RequestSending::class => [
            \App\Listeners\LogRequestSending::class,
        ],
        \Illuminate\Http\Client\Events\ResponseReceived::class => [
            \App\Listeners\LogResponseReceived::class,
        ],
    ];

    public function boot()
    {
        parent::boot();
        Event::listen('eloquent.saved: *Media', function ($eventName, array $data) {
            $media = $data[0];
            if ($media->order_column == 1) {
                $product = $media->model->setAppends([]);
                if (method_exists($product, 'getModelSwiftypeTransformed')) {
                    $product->withoutEvents(function () use ($product) {
                        $product->update([
                            'search' => $product->getModelSwiftypeTransformed()
                        ]);
                    });
                    (new ProductObserver)->updateSwiftype($product);
                }
            }
        });

        Event::listen('eloquent.saved: *Menu', function ($eventName, array $data) {
            refreshCache();
        });

        Event::listen('eloquent.saved: *MenuItem', function ($eventName, array $data) {
            refreshCache();
        });

        Event::listen('eloquent.deleted: *Menu', function ($eventName, array $data) {
            refreshCache();
        });

        Event::listen('eloquent.deleted: *MenuItem', function ($eventName, array $data) {
            refreshCache();
        });
    }
}
