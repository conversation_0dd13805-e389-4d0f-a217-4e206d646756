<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Group extends Model
{
    public function discounts()
    {
        return $this->morphMany(Discount::class, 'eligibility');
    }

    public function customers()
    {
        return $this->hasMany('App\Customer');
    }

    public function getDiscounts($min)
    {
        return $this->eligible()->active()->automated()->above($min)->get();
    }
    // public function getCustomersIdsAttribute() {
    //     return $this->customers->pluck('id');
    // }

    // public function setCustomersIdsAttribute($value) {
    //     return;
    // }
}
