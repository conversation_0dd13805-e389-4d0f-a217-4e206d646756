<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\Http\Controllers\ReturnLabelController;

class Returns extends Model
{
    protected $guarded = [];

    protected $appends = [
        // 'valid_until',
    ];

    public $casts = [
        'meta' => 'array',
        'products' => 'array',
        'shipping' => 'array',
        'payments' => 'array',
        'discount' => 'array',
        'product_ids' => 'array',
        'reversions' => 'array',
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function getCustomerAttribute()
    {
        return $this->order->customer;
    }

    public function getCustomerNameAttribute()
    {
        return $this->customer->name;
    }

    public function setCustomerNameAttribute()
    {
    }

    public function getPaymentIdAttribute()
    {
        return data_get($this, 'payments.creditInfo.payment_id');
    }

    public function getValidUntilAttribute()
    {
        return $this->order->return_by;
    }

    public function getShippingInfoAttribute()
    {
        return data_get($this->order, 'shipping.shippingInfo');
    }

    public function IsValid()
    {
        return $this->valid_until >= now();
    }

    public function TotalRefundedForGiftCard($id)
    {
        return collect(data_get($this, 'payments.giftCard'))->map(function ($card) use ($id) {
            if ($card['id'] == $id) {
                return $card['amount'];
            }
        })->sum();
    }

    public function markAsDelivered()
    {
        $order = $this;
        $order->update([
            'shipping_status' => 'delivered',
            'shipping->tracking->arrived_at' => now(),
            'shipping->tracking->status' => 'Delivered',
        ]);
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($return) {
            if (
                $return->getOriginal('dropoff')
                && $return->dropoff == false
                && data_get($return, 'shippingInfo.country') == 'US'
                && !$return->shipping
            ) {
                ReturnLabelController::CreateReturnLabel($return);
            }
        });
    }
}
