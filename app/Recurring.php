<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\Http\Controllers\Api\BagController;
use App\Http\Controllers\Api\EmailsController;

class Recurring extends Model
{
    protected $guarded = [];

    protected $dates = [
        'next_date'
    ];

    // protected $visible = [
    //     'cycle',
    //     'amount',
    //     'quantity',
    //     'next_date',
    //     'status'
    // ];

    public $casts = [
        'meta' => 'array',
        'shipping' => 'array',
        'payments' => 'array',
    ];

    public function orders()
    {
        return $this->morphMany(Order::class, 'recurring');
    }

    public function recurringSetting()
    {
        return $this->belongsTo(RecurringSetting::class, 'cycle', 'cycle')->where('amount', $this->amount);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function address()
    {
        return $this->belongsTo(Address::class);
    }

    public function payment()
    {
        return $this->belongsTo(Payment::class);
    }

    public function model()
    {
        return $this->morphTo();
    }

    public function orderWasCreated()
    {
        return !!$this->customer->orders
            ->where('meta.recurring.id', $this->id)
            ->where('meta.recurring.date', now()->toDateString())
            ->first();
    }

    public function createOrder()
    {
        if ($this->orderWasCreated()) {
            return;
        }
        $products = [$this->model->getFrontEndAttribute($this->quantity)];
        $address = $this->address;
        $payment = $this->payment;
        $totals = (new BagController)->GetBreakDown(request()->merge([
            'products' => $products,
            'address' => $address,
        ]));

        $this->customer->orders()->create([
            'sub_total' => $totals['sub_total'],
            'tax_amount' => $totals['tax'],
            'shipping_amount' => $totals['shipping'],
            'meta->recurring' => [
                'id' => $this->id,
                'name' => $this->recurringSetting->name,
                'amount' => $this->amount,
                'cycle' => $this->cycle,
                'date' => now()->toDateString()
            ],
            'meta->subscription' => [
                'id' => $this->id,
                'name' => $this->recurringSetting->name,
            ],
            'subscription' => true,
            'grand_total' => $totals['total'],
            'products' => $totals['bag'],
            'shipping' => [
                'shippingInfo' => $this->shipping,//$address,
                'shippingType' => 'subscription',
                'tracking' => null,
            ],
            'payments' => [
                'giftCard' => [],
                'creditInfo' => $this->payments,//$payment,
            ],
            'payments->creditInfo->payment_type' => 'creditCard',
            'payment_status' => 'unpaid',
            'status' => 'paid',
            'product_ids' => $totals['bag']->pluck('product_id')->filter(),
            'recurring_type' => 'App\Recurring',
            'recurring_id' => $this->id,
        ]);

        $interval_function = 'add' . $this->cycle;
        $this->update([
            'next_date' => today()->$interval_function($this->amount)
        ]);
    }

    public static function createTodaysOrders()
    {
        self::whereDate('next_date', today())
            ->where(function ($query) {
                $query->whereNotIn('status', ['cancelled', 'canceled'])
                    ->orWhere('status', null);
            })
            ->get()
            ->each
            ->createOrder();
    }

    public function getRecurringAttribute()
    {
    }

    public function setRecurringAttribute()
    {
    }

    public function setTypeAttribute($value)
    {
        if ($value == 'Product') {
            $this->model_type = 'App\Product';
        } elseif ($value == 'Variation') {
            $this->model_type = 'App\VariationInfo';
        }
    }

    public function getTypeAttribute()
    {
        if ($this->model_type == 'App\Product') {
            return 'Product';
        } elseif ($this->model_type == 'App\VariationInfo') {
            return 'Variation';
        }
    }

    public function getLastPaymentAttribute()
    {
        return $this->orders ? optional(($this->orders)->sortByDesc('created_at')->first())->payment_status : '';
    }

    public function setLastPaymentAttribute()
    {
    }

    public static function sendTodaysEmails()
    {
        $date = today()->addDays(4);
        self::whereDate('next_date', $date)->get()->each->sendReview();
    }

    public function sendReview()
    {
        EmailsController::SendRecurringReview($this);
    }

    public function setShippingAttribute($value)
    {
        if (!request()->is('nova-api/*')) {
            $this->attributes['shipping'] = json_encode($value);
        }
    }

    public function setPaymentsAttribute($value)
    {
        if (!request()->is('nova-api/*')) {
            $this->attributes['payments'] = json_encode($value);
        }
    }


    protected static function boot()
    {
        parent::boot();

        static::creating(function ($recurring) {
            if (request()->is('nova-api/*')) {
                $request = request();

                $recurring_setting = RecurringSetting::find($request->recurring);
                $recurring->cycle = $recurring_setting->cycle;
                $recurring->amount = $recurring_setting->amount;
                $recurring->status = 'active';
            }
        });
        static::saving(function ($recurring) {
            if (request()->is('nova-api/*')) {
                $array = [];

                if (request()->shipping) {
                    collect(json_decode(request()->shipping))->map(function ($value, $key) use (&$array) {
                        $array = array_merge(["shipping->$key" => $value], $array);
                    });
                }
                $payment = Payment::find(request()->payment_id);
                $payments = $payment ? collect($payment->toArray())->only(
                    'id',
                    'address_line_1',
                    'address_line_2',
                    'city',
                    'country',
                    'created_at',
                    'expDate',
                    'last_four',
                    'name',
                    'postal_code',
                    'securityCode',
                    'state',
                    'type'
                )->toArray() : null;

                if ($payments) {
                    collect($payments)->map(function ($value, $key) use (&$array) {
                        $array = array_merge(["payments->$key" => $value], $array);
                    });
                }
                $recurring->withoutEvents(function () use ($array, $recurring) {
                    $recurring->update($array);
                });
            }
        });
        self::created(function ($recurring) {
            $recurring->createOrder();
            EmailsController::SendRecurringConfirmation($recurring);
        });
    }
}
