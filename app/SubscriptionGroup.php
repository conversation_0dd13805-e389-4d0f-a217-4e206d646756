<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class SubscriptionGroup extends Model
{
    public $guarded = [];
    // public $visible = [
    //     'id',
    //     'name',
    //     'items',
    // ];

    public function combinations()
    {
        return $this->hasMany(Combination::class, 'group_id', 'id');
    }

    public function items()
    {
        return $this->hasMany(SubscriptionGroupItem::class);
    }

    public function filter()
    {
        return $this->belongsTo(Filter::class);
    }

    public function subscription_types()
    {
        return $this->hasMany(SubscriptionType::class, 'filter_id', 'filter_id');
    }

    public function getFormattedProductsAttribute()
    {
        return $this->items->map(function ($item) {
            if (!$model = $item->model) {
                return;
            }
            $data = $model->getFrontEndAttribute();
            $return = [
                'id' => $model->id,
                'type' => $data['type'],
                'title' => $model->title,
                'price' => $model->price,
                'media' => data_get($model->search, 'image'),
                'sku' => $model->sku,
                'product_id' => $data['product_id'],
            ];
            if ($data['type'] == 'variation') {
                $return['meta'] = json_decode($item->model->meta);
                $return['title'] = $item->model->product->title;
                $return['media'] = $item->model->product->search['image'];
            }
            return $return;
        })->filter()->values();
    }

    public function setFormattedProductsAttribute($value)
    {
        return;
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($group) {
            $request = request();
            if ($request->has('formatted_products')) {
                $items = collect(json_decode(request()->formatted_products))->map(function ($item) {
                    switch ($item->type) {
                        case 'product':
                            $type = 'App\Product';
                            break;
                        case 'variation':
                            $type = 'App\VariationInfo';
                            break;
                    }
                    return SubscriptionGroupItem::make([
                        'model_type' => $type,
                        'model_id' => $item->id,
                        'product_id' => $item->product_id,
                    ]);
                });
                $group->items()->delete();
                $group->items()->saveMany($items);
            }
            Combination::groupSaved($group);
        });

        static::deleted(function ($group) {
            $group->combinations()->delete();
        });
    }
}
