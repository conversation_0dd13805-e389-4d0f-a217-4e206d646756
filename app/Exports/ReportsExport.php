<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;

use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Contracts\Queue\ShouldQueue;


class ReportsExport implements FromCollection, ShouldQueue
{
    use Exportable;

    /**
     * @return Collection
     */
    protected $report;

    public function __construct(array $report)
    {
        $this->report = $report;
    }

    public function collection()
    {
        return collect($this->report);
    }
}
