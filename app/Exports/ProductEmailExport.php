<?php

namespace App\Exports;

use App\Order;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\LaravelNovaExcel\Actions\DownloadExcel;

class ProductEmailExport extends DownloadExcel implements WithMapping
{
    public function name()
    {
        return 'Download Emails That Ordered';
    }

    public function map($row): array
    {
        return Order::setEagerLoads([])
            ->with('customer:id,email')
            ->whereJsonContains('product_ids', $row->id)
            ->get(['id', 'customer_id'])
            ->map
            ->customer
            ->map
            ->email
            ->map(function ($email) use ($row) {
                return [
                    $email,
                    $row->id,
                ];
            })
            ->unique()
            ->toArray();
    }
}
