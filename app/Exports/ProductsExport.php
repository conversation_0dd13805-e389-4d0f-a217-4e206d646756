<?php

namespace App\Exports;

use App\Product;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\Exportable;
use Illuminate\Contracts\Queue\ShouldQueue;

class ProductsExport implements FromCollection, WithHeadings//, ShouldQueue
{
    use Exportable;

    /**
     * @return Collection
     */

    // protected $products;
    // public function __construct($products)
    // {
    //     $this->products = $products;
    // }

    public function collection()
    {
        return Product::active()->get([
            'id',
            'title',
            'description',
            'short_desc',
            'heb_title',
            'heb_description',
            'heb_short_desc',
            'list_price',
            'store_price',
            'online_price',
            'sale_price',
            'store_quantity',
            'website_quantity',
            'barcode',
            'sku',
            'width',
            'height',
            'length',
            'weight',
            'origin',
            'system_code',
            'track_inventory',
            'vendor_id',
            'tax_code',
            'publish',
            'visibility',
            'exclude_free_shipping',
        ])->map(function ($product, $index) {
            return [
                $index + 1,
                data_get($product, 'id'),
                data_get($product, 'title'),
                data_get($product, 'description'),
                data_get($product, 'short_desc'),
                data_get($product, 'heb_title'),
                data_get($product, 'heb_description'),
                data_get($product, 'heb_short_desc'),
                data_get($product, 'list_price'),
                data_get($product, 'store_price'),
                data_get($product, 'online_price'),
                data_get($product, 'sale_price'),
                data_get($product, 'store_quantity'),
                data_get($product, 'website_quantity'),
                data_get($product, 'barcode'),
                data_get($product, 'sku'),
                data_get($product, 'width'),
                data_get($product, 'height'),
                data_get($product, 'length'),
                data_get($product, 'weight'),
                data_get($product, 'origin'),
                data_get($product, 'system_code'),
                data_get($product, 'track_inventory'),
                data_get($product, 'vendor_id'),
                data_get($product, 'tax_code'),
                data_get($product, 'publish'),
                data_get($product, 'visibility'),
                data_get($product, 'exclude_free_shipping'),
            ];
        });
    }

    public function headings(): array
    {
        return [
            'index',
            'id',
            'title',
            'description',
            'short_desc',
            'heb_title',
            'heb_description',
            'heb_short_desc',
            'list_price',
            'store_price',
            'online_price',
            'sale_price',
            'store_quantity',
            'website_quantity',
            'barcode',
            'sku',
            'width',
            'height',
            'length',
            'weight',
            'origin',
            'system_code',
            'inventory',
            'track_inventory',
            'vendor_id',
            'tax_code',
            'publish',
            'visibility',
            'exclude_free_shipping',
        ];
    }




    // public function collection()
    // {
    //    return Product::active()->get()->map(function($product) {
    //         return [
    //             'sku' => data_get($product, 'sku'),
    //             'title' => data_get($product, 'title'),
    //             'description' => strip_tags(data_get($product, 'short_desc')),
    //             'avaiability' => $product->max >= 0,
    //             'condition' => 'new',
    //             'price' => $product->price,
    //             'link' => env('APP_URL') . $product->path,
    //             'image_link' => data_get($product, 'media_urls.0.grid'),
    //             'brand' => $product->barcode,
    //             'product_type' => $product->item_type,
    //         ];
    //     });
    // }
    // public function headings(): array
    // {
    //     return [
    //         'SKU',
    //         'TITLE',
    //         'DESCRIPTION',
    //         'AVAILABILITY',
    //         'CONDITION',
    //         'PRICE',
    //         'LINK',
    //         'IMAGE_LINK',
    //         'BRAND',
    //         'PRODUCT_TYPE',
    //     ];
    // }
}
