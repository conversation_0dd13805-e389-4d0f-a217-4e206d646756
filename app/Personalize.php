<?php

namespace App;

use App\Jobs\Miscellaneous;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Personalize extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $casts = [
        'options' => 'array',
    ];

    protected $appends = [];

    protected $guarded = [];

    public function products()
    {
        return $this->hasMany(Product::class, 'personalizes_id');
    }

    public function getTotalAttribute()
    {
        return collect(collect($this->options)->pluck('attributes'))->pluck('price')->filter()->sum();
    }

    public function info($key, $value)
    {
        $option = collect(collect($this->options)->firstWhere('key', $key));
        if (data_get($option, 'layout') == 'images') {
            $value = collect($option['attributes']['options'])->firstWhere('key', $value)['attributes']['name'];
        }
        return [
            'layout' => $option['layout'],
            'price' => $option['attributes']['price'],
            'name' => $option['attributes']['name'],
            'value' => $value
        ];
    }

    public function getInfo($personalize, $quantity)
    {
        $options = collect($personalize)->flatten(1)->map(function ($p) {
            $value = data_get($p, 'value.key') ?? $p['value'];

            return $this->info($p['key']['key'], $value);
        });

        $string = '';
        $options->each(function ($o) use (&$string) {
            $string .= $o['name'] . ' : ' . $o['value'] . ' | ';
        });
        $string .= 'Fee : $' . number_format($options->map->price->filter()->sum() * $quantity, 2);

        return [
            'duration' => $this->duration,
            'total' => $options->map->price->filter()->sum() * $quantity,
            'options' => $options,
            'string' => $string
        ];
    }

    public function AddPictures()
    {
        $this->options = collect($this->options)->map(function ($per) {
            if ($per['layout'] == 'images') {
                $per['attributes']['options'] = collect($per['attributes']['options'])->map(function ($option) {
                    $option['attributes']['picture'] = $this->getPicture($option['key']);
                    return $option;
                });
            }
            return $per;
        });
        return $this;
    }

    public function getPicture($key)
    {
        return collect($this->media_urls)->map(function ($picture) use ($key) {
            if (Str::contains($picture, $key)) {
                return $picture;
            }
        })->filter()->first();
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->background('#ffffff')
            ->width(130)
            ->height(130);
    }

    public function getMediaUrlsAttribute()
    {
        return $this->media->sortBy('order_column')->map(function ($item) {
            return $item->getUrl();
        });
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('images');

        $this->addMediaCollection('mobile');

        $this->addMediaCollection('media')
            ->singleFile();
    }

    public function refreshCache($personalize)
    {
        $personalize->products->each->refreshCache();
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($personalize) {
            refreshCache();
            Miscellaneous::dispatch(Personalize::class, 'refreshCache', $personalize);
        });

        static::deleted(function ($personalize) {
            refreshCache();
        });
    }
}
