<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ProductCollection extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $guarded = [];

    protected $casts = [
        'products' => 'array',
    ];

    public function getPathAttribute()
    {
        return "/collections/" . str_slug($this->name) . "/{$this->id}";
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->background('#ffffff')
            ->width(130)
            ->height(130);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image')
            ->singleFile();
    }

    public function products()
    {
        if (!$this->products || !is_array($this->products)) {
            return collect();
        }

        // Return products in the exact order they appear in the database
        $productIds = $this->products;
        $products = \App\Product::whereIn('id', $productIds)->get();

        // Sort products according to the order in the products array
        return $products->sortBy(function ($product) use ($productIds) {
            return array_search($product->id, $productIds);
        });
    }

    public function getFormattedProductsAttribute()
    {
        if (!$this->products || !is_array($this->products)) {
            return [];
        }

        $products = \App\Product::whereIn('id', $this->products)->get();

        // Sort products according to the order in the products array
        $sortedProducts = $products->sortBy(function ($product) {
            return array_search($product->id, $this->products);
        });

        return $sortedProducts->map(function ($product) {
            return [
                'id' => $product->id,
                'type' => 'product',
                'title' => $product->title,
                'price' => $product->price,
                'media' => $product->getFirstMediaUrl('media', 'grid'),
                'sku' => $product->sku,
                'quantity' => 1, // Collections don't use quantities
            ];
        })->values();
    }

    public function setFormattedProductsAttribute($value)
    {
        // This will be handled by the observer
        return;
    }
}
