<?php

namespace App;

use App\GiftCard;
use App\PayPalPayment;
use Illuminate\Database\Eloquent\Model;
use App\Http\Controllers\Api\CreditCardController;

class CreditCardPayment extends Model
{
    protected $guarded = [];

    public $casts = [
        'captures' => 'array',
        'authorized' => 'array',
        'refunds' => 'array',
        'charges' => 'array',
        'payer_info' => 'array',
        'errors' => 'array',
    ];

    public function order()
    {
        return $this->morphOne(Order::class, 'payment');
    }

    public function getTotalAttribute()
    {
        return $this->amount;
    }

    public function getRefundAttribute()
    {
        return collect($this->refunds)->map->Amount->sum();
    }

    public function charge($order, $amount)
    {
        CreditCardController::Charge($order, $amount);
    }

    public function authorize($order, $amount)
    {
        CreditCardController::AuthorizeCC($order, $amount);
    }

    public function capture($order)
    {
        CreditCardController::Capture($order);
    }

    public function cancel($order)
    {
        CreditCardController::Cancel($order);
    }

    public function refund($order, $amount)
    {
        CreditCardController::Refund($order, $amount);
    }
}
