<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;

class Redirect extends Model
{
    protected $quarded = [];

    public static function getAllSearchRedirects()
    {
        return Cache::tags(['redirects'])->remember("redirects_search", 60 * 60 * 24 * 7, function () {
            return collect([
                Redirect::where('active', true)
                    ->where('from_type', 'search_term')
                    ->get()
                    ->map(function ($redirect) {
                        return collect(explode(',', $redirect->from))->map(function ($from) use ($redirect) {
                            return [
                                'from' => trim(strtolower($from)),
                                'to_type' => $redirect->to_type,
                                'to' => $redirect->to_type == 'url' ? '/' . $redirect->to : $redirect->to
                            ];
                        });
                    })->flatten(1),

                Page::where('redirect', true)
                    ->get(['name', 'id', 'title'])
                    ->map(function ($page) {
                        return [
                            'from' => strtolower($page->name),
                            'to_type' => 'url',
                            'to' => $page->path
                        ];
                    }),

                Category::where('redirect', true)
                    ->get(['name', 'id', 'name'])
                    ->map(function ($category) {
                        return [
                            'from' => strtolower($category->name),
                            'to_type' => 'url',
                            'to' => $category->path
                        ];
                    }),


                AutomatedCategory::where('redirect', true)
                    ->get(['name', 'id', 'name'])
                    ->map(function ($automatedCategory) {
                        return [
                            'from' => strtolower($automatedCategory->name),
                            'to_type' => 'url',
                            'to' => $automatedCategory->path
                        ];
                    }),


                Creator::where('redirect', true)
                    ->get(['name', 'id'])
                    ->map(function ($creator) {
                        return [
                            'from' => strtolower($creator->name),
                            'to_type' => 'url',
                            'to' => $creator->path
                        ];
                    }),

            ])->flatten(1);
        });
    }


    public static function fromUrls()
    {
        return Cache::tags(['redirects'])->remember("redirects_url", 60 * 60 * 24 * 7, function () {
            return Redirect::where('active', true)
                ->where('from_type', 'url')
                ->get()
                ->map(function ($redirect) {
                    return [
                        'from' => env('APP_URL') . '/' . $redirect->from,
                        'to_type' => $redirect->to_type,
                        'to' => $redirect->to
                    ];
                });
        });
    }


    public function setFromAttribute($value)
    {
        if ($this->from_type == 'url') {
            $array = collect([
                Redirect::get()
                    ->where('id', '!=', $this->id)
                    ->where('from_type', 'url')
                    ->map(function ($redirect) {
                        return strtolower($redirect->from);
                    })->flatten(1)
            ]);
        } else {
            $array = collect([
                Redirect::get()
                    ->where('id', '!=', $this->id)
                    ->where('from_type', 'search_term')
                    ->map(function ($redirect) {
                        return collect(explode(',', $redirect->from))->map(function ($from) use ($redirect) {
                            return trim(strtolower($from));
                        });
                    })->flatten(1),
                Page::where('redirect', true)->get()->map(function ($redirect) {
                    return strtolower($redirect->name);
                }),
                Category::where('redirect', true)->get()->map(function ($redirect) {
                    return strtolower($redirect->name);
                }),
                AutomatedCategory::where('redirect', true)->get()->map(function ($redirect) {
                    return strtolower($redirect->name);
                }),
                Creator::where('redirect', true)->get()->map(function ($creator) {
                    return strtolower($creator->name);
                })
            ])->flatten(1);
        }


        if ($array->contains(strtolower($value))) {
            throw ValidationException::withMessages([
                'from' => 'This is not unique',
            ]);
        }

        $this->attributes['from'] = $value;
    }

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($redirect) {
            Cache::tags('redirects')->flush();
        });

        static::deleted(function ($redirect) {
            Cache::tags('redirects')->flush();
        });
    }
}
