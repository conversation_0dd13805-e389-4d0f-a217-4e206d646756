<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\Product;
use DateTime;
use App\Order;

class AssociatedPurchase extends Model
{
    protected $guarded = [];

    protected $casts = [
        'associated_items' => 'array'
    ];

    public static function min_customers()
    {
        return settings()->getValue('frequent_min_customer'); //3;//settings()->getValue('frequent_date')
    }

    public function product()
    {
        return $this->belongsTo(\App\Product::class);
    }

    public static function saveAssociation($order)
    {
        $productIds = collect($order->product_ids);
        $orderId = $order->id;

        if ($productIds->count() <= 1) {
            return null;
        }
        $productIds->each(function ($id) use ($productIds, $orderId) {
            $purchase = static::firstOrNew(['product_id' => $id]);

            $productIds->each(function ($itemId) use ($id, $purchase, $orderId) {
                if ($itemId != $id) {
                    $purchase->associated_items = collect($purchase->associated_items)->push([$itemId, time(), $orderId]
                    );
                }
            });

            $purchase->save();
        });
    }

    public static function findAssociation($productId)
    {
        return static::where('product_id', '=', $productId)->first();
    }

    public function getAssociatedItems()
    {
        $lastYear = now()->addYears(-1)->timestamp;
        $min_associations = 3;
        $required_products = 5;
        $items = collect($this->associated_items)->where(1, ">=", $lastYear)->groupBy(0);
        $filtered = $items->filter(function ($value, $key) use ($min_associations) {
            return $value->count() >= $min_associations;
        });
        $mapped = $filtered->map(function ($item) {
            return $item = (int)($item->pluck(1)->sum() / 86400);
        });
        $product_ids = $mapped->sortByDesc(1)->keys()->take($required_products);
        if ($product_ids->count() < $required_products) {
            $needed = $required_products - $product_ids->count();
            $old_items = collect($this->associated_items)->where(1, "<", $lastYear)->pluck(0)->countBy();
            $product_ids = $product_ids->merge($old_items->sortByDesc(1)->keys()->take($needed));
        }
        return Product::find($product_ids)->sortBy(function ($product) use ($product_ids) {
            return array_search($product->id, $product_ids->toArray());
        })->map(function ($product) {
            return $product->front_end;
        })->values();
    }

    public function calculateRuleMining($date, $totalOrders, $customerIds, $setThresh, $setConf)
    {
        $totalFrequency = data_get($totalOrders, $this->product_id);

        $customerIds = collect($customerIds);
        $totalOrders = collect($totalOrders);

        $this->associated_items = collect($this->associated_items)->map(function ($item) use ($date, $customerIds) {
            if ($item[1] > $date) {
                return collect(
                    [
                        $item['0'],
                        $item['2'],
                        data_get($customerIds->where('order_id', $item['2'])->first(), 'customer_id')
                    ]
                );
            } else {
                return null;
            }
        })->filter();

        if (collect($this->associated_items)->count() < $setThresh) {
            return;
        }
        $threshold = round($totalFrequency * $setConf);
        $threshold = ($threshold >= $setThresh) ? $threshold : $setThresh;

        $frequentBundles = $this->getFirstSet($threshold);

        if ($frequentBundles->isEmpty()) {
            return;
        }

        $transactions = $this->getTransactions();

        $newBundles = static::addSecondItem($frequentBundles->keys(), $transactions, $threshold);
        if ($newBundles->isNotEmpty()) {
            while (true) {
                $frequentBundles = $newBundles;
                $newBundles = static::addMoreItems($newBundles->pluck(0), $transactions, $threshold);
                if ($newBundles->isEmpty()) {
                    break;
                }
            }
            $bundle = $frequentBundles->sortBy(1)->reverse()->first();
        } else {
            $bundle = $frequentBundles->take(1);
            $bundle = [$bundle->keys()[0], $bundle->values()[0]];
        }
        $bundle[1] = (float)number_format($bundle[1] / $totalFrequency, 2);
        return $bundle;
    }

    public function getTransactions()
    {
        return collect($this->associated_items)
            ->groupBy(1)->map(function ($item) {
                return [$item->pluck(0)->toArray(), $item->first()[2]];
            })->values()->toArray();
    }

    public function getFirstSet($threshold)
    {
        return collect($this->associated_items)
            ->groupBy(0)
            ->map(function (&$item) use ($threshold) {
                if (
                    $item->count() >= $threshold
                    && $item->pluck(2)->unique()->values()->count() >= self::min_customers()
                ) {
                    return $item = $item->count();
                } else {
                    return null;
                }
            })
            ->filter()
            ->sort()
            ->reverse();
    }

    public static function searchArrayInArray($array, $array2)
    {
        foreach ($array as $a) {
            if (!in_array($a, $array2[0])) {
                return false;
            }
        }
        return true;
    }

    public static function getTotalMatches($values, $multiArray)
    {
        return collect($multiArray)->filter(function ($array) use ($values) {
            return static::searchArrayInArray($values, $array);
        })->pluck(1);
    }

    public static function addSecondItem($itemSets, $transactions, $threshold)
    {
        return $itemSets->map(function ($item) use ($itemSets) {
            return collect($item)->crossJoin($itemSets->slice($itemSets->search($item) + 1));
        })->flatten(1)
            ->map(function ($itemSet) use ($transactions, $threshold) {
                $customers = static::getTotalMatches($itemSet, $transactions);
                if ($customers->count() >= $threshold && $customers->unique()->count() >= self::min_customers()) {
                    return [$itemSet, $customers->count()];
                } else {
                    return null;
                }
            })->filter();
    }

    public static function addMoreItems($itemSets, $transactions, $threshold)
    {
        $setSize = count($itemSets[0]) + 1;
        return $itemSets->crossJoin(
            $itemSets->flatten()->unique()
        )->filter(function ($array) {
            return !(in_array($array[1], $array[0]));
        })
            ->flatten()->chunk($setSize)
            ->map(function ($chunks) {
                return $chunks->sort()->values();
            })->unique()->values()
            ->map(function ($itemSet) use ($transactions, $threshold) {
                $customers = static::getTotalMatches($itemSet->toArray(), $transactions);
                if ($customers->count() >= $threshold && $customers->unique()->count() >= self::min_customers()) {
                    return [$itemSet->toArray(), $customers->count()];
                } else {
                    return null;
                }
            })->filter();
    }
}
