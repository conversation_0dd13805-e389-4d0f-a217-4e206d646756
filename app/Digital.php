<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Digital extends Model
{
    // public $with = ['model'];
    public $guarded = [];

    protected $appends = [
        'extensions'
    ];

    public function model()
    {
        return $this->morphTo('model');
    }

    public function GetExtensionsAttribute()
    {
        return $this->model->digital_extensions;
    }

    public static function saveOrder($order)
    {
        if ($order->guest) {
            return;
        }

        $user = $order->customer;
        collect($order->products)->each(function ($item) use ($user) {
            if (
                (data_get($item, 'item_type') == 'digital' || data_get($item, 'item_type') == 'both')
                && data_get($item, 'type') != 'giftCard'
            ) {
                $user->digitals()->firstOrCreate([
                    'model_type' => 'App\Product',
                    'model_id' => $item['product_id'],
                ]);
            }
        });
    }
}
