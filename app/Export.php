<?php

namespace App;

use <PERSON><PERSON>\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Export extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $guarded = [];

    public function getMediaUrlsAttribute()
    {
        return $this->media->sortBy('order_column')->map(function ($item) {
            return $item->getUrl();
        });
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('file')
            ->singleFile();
    }
}
