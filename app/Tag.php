<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Tag extends Model
{
    protected $appends = [
        'path',
    ];

    protected $guarded = [];

    protected $casts = [
        'name' => 'object',
        'slug' => 'object',
    ];

    public function products()
    {
        return $this->belongsToMany(Product::class);
    }

    public function getPathAttribute()
    {
        return "/tags/{$this->id}";
    }
}
