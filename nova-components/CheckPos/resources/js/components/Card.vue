<template>
    <card class="flex flex-col items-center justify-center">
        <div class="px-3 py-3">
            <h1 style="margin-bottom:10px" class="text-center text-3xl text-80 font-light">POS Sync</h1>
            <input class="form-control form-input form-input-bordered" placeholder="Sku" type="text" v-model="input" id="sku" style="border:1px solid black">
            <div class="flex items-center justify-center mt-2 gap-4">
                <DefaultButton @click="check" class="dim btn btn-default">Check</DefaultButton>
                <OutlineButton @click="update" class="dim btn btn-default">Update</OutlineButton>
            </div>
        </div>
        <div class="fixed" v-if="showLightBox">
            <div>
                <form autocomplete="off" class="bg-white rounded-lg shadow-lg overflow-hidden w-action">
                    <div>
                        <h2 class="border-b border-40 py-8 px-8 text-90 font-normal text-xl">Sku: <strong>{{input}}</strong></h2>
                    <p v-html="results" class="text-80 px-8 my-8"></p>
                    </div>
                    <div class="bg-30 px-6 py-3 flex">
                        <div class="flex items-center ml-auto">
                            <DefaultButton @click.prevent="showLightBox = false" dusk="cancel-action-button" type="button" class="btn btn-default btn-primary">Ok</DefaultButton>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </card>
</template>

<script>
import axios from 'axios'

export default {
    data(){
        return {
            input: '',
            results: '',
            showLightBox: false,
        }
    },
    props: [
        'card',
    ],

    methods: {
        check() {
            axios(`/nova-vendor/check-pos/${encodeURIComponent(this.input)}`)
                .then(({data}) => {
                    this.showLightBox = true
                    this.results = data
                })
        },
        update() {
            axios(`/nova-vendor/check-pos/update/${encodeURIComponent(this.input)}`)
                .then(({data}) => {
                    this.showLightBox = true
                    this.results = data
                })
        },
        close() {
            this.input = ''
            this.showLightBox = false
        }
    }
}
</script>

<style scoped>
    .fixed {
        position: fixed;
        height: 100%;
        width: 100%;
        top: 0;
        bottom: 0;
        right: 0;
        left: 0;
        display: flex;
        justify-content: center;
        padding-top: 100px;
        background-color: rgba(179,185,191,.8);
        z-index: 100;
    }
</style>
