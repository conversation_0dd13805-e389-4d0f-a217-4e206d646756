<?php

use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Cache;
use App\Http\Controllers\PosReportsController;

/*
|--------------------------------------------------------------------------
| Card API Routes
|--------------------------------------------------------------------------
|
| Here is where you may register API routes for your card. These routes
| are loaded by the ServiceProvider of your card. You're free to add
| as many additional routes to this file as your card may require.
|
*/

Route::get('{input}', function (Request $request, $input) {

    $input = rawurldecode($input);

    $client = new \GuzzleHttp\Client;
    $response = $client->post("https://api5.firstchoicepos.com/v1/Report/Read", [
        'headers' => ['Authorization' => 'Basic ' . env('POS')],
        'json' => [
            'ReportKey' => 'API_Items',
            'Parameters' => [
                'SKU' => $input
            ]
        ]
    ]);
    $results = $response->getBody()->getContents();
    $lines = '';
    $array = collect(forceArray($results)['Results']);
    if ($array->isEmpty()) {
        return 'Not Found';
    }
    foreach ($array->first() as $key => $value) {
        $lines .= "$key: $value<br/>";
    }
    return $lines;
})->where('input', '.+');
Route::get('update/{input}', function (Request $request, $input) {

    $input = rawurldecode($input);

    $updateIds = collect();
    $deleteIds = collect();

    $products = \App\Product::where('sku', $input)->get();
    $variations = \App\VariationInfo::where('sku', $input)->get();
    $all = $products->merge($variations);
    $products->each(function ($product) use (&$updateIds, $deleteIds) {
        if ($product->searchable) {
            $updateIds->push($product->id);
        } else {
            $deleteIds->push($product->id);
        }
        $product->withoutEvents(function () use ($product) {
            PosReportsController::GetProductInfo($product);
        });
    });

    $variations->each(function ($product) use (&$updateIds) {
        if ($product->searchable) {
            $updateIds->push($product->id);
        }
        $product->withoutEvents(function () use ($product) {
            PosReportsController::GetProductInfo($product);
        });
    });

    collect($updateIds)->each(function ($id) {
        Cache::forget("product_{$id}");
    });
    collect($deleteIds)->each(function ($id) {
        Cache::forget("product_{$id}");
    });

    idsToSwiftype($deleteIds, true);
    idsToSwiftype($updateIds);

    return $products->count()
        . ' '
        . Str::plural('Product', $products->count())
        . ', '
        . $variations->count()
        . ' '
        . Str::plural('Variation', $variations->count())
        . ' was updated';
})->where('input', '.+');
