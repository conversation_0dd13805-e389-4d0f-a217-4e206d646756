{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production", "nova:install": "npm --prefix='../../vendor/laravel/nova' ci"}, "devDependencies": {"@vue/compiler-sfc": "^3.2.22", "form-backend-validation": "^2.3.3", "laravel-mix": "^6.0.41", "lodash": "^4.17.21", "postcss": "^8.3.11", "resolve-url-loader": "^5.0.0", "sass": "^1.81.1", "sass-loader": "^16.0.3", "vue-loader": "^16.8.3"}, "dependencies": {"axios": "^1.7.9", "vue": "^3.2.22"}}