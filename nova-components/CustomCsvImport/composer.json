{"name": "capitalc/custom-csv-import", "description": "Custom extension of simonhamp/laravel-nova-csv-import with resource restrictions and upsert functionality", "keywords": ["laravel", "nova", "csv", "import"], "license": "MIT", "require": {"php": ">=8.2", "simonhamp/laravel-nova-csv-import": "^0.7.2"}, "autoload": {"psr-4": {"Capitalc\\CustomCsvImport\\": "src/"}}, "extra": {"laravel": {"providers": ["Capitalc\\CustomCsvImport\\ToolServiceProvider"]}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}