<template>
    <div>
        <Head>
            <title>Preview Import</title>
        </Head>

        <heading class="mb-6">CSV Import - Preview</heading>

        <card class="p-8 space-y-4" style="min-height: 300px">
            <p>
                Here's a preview of the first few rows of your import. If everything looks good, click <b>Import</b> to queue the import job.
            </p>

            <table cellpadding="10">
                <thead class="border-b">
                    <tr>
                        <th v-for="(column, index) in columns" :key="index">{{ column }}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(row, index) in rows" :key="index" class="border-b">
                        <td v-for="(column, columnIndex) in columns" :key="columnIndex">
                            {{ row[column] }}
                        </td>
                    </tr>
                </tbody>
            </table>

            <div class="border-t flex justify-between" style="padding-top: 1rem">
                <div class="flex space-x-4">
                    <LinkButton @click="reconfigure"><HeroiconsOutlineRewind /> Reconfigure</LinkButton>
                </div>
                <div class="flex space-x-4">
                    <BasicButton 
                        @click="runImport" 
                        :disabled="importing"
                        class="bg-green-600 hover:bg-green-700 text-white"
                    >
                        <span v-if="importing" class="flex items-center space-x-2">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            <span>Queueing Import...</span>
                        </span>
                        <span v-else class="flex items-center space-x-2">
                            <HeroiconsOutlinePlay />
                            <span>Import</span>
                        </span>
                    </BasicButton>
                </div>
            </div>
        </card>
    </div>
</template>

<script>
export default {
    props: {
        rows: {
            type: Array,
            required: true
        },
        columns: {
            type: Array,
            required: true
        },
        file: {
            type: String,
            required: true
        }
    },

    data() {
        return {
            importing: false,
        };
    },

    methods: {
        runImport() {
            this.importing = true;

            let data = {
                file: this.file,
            };

            Nova.request()
                .post(this.url('import'), data)
                .then((response) => {
                    if (response.status === 200) {
                        Nova.success('Import job queued successfully! Redirecting to results page...');
                        
                        // Small delay to show the success message
                        setTimeout(() => {
                            Nova.visit('/csv-import/review/' + this.file);
                        }, 1000);
                    }
                })
                .catch((error) => {
                    this.importing = false;
                    
                    console.error('Import error:', error);
                    
                    if (error.response && error.response.data && error.response.data.error) {
                        Nova.error('Import failed: ' + error.response.data.error);
                    } else {
                        Nova.error('There was a problem queueing your import. Please try again.');
                    }
                });
        },

        reconfigure() {
            Nova.visit('/csv-import/configure/' + this.file);
        },

        url: function (path) {
            return '/nova-vendor/laravel-nova-csv-import/' + path;
        }
    },
}
</script>

<style scoped>
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>
