<template>
    <div>
        <Head>
            <title>Review Import</title>
        </Head>

        <heading class="mb-6">CSV Import - Review</heading>

        <card class="p-8 space-y-4" style="min-height: 300px">
            <!-- Progress indicator for queued/processing imports -->
            <div v-if="isProcessing" class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-center space-x-3">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <div>
                        <h3 class="text-lg font-medium text-blue-900">
                            {{ status === 'queued' ? 'Import Queued' : 'Import Processing' }}
                        </h3>
                        <p class="text-blue-700">
                            {{ progressText }}
                        </p>
                        <div v-if="total_rows > 0" class="mt-2">
                            <div class="bg-blue-200 rounded-full h-2">
                                <div 
                                    class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                                    :style="{ width: progressPercentage + '%' }"
                                ></div>
                            </div>
                            <p class="text-sm text-blue-600 mt-1">
                                {{ progressPercentage }}% complete ({{ processed_rows }} of {{ total_rows }} rows)
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error state -->
            <div v-if="status === 'failed'" class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center space-x-3">
                    <div class="text-red-600">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-red-900">Import Failed</h3>
                        <p class="text-red-700">The import job encountered an error and could not complete.</p>
                    </div>
                </div>
            </div>

            <!-- Success summary -->
            <div v-if="status === 'completed'">
                <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="text-green-600">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-green-900">Import Completed</h3>
                            <p class="text-green-700">
                                <b>{{ successful_rows }}</b> row(s) out of {{ total_rows }} were successfully imported.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Failures section -->
            <template v-if="failures.length !== 0">
                <BasicButton @click="showFailures = !showFailures">
                    {{ showFailures ? 'Hide failures' : 'Show failures' }} ({{ failures.length }})
                </BasicButton>
                <div v-if="showFailures">
                    <table cellpadding="10">
                        <thead class="border-b">
                            <tr>
                                <th>Row #</th>
                                <th>Attribute</th>
                                <th>Data</th>
                                <th>Details</th>
                                <th>Row Data</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template v-for="(failure, failureIndex) in failures" :key="failureIndex">
                                <tr class="border-b">
                                    <td valign="top" align="right">{{ failure.row }}</td>
                                    <td valign="top">{{ failure.attribute }}</td>
                                    <td valign="top">
                                        <code>
                                            {{ failure.values[failure.attribute] }}
                                            <i v-if="!failure.values[failure.attribute]">null</i>
                                        </code>
                                    </td>
                                    <td valign="top">
                                        <div v-for="error in failure.errors" :key="error">{{ error }}</div>
                                    </td>
                                    <td valign="top">
                                        <BasicButton @click="showFailureData[failureIndex] = !showFailureData[failureIndex]">
                                            {{ showFailureData[failureIndex] ? 'Hide data' : 'Show all row data' }}
                                        </BasicButton>
                                        <div v-show="showFailureData[failureIndex]">
                                            <div v-for="(value, key) in failure.values" :key="key">
                                                {{ key }} : <code>{{ value }}<i v-if="!value">null</i></code>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </template>

            <!-- Errors section -->
            <template v-if="errors.length !== 0">
                <BasicButton @click="showErrors = !showErrors">
                    {{ showErrors ? 'Hide errors' : 'Show errors' }} ({{ errors.length }})
                </BasicButton>
                <div v-if="showErrors">
                    <table cellpadding="10">
                        <thead class="border-b">
                            <tr>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="error in errors" :key="error.message" class="border-b">
                                <td>{{ error.message }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </template>

            <div class="border-t flex justify-between" style="padding-top: 1rem">
                <LinkButton @click="reconfigure"><HeroiconsOutlineRewind /> Reconfigure</LinkButton>
                <LinkButton @click="restart"><HeroiconsOutlineRefresh /> Upload another</LinkButton>
            </div>
        </card>
    </div>
</template>

<script>
export default {
    props: {
        file: {
            type: String,
            required: true
        }
    },

    data() {
        return {
            showFailureData: {},
            showFailures: false,
            showErrors: false,
            
            // Results data
            status: 'queued',
            total_rows: 0,
            processed_rows: 0,
            successful_rows: 0,
            failed_rows: 0,
            failures: [],
            errors: [],
            
            // Polling
            pollInterval: null,
            pollCount: 0,
            maxPollAttempts: 720, // 1 hour at 5-second intervals
        };
    },

    computed: {
        isProcessing() {
            return ['queued', 'processing'].includes(this.status);
        },
        
        progressPercentage() {
            return this.total_rows > 0 ? Math.round((this.processed_rows / this.total_rows) * 100) : 0;
        },
        
        progressText() {
            if (this.status === 'queued') {
                return 'Your import has been queued and will begin processing shortly...';
            }
            
            if (this.total_rows === 0) {
                return 'Initializing import...';
            }
            
            return `Processing ${this.processed_rows} of ${this.total_rows} rows...`;
        }
    },

    mounted() {
        this.startPolling();
    },

    beforeUnmount() {
        this.stopPolling();
    },

    methods: {
        startPolling() {
            this.fetchResults();
            this.pollInterval = setInterval(() => {
                this.pollCount++;
                
                if (this.pollCount >= this.maxPollAttempts) {
                    this.stopPolling();
                    Nova.error('Import polling timed out. Please refresh the page to check status.');
                    return;
                }
                
                if (this.isProcessing) {
                    this.fetchResults();
                } else {
                    this.stopPolling();
                }
            }, 5000); // Poll every 5 seconds
        },

        stopPolling() {
            if (this.pollInterval) {
                clearInterval(this.pollInterval);
                this.pollInterval = null;
            }
        },

        fetchResults() {
            Nova.request()
                .get(`/nova-vendor/laravel-nova-csv-import/results/${this.file}`)
                .then((response) => {
                    const data = response.data;
                    
                    this.status = data.status;
                    this.total_rows = data.total_rows;
                    this.processed_rows = data.processed_rows;
                    this.successful_rows = data.successful_rows;
                    this.failed_rows = data.failed_rows;
                    this.failures = data.failures || [];
                    this.errors = data.errors || [];
                    
                    if (!this.isProcessing) {
                        this.stopPolling();
                        
                        if (this.status === 'completed') {
                            Nova.success(`Import completed! ${this.successful_rows} rows imported successfully.`);
                        } else if (this.status === 'failed') {
                            Nova.error('Import failed. Please check the error details above.');
                        }
                    }
                })
                .catch((error) => {
                    console.error('Error fetching results:', error);
                    
                    // Don't stop polling on network errors, but limit retries
                    if (this.pollCount >= 10) {
                        this.stopPolling();
                        Nova.error('Unable to fetch import results. Please refresh the page.');
                    }
                });
        },

        reconfigure() {
            this.stopPolling();
            Nova.visit('/csv-import/configure/' + this.file);
        },

        restart() {
            this.stopPolling();
            Nova.visit('/csv-import');
        },
    }
}
</script>
