<?php

namespace Capitalc\CustomCsvImport\Http\Controllers;

use Illuminate\Support\Collection;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Nova;
use <PERSON>vel\Nova\Actions\ActionResource;
use SimonHamp\LaravelNovaCsvImport\Http\Controllers\ImportController;

class CustomImportController extends ImportController
{
    /**
     * Override to filter resources based on configuration
     *
     * @param NovaRequest $request
     * @return Collection
     */
    protected function getAvailableResourcesForImport(NovaRequest $request): Collection
    {
        $allowedResources = config('custom-csv-import.allowed_resources', []);
        
        // If no allowed resources are configured, use the default behavior
        if (empty($allowedResources)) {
            return parent::getAvailableResourcesForImport($request);
        }
        
        $novaResources = collect(Nova::authorizedResources($request));

        return $novaResources->filter(function ($resource) use ($request, $allowedResources) {
            if ($resource === ActionResource::class) {
                return false;
            }

            if (! isset($resource::$model)) {
                return false;
            }
            
            // Check if this resource is in the allowed list
            if (!in_array($resource, $allowedResources)) {
                return false;
            }

            $resourceReflection = (new \ReflectionClass((string) $resource));

            if ($resourceReflection->hasMethod('canImportResource')) {
                return $resource::canImportResource($request);
            }

            $static_vars = $resourceReflection->getStaticProperties();

            if (! isset($static_vars['canImportResource'])) {
                return true;
            }

            return isset($static_vars['canImportResource']) && $static_vars['canImportResource'];
        });
    }
}
