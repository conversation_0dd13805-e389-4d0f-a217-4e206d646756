<?php

namespace Capitalc\CustomCsvImport;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Laravel\Nova\Nova;
use Laravel\Nova\Events\ServingNova;
use SimonHamp\LaravelNovaCsvImport\Http\Middleware\Authorize;
use Capitalc\CustomCsvImport\Http\Controllers\CustomImportController;
use Capitalc\CustomCsvImport\Http\Controllers\QueuedImportController;

class ToolServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        $this->mergeConfigFrom(__DIR__.'/../config/custom-csv-import.php', 'custom-csv-import');

        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__.'/../config/custom-csv-import.php' => config_path('custom-csv-import.php'),
            ], 'custom-csv-import-config');
        }

        // Register our custom routes BEFORE the base package routes
        $this->registerCustomRoutes();

        // Register other bindings after the original service provider
        $this->app->booted(function () {
            // Replace the ImportController entirely with our QueuedImportController
            $this->app->bind(
                \SimonHamp\LaravelNovaCsvImport\Http\Controllers\ImportController::class,
                \Capitalc\CustomCsvImport\Http\Controllers\QueuedImportController::class
            );

            // Override the binding after the original service provider has registered
            $this->app->when(\SimonHamp\LaravelNovaCsvImport\Http\Controllers\ImportController::class)
                ->needs(\Maatwebsite\Excel\Concerns\ToModel::class)
                ->give(function () {
                    return $this->app->make(CustomImporter::class);
                });

            $this->app->when(\SimonHamp\LaravelNovaCsvImport\Http\Controllers\UploadController::class)
                ->needs(\Maatwebsite\Excel\Concerns\ToModel::class)
                ->give(function () {
                    return $this->app->make(CustomImporter::class);
                });

            // Also bind our QueuedImportController to use CustomImporter
            $this->app->when(\Capitalc\CustomCsvImport\Http\Controllers\QueuedImportController::class)
                ->needs(\Maatwebsite\Excel\Concerns\ToModel::class)
                ->give(function () {
                    return $this->app->make(CustomImporter::class);
                });
        });

        // Register JavaScript assets
        Nova::serving(function (ServingNova $event) {
            Nova::script('custom-csv-import', __DIR__.'/../dist/js/tool.js');
        });
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // Register our CustomImporter in the container
        $this->app->singleton(CustomImporter::class, function ($app) {
            return new CustomImporter();
        });
    }

    /**
     * Register custom routes to override the base package routes
     */
    protected function registerCustomRoutes()
    {
        if ($this->app->routesAreCached()) {
            return;
        }

        // Register our routes with higher priority by registering them early
        Route::middleware(['nova', Authorize::class])
            ->namespace('Capitalc\\CustomCsvImport\\Http\\Controllers')
            ->prefix('nova-vendor/laravel-nova-csv-import')
            ->group(function () {
                // Override the import route to use our queued controller
                Route::post('/import', 'QueuedImportController@import')->name('nova.vendor.csv-import.import');

                // Override the results route to handle real-time updates
                Route::get('/results/{file}', 'QueuedImportController@results')->name('nova.vendor.csv-import.results');
            });
    }
}
