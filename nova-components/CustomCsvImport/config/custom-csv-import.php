<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Custom Importer Class
    |--------------------------------------------------------------------------
    |
    | This is the custom importer class that extends the base functionality
    | with upsert capabilities and resource restrictions.
    |
    */
    'importer' => Capitalc\CustomCsvImport\CustomImporter::class,

    /*
    |--------------------------------------------------------------------------
    | Allowed Resources for Import
    |--------------------------------------------------------------------------
    |
    | Define which Nova resources are allowed for CSV import operations.
    | Only these specified resources will be available in the import interface.
    | If empty, all resources will be available (default behavior).
    |
    */
    'allowed_resources' => [
        'App\Nova\Category',
        'App\Nova\Creator', 
        'App\Nova\Discount',
        'App\Nova\Filter',
        'App\Nova\Label',
        'App\Nova\Product',
        'App\Nova\Vendor',
    ],

    /*
    |--------------------------------------------------------------------------
    | Storage Disk
    |--------------------------------------------------------------------------
    |
    | The disk where CSV files will be stored during import process.
    | If null, the default filesystem disk will be used.
    |
    */
    'disk' => null,
];
