<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Card API Routes
|--------------------------------------------------------------------------
|
| Here is where you may register API routes for your card. These routes
| are loaded by the ServiceProvider of your card. You're free to add
| as many additional routes to this file as your card may require.
|
*/

Route::get('{input}', function (Request $request, $input) {
    $client = new \GuzzleHttp\Client;
   
    $response = $client->get("https://api5.firstchoicepos.com/v1/Voucher/Get/{$input}", [
        'headers' => ['Authorization' => 'Basic '.env('POS')],
    ]);

    $results = $response->getBody()->getContents();
    $lines = '';
    $array = collect(forceArray($results));
    
    if ($array->isEmpty()) {
        return 'Not Found';
    }
    foreach ($array as $key => $value) {
        $lines .= "$key: $value<br/>";
    }
    return $lines;
});
