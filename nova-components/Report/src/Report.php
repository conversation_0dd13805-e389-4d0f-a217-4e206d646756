<?php

namespace Capitalc\Report;

use Illuminate\Http\Request;
use Laravel\Nova\Nova;
use Lara<PERSON>\Nova\Tool;

class Report extends Tool
{
    /**
     * Perform any tasks that need to happen when the tool is booted.
     *
     * @return void
     */
    public function boot()
    {
        if (app()->isLocal() && file_exists(__DIR__.'/../dist/hot')) {
            Nova::script('report', 'http://localhost:8080/js/tool.js');
        } else {
            Nova::script('report', __DIR__ . '/../dist/js/tool.js');
        }
        // Nova::style('report', __DIR__.'/../dist/css/tool.css');
    }

    /**
     * Build the view that renders the navigation links for the tool.
     *
     * @return \Illuminate\View\View
     */
    public function renderNavigation()
    {
        return view('report::navigation');
    }

    public function menu(Request $request)
    {

    }
}
