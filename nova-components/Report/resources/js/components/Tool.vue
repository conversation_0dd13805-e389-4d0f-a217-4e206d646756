<template>
    <div>
        <heading class="mb-6">Report</heading>

        <form @change="formChange" @submit.prevent="downloadReport">
            <card class="grid gap-6 p-6">
                <form-select-field
                    ref="report_option"
                    :field="{
                            indexName:'Report Option',
                            attribute:'report_option',
                            component:'select_field',
                            options:this.reportOptions,
                            name:'Report Option',
                            visible: true,
                            value: this.report_option
                        }"
                />
                <form-select-field
                    :field="{
                            indexName:'From',
                            attribute:'from',
                            component:'select_field',
                            name:'From',
                            visible: true,
                            options:[{'label':'Orders','value': 'App\\Order'},{'label':'Customers','value':'App\\Customer'}],
                        }"
                    v-if="report_option=='utm_report'"
                />
                <form-select-field
                    :field="{
                            indexName:'Date Range',
                            attribute:'date_range',
                            component:'select_field',
                            options:this.rangeOptions,
                            visible: true,
                            name:'Date Range'
                        }"
                    v-if="report_option !='inventory_report' && report_option !='customers_report' &&
                              report_option != 'categories_report' && report_option != 'creators_report' && report_option != 'products_report'
                               && report_option != 'vendors_report'"
                />
                <form-date
                    :field="{
                            indexName:'Start Date',
                            attribute: 'start_date',
                            id:'start_date',
                            component: 'date',
                            name: 'Start Date',
                            visible: true,
                            placeholder:dateSlice(this.placeholder_start.toISOString('YYYY-MM-DD'))
                        }"
                    v-if="date_range=='custom'"
                />
                <form-date
                    :field="{
                            indexName:'End Date',
                            attribute: 'end_date',
                            component: 'date',
                            name: 'End Date',
                            visible: true,
                            placeholder:dateSlice(this.placeholder_end.toISOString('YYYY-MM-DD'))
                        }"
                    v-if="date_range=='custom'"
                />
                <form-select-field
                    :field="{
                            indexName:'Group By Date',
                            attribute:'date_group',
                            component:'select_field',
                            name:'Group By Date',
                            visible: true,
                            options:this.dateGroups
                        }"
                    v-if="report_option == 'sales_by_date' || report_option == 'gift_cards_transactions'"
                />
                <form-select-field
                    :field="{
                            indexName:'Group By Location',
                            attribute:'location_group',
                            component:'select_field',
                            name:'Group By Location',
                            visible: true,
                            options:this.locationGroups
                        }"
                    v-if="report_option=='sales_by_location'"
                />
                <form-select-field
                    :field="{
                            indexName:'Type',
                            attribute:'type',
                            component:'select_field',
                            name:'Type',
                            visible: true,
                            options:[{'label':'Products','value': 'Products'},{'label':'Variations','value':'Variations'},{'label':'Both','value':'Products and Variations'}],
                        }"
                    v-if="report_option=='inventory_report'"
                />

                <form-select-field
                    :field="{
                            indexName:'Active',
                            attribute:'active',
                            component:'select_field',
                            name:'Active',
                            visible: true,
                            options:[{'label':'Active','value': 1},{'label':'Not active','value':0},{'label':'Both','value':null}],
                            nullable:true
                        }"
                    v-if="report_option=='inventory_report' || report_option == 'products_report'"
                />
                <form-creators
                    ref="categories"
                    :field="{
                            indexName:'Categories',
                            attribute:'categories',
                            component:'creators',
                            name:'Categories',
                            visible: true,
                            nullable:true,
                            extraAttributes:{
                                hide:false,
                                delay:false,
                                label:'Categories',
                                availableResources:this.categories
                            },
                        }"
                    v-if="report_option=='inventory_report' || report_option == 'products_report'"
                />

                <form-creators
                    ref="vendors"
                    :field="{
                            indexName:'Vendors',
                            attribute:'vendors',
                            component:'creators',
                            name:'Vendors',
                            visible: true,
                            nullable:true,
                            extraAttributes:{
                                hide:false,
                                delay:true,
                                label:'Vendors',
                            },
                        }"
                    v-if="report_option=='inventory_report' || report_option == 'products_report'"
                />

                <form-creators
                    ref="digital_vendors"
                    :field="{
                            indexName:'Vendors',
                            attribute:'digital_vendors',
                            component:'creators',
                            name:'Vendors',
                            nullable:true,
                            visible: true,
                            extraAttributes:{
                                hide:false,
                                delay:false,
                                label:'Vendors',
                                availableResources:this.digital_vendors
                            },
                        }"
                    v-if="report_option=='digital_sales_by_vendor'"
                />


                <form-creators
                    ref="all_vendors"
                    :field="{
                            indexName:'Vendors',
                            attribute:'all_vendors',
                            component:'creators',
                            name:'Vendors',
                            nullable:true,
                            visible: true,
                            extraAttributes:{
                                hide:false,
                                delay:true,
                                label:'Vendors',
                            },
                        }"
                    v-if="report_option=='sales_by_product_by_vendor'"
                />


                <form-creators
                    ref="all_creators"
                    :field="{
                            indexName:'Creators',
                            attribute:'all_creators',
                            component:'creators',
                            name:'Creators',
                            nullable:true,
                            visible: true,
                            extraAttributes:{
                                hide:false,
                                delay:true,
                                label:'Creators',
                            },
                        }"
                    v-if="report_option=='sales_by_product_by_creator'"
                />


                <div class="bg-30 flex px-8 py-4">
                    <default-button
                        class="btn btn-default btn-primary ml-auto mr-3"
                        :disabled="!this.form_valid"
                    >Start Export
                    </default-button>
                </div>
            </card>
            <div class="bg-30 flex px-8 py-4">
                <outline-button
                    style="cursor:pointer;"
                    @click="goToExports"
                    class="ml-auto mr-3 dim"
                >Go to all Exports</outline-button>
            </div>
        </form>
    </div>
</template>

<script>
import axios from 'axios';

export default {
    data() {
        return {
            reportOptions: [],
            dateFilters: [],
            locationFilters: [],
            rangeOptions: [],
            report_option: '',
            start_date: '',
            end_date: '',
            placeholder_start: this.getStartDate(),
            placeholder_end: new Date(),
            custom: false,
            date_group: '',
            location_group: '',
            date_range: '',
            form_valid: false,
            category: '',
            vendor: '',
            active: '',
            type: '',
            from: '',
        };
    },
    mounted() {
        this.reportOptions = [
            {label: 'Sales By Date', value: 'sales_by_date'},
            {label: 'Digital Sales By Vendor', value: 'digital_sales_by_vendor'},
            {label: 'Sales By Product By Vendor', value: 'sales_by_product_by_vendor'},
            {label: 'Sales By Product By Creator', value: 'sales_by_product_by_creator'},
            {label: 'Sales By Product', value: 'sales_by_product'},
            {label: 'Sales By Variation', value: 'sales_by_variation'},
            {label: 'Sales By Discount', value: 'sales_by_discount'},
            {label: 'Sales By Customer', value: 'sales_by_customer'},
            {label: 'Sales By Location', value: 'sales_by_location'},
            {label: 'Shipping By Order', value: 'shipping_by_order'},
            {label: 'Gift Cards Balance', value: 'gift_cards_balance'},
            {label: 'Gift Cards Transactions', value: 'gift_cards_transactions'},
            {label: 'Shipping Accuracy', value: 'shipping_accuracy'},
            {label: 'Inventory Report', value: 'inventory_report'},
            {label: 'Orders Report', value: 'orders_report'},
            {label: 'Customers Report', value: 'customers_report'},
            {label: 'Creators Report', value: 'creators_report'},
            {label: 'Categories Report', value: 'categories_report'},
            {label: 'Products Report', value: 'products_report'},
            {label: 'Vendor Report', value: 'vendors_report'},
            {label: 'Returns Report', value: 'returns_report'},
            {label: 'Returns By Product', value: 'returns_by_product'},
            {label: 'Utm Report', value: 'utm_report'}
        ];
        this.dateGroups = [
            {label: 'Date', value: 'date'},
            {label: 'Week', value: 'week'},
            {label: 'Month', value: 'month'},
            {label: 'Year', value: 'year'},
            {label: 'All', value: 'all'}
        ];
        this.locationGroups = [
            {label: 'Country', value: 'country'},
            {label: 'State (US)', value: 'state'},
            {label: 'City', value: 'city'},
            {label: 'Postal Code', value: 'postal_code'},
            {label: 'Shipping Zone', value: 'shipping_zone'},
            {label: 'All Locations', value: 'all'}
        ];
        this.rangeOptions = [
            {label: '1 Week', value: 'week'},
            {label: '4 Weeks', value: 'weeks'},
            {label: '1 Year', value: 'year'},
            {label: 'Month to Date', value: 'month_date'},
            {label: 'Quarter to Date', value: 'quarter_date'},
            {label: 'Year to Date', value: 'year_date'},
            {label: 'All', value: 'all'},
            {label: 'Custom', value: 'custom'}
        ];
        axios.post(`/nova-custom-api/categories_options`).then(resp => {
            this.categories = resp.data;
        });
        axios.post(`/nova-custom-api/digital_vendors_options`).then(resp => {
            this.digital_vendors = resp.data;
        });
    },
    methods: {
        formChange(e) {
            if (e.target.id === 'report_option') {
                this.report_option = e.target.value;
            }
            if (e.target.id === 'active') {
                this.active = e.target.value;
            }
            if (e.target.id === 'from') {
                this.from = e.target.value;
            }
            if (e.target.id === 'type') {
                this.type = e.target.value;
            }
            if (e.target.id === 'date_range') {
                this.date_range = e.target.value;
                if (this.date_range != 'custom' && this.date_range != 'all') {
                    // this.start_date=this.calculateDays(this.date_range);
                    this.calculateDays(this.date_range);
                } else if (this.date_range === 'custom') {
                    this.custom = true;
                    this.end_date = this.placeholder_end;
                    this.start_date = this.placeholder_start;
                } else if (this.date_range === 'all') {
                    // this.end_date = null
                    // this.start_date = null
                }
            }
            if (e.target.name === 'Start Date') {
                this.start_date = e.target.value;
            }
            if (e.target.name === 'End Date') {
                this.end_date = e.target.value;
            }
            if (e.target.id === 'date_group') {
                this.date_group = e.target.value;
            }
            if (e.target.id === 'location_group') {
                this.location_group = e.target.value;
            }
            if (this.checkAllFields()) {
                if (!this.form_valid) {
                    this.form_valid = true;
                }
            } else if (this.form_valid) {
                this.form_valid = false;
            }

        },
        downloadReport() {
            console.log('Downloading report');
            if (this.report_option == 'inventory_report') {
                this.form_valid = false;
                axios
                    .post(`/api/reports/inventory_report`, {
                        active: this.active,
                        categories: this.$refs.categories.value,
                        vendors: this.$refs.vendors.value,
                        type: this.type
                    })
                    .then(resp => {
                        this.$refs.report_option.value = [];
                        this.report_option = '';
                        this.form_valid = false;
                        Nova.success( "Export started! You can find it when it's done by Exports");
                    })
                    .catch(err => {
                        Nova.error('Something went wrong');
                        console.log(err);
                    });
                return;
            }
            var start = this.start_date;
            var end = this.end_date;
            if (typeof start == 'object') {
                start = this.dateSlice(start.toISOString('YYYY-MM-DD'));
            }
            if (typeof end == 'object') {
                end = this.dateSlice(end.toISOString('YYYY-MM-DD'));
            }

            this.form_valid = false;
            axios
                .post(`/api/reports`, {
                    start: this.date_range == 'all' ? null : start,
                    end: end,
                    name: this.report_option,
                    groupBy: this.date_group || this.location_group,
                    active: this.active,
                    from: this.from,
                    categories: _.get(this.$refs.categories, 'value'),
                    vendors: _.get(this.$refs.vendors, 'value'),
                    all_vendors: _.get(this.$refs.all_vendors, 'value'),
                    all_creators: _.get(this.$refs.all_creators, 'value'),
                    digital_vendors: _.get(this.$refs.digital_vendors, 'value')
                })
                .then(resp => {
                    this.$refs.report_option.value = [];
                    this.report_option = '';
                    this.form_valid = false;
                    Nova.success( "Export started! When done you can find it by Exports");
                })
                .catch(err => {
                    Nova.error('Something went wrong');
                    console.log(err);
                });
        },
        calculateDays(range) {
            this.start_date = new Date();
            this.end_date = new Date();
            switch (range) {
                case 'week':
                    this.start_date.setDate(this.start_date.getDate() - 7);
                    break;
                case 'weeks':
                    this.start_date.setDate(this.start_date.getDate() - 28);
                    break;
                case 'year':
                    this.start_date.setFullYear(this.start_date.getFullYear() - 1);
                    break;
                case 'month_date':
                    this.start_date.setDate(1);
                    break;
                case 'quarter_date':
                    var current_month = this.start_date.getMonth();
                    this.start_date.setMonth(current_month - (current_month % 3));
                    this.start_date.setDate(1);
                    break;
                case 'year_date':
                    this.start_date.setMonth(0);
                    this.start_date.setDate(1);
            }
        },
        getStartDate() {
            var date = new Date();
            date.setDate(date.getDate() - 30);
            return date;
        },
        dateSlice(dateString) {
            return dateString.slice(0, dateString.indexOf('T'));
        },
        checkAllFields() {
            var report_option = this.report_option;
            if (report_option) {
                if (report_option === 'inventory_report' && this.type) {
                    return true;
                }
                if (
                    report_option === 'customers_report' ||
                    report_option === 'orders_report' ||
                    report_option === 'categories_report' ||
                    report_option === 'creators_report' ||
                    report_option === 'products_report' ||
                    report_option === 'vendors_report'
                ) {
                    return true;
                }
                if (
                    (report_option === 'sales_by_date' ||
                        report_option === 'gift_cards_transactions') &&
                    !this.date_group
                ) {
                    return false;
                }
                if (report_option === 'sales_by_location' && !this.location_group) {
                    return false;
                }
                if (report_option === 'utm_report' && !this.from) {
                    return false;
                }
                if (!this.date_range) {
                    return false;
                }
                return true;
            }
            return false;
        },
        goToExports() {
            window.location.href = '/admin/resources/exports';
        }
    }
};
</script>

<style>
/* Scoped Styles */
button:disabled{
    opacity: 0.5;
    cursor: not-allowed;
}
</style>
