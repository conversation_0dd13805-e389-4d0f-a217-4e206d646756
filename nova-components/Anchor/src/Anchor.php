<?php

namespace Capitalc\Anchor;

use Laravel\Nova\Fields\Field;

class Anchor extends Field
{
    public $component = 'anchor';

    public function __construct($name)
    {
        parent::__construct($name, 'anchor');

        $this->withMeta([
            'anchorId' => $name,
        ]);
        $this->onlyOnForms();
        $this->fillUsing(fn () => null);
        $this->resolve(fn () => null);
    }
}
