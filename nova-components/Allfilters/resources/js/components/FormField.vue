<template>
    <DefaultField
        :field="field"
        :errors="errors"
        :show-help-text="showHelpText"
        :full-width-content="fullWidthContent"
    >
        <template #field>
            <treeselect
                v-model="value"
                :multiple="true"
                :options="filters"
                :flat="true"
                sort-value-by="LEVEL"
                :default-expand-level="1"
                :disableBranchNodes="true"
                search-nested
                @select="resetSearchQuery"
            >
            </treeselect>
        </template>
    </DefaultField>
</template>

<script>
import { FormField, HandlesValidationErrors } from 'laravel-nova'
import axios from 'axios'
import Treeselect from "@zanmato/vue3-treeselect";
import "@zanmato/vue3-treeselect/dist/vue3-treeselect.min.css";

export default {
    mixins: [FormField, HandlesValidationErrors],

    props: ['resourceName', 'resourceId', 'field'],

    components: { Treeselect },

    data() {
        return {
            filters: [],
            value: null,
        }
    },

    mounted() {
        this.updateFilters();
    },
    methods: {
        updateFilters() {
            axios.get(`/nova-custom-api/categories/allfilteroptions`)
                .then( ({data}) => {
                    this.filters = data
                })

        },
        setInitialValue() {
            this.value = this.field.value && typeof this.field.value === 'string' ? this.field.value.split(',') : [];
        },
        fill(formData) {
            formData.append(this.fieldAttribute, this.value || '')
        },
    },
}
</script>
