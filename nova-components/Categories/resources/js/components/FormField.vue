<template>
    <DefaultField
        :field="Object.assign(field, { singularLabel:'Categories' })"
        :errors="errors"
        :show-help-text="showHelpText"
        :full-width-content="fullWidthContent"
    >
        <template #field>
            <treeselect
                v-model="value"
                :multiple="true"
                :options="options"
                :flat="true"
                sort-value-by="LEVEL"
                :disableBranchNodes="true"
                search-nested
                @select="handleChange"
                @deselect="deselect"
                ref="tree"
            >
            <div slot="value-label" slot-scope="{ node }">{{ node.raw.name }}</div>
            <div slot="option-label" slot-scope="{ node }">{{ node.raw.name }}</div>
            </treeselect>
        </template>
    </DefaultField>
</template>

<script>

import { FormField, HandlesValidationErrors } from 'laravel-nova'
import axios from 'axios'
import Treeselect from "@zanmato/vue3-treeselect";
import "@zanmato/vue3-treeselect/dist/vue3-treeselect.min.css";

export default {
    mixins: [FormField, HandlesValidationErrors],

    props: ['resourceName', 'resourceId', 'field'],

    components: { Treeselect },

    data() {
        return {
            options: null,
            value: null,
            fieldName: 'Categories',
        }
    },
    mounted() {
        axios.get('/nova-custom-api/category_tree')
            .then( ({data}) => {
                this.options = data
                if (this.value.length) {
                    this.handleChange()
                }
            })
    },
    methods: {
        setInitialValue() {
            this.value = Array.isArray(this.field.value)
                ? this.field.value
                : (typeof this.field.value === 'string' && this.field.value.trim()
                    ? this.field.value.split(',').map(Number)
                    : []);
            if (this.value.length) {
                this.handleChange()
            }
        },
        fill(formData) {
            formData.append(this.field.attribute, this.value || '')
        },

        handleChange() {
            if(this.$refs['tree']) {
                this.$refs['tree'].resetSearchQuery()
            }
            if (this.field) {
                this.$nextTick(() => {
                    this.emitFieldValueChange(this.fieldAttribute, this.value)
                })
            }
        },
        deselect(node) {
            this.value = _.filter(this.value, item => item != node.id)
            this.handleChange()
        }
    },
}
</script>
