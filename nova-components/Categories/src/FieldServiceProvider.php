<?php

namespace Capitalc\Categories;

use Laravel\Nova\Nova;
use Laravel\Nova\Events\ServingNova;
use Illuminate\Support\ServiceProvider;

class FieldServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        Nova::serving(function (ServingNova $event) {
            Nova::script('categories', __DIR__.'/../dist/js/field.js');
            Nova::style('categories', __DIR__.'/../dist/css/field.css');
        });
    }

    public function register()
    {
        //
    }
}
