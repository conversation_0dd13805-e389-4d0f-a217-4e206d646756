<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\FulfillmentSchematic;
use App\ShippingOption;
use App\ClosedDay;
use App\ZipCode;
use Carbon\Carbon;

class DeliveryTimeReport
{
    protected $shippingController;
    protected $targetShippingOption;
    protected $testLocation;
    protected $mockParams;

    public function __construct($shippingOptionName = 'Local Delivery', $zipCode = '11219')
    {
        $this->shippingController = new \App\Http\Controllers\Api\ShippingController();
        $this->targetShippingOption = $shippingOptionName;

        // Set up test location - using a zip code that has the target shipping option
        $this->testLocation = [
            'postal_code' => $zipCode,
            'country' => 'US',
            'state' => 'NY',
            'city' => 'Brooklyn'
        ];

        // Set up mock parameters for getOptions method
        $this->mockParams = [
            'billable_weight' => 1.0,
            'total' => 100.00,
            'excluded_billable_weight' => null,
            'location' => $this->testLocation,
            'discount' => null,
            'personalDuration' => 0
        ];

        // Verify the zip code has shipping options
        try {
            $testOptions = $this->getShippingOptionsForTime(Carbon::now());
            if ($testOptions->isEmpty()) {
                throw new Exception("No shipping options found for zip code {$zipCode}");
            }

            $hasTargetOption = $testOptions->contains(function ($option) {
                return $option['name'] === $this->targetShippingOption;
            });

            if (!$hasTargetOption) {
                throw new Exception("Shipping option '{$this->targetShippingOption}' not found for zip code {$zipCode}");
            }
        } catch (Exception $e) {
            throw new Exception("Failed to initialize delivery report: " . $e->getMessage());
        }
    }
    
    protected function getShippingOptionsForTime($orderTime)
    {
        // Temporarily set the current time to the order time for calculation
        $originalTime = Carbon::now();
        Carbon::setTestNow($orderTime);

        try {
            $options = $this->shippingController->getOptions(
                $this->mockParams['billable_weight'],
                $this->mockParams['total'],
                $this->mockParams['excluded_billable_weight'],
                $this->mockParams['location'],
                $this->mockParams['discount'],
                $this->mockParams['personalDuration']
            );

            return $options;
        } catch (Exception $e) {
            // Return empty collection if there's an error
            return collect([]);
        } finally {
            // Reset the time
            Carbon::setTestNow($originalTime);
        }
    }

    public function generateReport()
    {
        $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        $report = [];

        // Set a base date for the current week
        $baseDate = Carbon::now()->startOfWeek();

        foreach ($days as $dayIndex => $day) {
            $currentDate = $baseDate->copy()->addDays($dayIndex);

            for ($hour = 0; $hour < 24; $hour++) {
                $orderTime = $currentDate->copy()->hour($hour)->minute(0)->second(0);

                // Format for display (12-hour format)
                $hourFormatted = $orderTime->format('ga');

                // Get shipping options using the actual controller method
                $shippingOptions = $this->getShippingOptionsForTime($orderTime);

                // Find our target shipping option
                $targetOption = $shippingOptions->firstWhere('name', $this->targetShippingOption);

                if ($targetOption) {
                    $deliveryDate = Carbon::parse($targetOption['estimated_arrival']);
                    $daysToDelivery = $orderTime->diffInDays($deliveryDate);

                    // Determine status based on delivery time patterns
                    $isOffHours = $this->determineOffHoursStatus($orderTime, $deliveryDate, $daysToDelivery);
                    $isAfterCutoff = $this->determineAfterCutoffStatus($orderTime, $deliveryDate, $daysToDelivery);
                    $isClosedDay = $this->determineClosedDayStatus($orderTime, $deliveryDate);

                    $report[] = [
                        'order_day' => $orderTime->format('l'),
                        'order_hour' => $hourFormatted,
                        'delivery_date' => $deliveryDate->format('l, F j, Y'),
                        'days_to_delivery' => $daysToDelivery,
                        'hours_to_delivery' => $targetOption['hours'] ?? 0,
                        'is_off_hours' => $isOffHours,
                        'is_after_cutoff' => $isAfterCutoff,
                        'is_closed_day' => $isClosedDay,
                        'order_timestamp' => $orderTime->format('Y-m-d H:i:s'),
                        'delivery_timestamp' => $deliveryDate->format('Y-m-d H:i:s'),
                        'price' => $targetOption['price'] ?? 0,
                        'shipping_option_id' => $targetOption['id'] ?? null
                    ];
                } else {
                    // No shipping option available for this time
                    $report[] = [
                        'order_day' => $orderTime->format('l'),
                        'order_hour' => $hourFormatted,
                        'delivery_date' => 'No service available',
                        'days_to_delivery' => null,
                        'hours_to_delivery' => null,
                        'is_off_hours' => true,
                        'is_after_cutoff' => true,
                        'is_closed_day' => true,
                        'order_timestamp' => $orderTime->format('Y-m-d H:i:s'),
                        'delivery_timestamp' => null,
                        'price' => null,
                        'shipping_option_id' => null
                    ];
                }
            }
        }

        return $report;
    }
    
    protected function determineOffHoursStatus($orderTime, $deliveryDate, $daysToDelivery)
    {
        // If delivery is pushed to next day and it's not a same-day service, likely off-hours
        $hour = $orderTime->hour;
        return ($hour < 10 || $hour >= 22) && $daysToDelivery > 0;
    }

    protected function determineAfterCutoffStatus($orderTime, $deliveryDate, $daysToDelivery)
    {
        // If delivery is pushed to next day and it's during business hours, likely after cutoff
        $hour = $orderTime->hour;
        $dayOfWeek = $orderTime->dayOfWeek;

        // Friday has early cutoff (2 PM), others have later cutoff
        if ($dayOfWeek == 5) { // Friday
            return $hour >= 14 && $daysToDelivery > 0; // After 2 PM
        } else {
            return $hour >= 14 && $hour < 22 && $daysToDelivery > 0; // After 2 PM but before 10 PM
        }
    }

    protected function determineClosedDayStatus($orderTime, $deliveryDate)
    {
        // Check if it's a weekend or if delivery is significantly delayed
        $dayOfWeek = $orderTime->dayOfWeek;
        $isWeekend = ($dayOfWeek == 0 || $dayOfWeek == 6); // Sunday or Saturday

        // Also check if there's a closed day in the database
        $isClosedDay = ClosedDay::whereDate('date', $orderTime->format('Y-m-d'))->exists();

        return $isWeekend || $isClosedDay;
    }
    
    protected function getShippingOptionInfo()
    {
        // Get a sample shipping option to extract configuration info
        $sampleTime = Carbon::now()->hour(12); // Noon on current day
        $options = $this->getShippingOptionsForTime($sampleTime);
        $targetOption = $options->firstWhere('name', $this->targetShippingOption);

        if (!$targetOption) {
            return [
                'name' => $this->targetShippingOption,
                'visible_name' => 'Unknown',
                'operational_days' => 'Unknown',
                'duration' => 'Unknown',
                'price_info' => 'Unknown'
            ];
        }

        // Get the shipping option from database for more details
        $shippingOption = ShippingOption::where('internal_name', $this->targetShippingOption)->first();
        $fulfillmentSchematic = FulfillmentSchematic::where('delivery', true)->first();

        return [
            'name' => $targetOption['name'],
            'visible_name' => $shippingOption->visible_name ?? 'Unknown',
            'operational_days' => $shippingOption->operational_days ?? 'Unknown',
            'duration' => 'Calculated dynamically',
            'cutoff' => $fulfillmentSchematic->cutoff ?? 'Unknown',
            'cutoff_is_hourly' => $fulfillmentSchematic->cutoff_is_hourly ?? false,
            'price_info' => "Base: $" . ($targetOption['base_rate'] ?? 0) . ", Per lb: $" . ($targetOption['price_per_pound'] ?? 0),
            'free_shipping_above' => $targetOption['free_shipping_above'] ?? 'None'
        ];
    }
    
    public function printReport()
    {
        $report = $this->generateReport();
        $shippingInfo = $this->getShippingOptionInfo();

        echo "COMPREHENSIVE DELIVERY TIME REPORT (Using Production Logic)\n";
        echo "===========================================================\n";
        echo "Generated: " . date('Y-m-d H:i:s') . "\n";
        echo "Test Location: {$this->testLocation['postal_code']} ({$this->testLocation['city']}, {$this->testLocation['state']})\n";
        echo "Shipping Option: {$shippingInfo['visible_name']} (Internal: {$shippingInfo['name']})\n";
        echo "Operational Days: {$shippingInfo['operational_days']}\n";
        echo "Cutoff Time: {$shippingInfo['cutoff']} " . ($shippingInfo['cutoff_is_hourly'] ? 'hours' : 'minutes') . " before close\n";
        echo "Pricing: {$shippingInfo['price_info']}\n";
        echo "Free Shipping Above: $" . ($shippingInfo['free_shipping_above'] ?? 'None') . "\n";
        echo "Mock Order: Weight={$this->mockParams['billable_weight']}lb, Total=$" . $this->mockParams['total'] . "\n\n";

        $currentDay = '';
        foreach ($report as $entry) {
            if ($currentDay !== $entry['order_day']) {
                if ($currentDay !== '') echo "\n";
                echo "=== {$entry['order_day']} ===\n";
                $currentDay = $entry['order_day'];
            }

            $status = '';
            if ($entry['is_off_hours']) $status .= '[OFF-HOURS] ';
            if ($entry['is_after_cutoff']) $status .= '[AFTER-CUTOFF] ';
            if ($entry['is_closed_day']) $status .= '[CLOSED-DAY] ';

            if ($entry['delivery_date'] === 'No service available') {
                echo "{$entry['order_hour']} - {$entry['delivery_date']} {$status}\n";
            } else {
                $price = $entry['price'] !== null ? " (\${$entry['price']})" : '';
                echo "{$entry['order_hour']} - Delivery on {$entry['delivery_date']} ({$entry['days_to_delivery']} days){$price} {$status}\n";
            }
        }

        echo "\n=== SUMMARY ===\n";
        $this->printSummary($report);
    }

    protected function printSummary($report)
    {
        $validEntries = array_filter($report, function($entry) {
            return $entry['days_to_delivery'] !== null;
        });

        if (empty($validEntries)) {
            echo "No valid delivery options found.\n";
            return;
        }

        $deliveryDays = array_column($validEntries, 'days_to_delivery');
        $prices = array_filter(array_column($validEntries, 'price'), function($price) {
            return $price !== null;
        });

        $statusCounts = [
            'normal' => 0,
            'off_hours' => 0,
            'after_cutoff' => 0,
            'closed_day' => 0,
            'no_service' => 0
        ];

        foreach ($report as $entry) {
            if ($entry['delivery_date'] === 'No service available') {
                $statusCounts['no_service']++;
            } elseif ($entry['is_off_hours']) {
                $statusCounts['off_hours']++;
            } elseif ($entry['is_after_cutoff']) {
                $statusCounts['after_cutoff']++;
            } elseif ($entry['is_closed_day']) {
                $statusCounts['closed_day']++;
            } else {
                $statusCounts['normal']++;
            }
        }

        echo "Delivery Time Statistics:\n";
        echo "- Average delivery time: " . round(array_sum($deliveryDays) / count($deliveryDays), 2) . " days\n";
        echo "- Min delivery time: " . min($deliveryDays) . " days\n";
        echo "- Max delivery time: " . max($deliveryDays) . " days\n\n";

        if (!empty($prices)) {
            echo "Pricing Statistics:\n";
            echo "- Average price: $" . round(array_sum($prices) / count($prices), 2) . "\n";
            echo "- Min price: $" . min($prices) . "\n";
            echo "- Max price: $" . max($prices) . "\n\n";
        }

        echo "Order Status Distribution:\n";
        echo "- Normal processing: {$statusCounts['normal']} orders\n";
        echo "- Off-hours orders: {$statusCounts['off_hours']} orders\n";
        echo "- After-cutoff orders: {$statusCounts['after_cutoff']} orders\n";
        echo "- Closed-day orders: {$statusCounts['closed_day']} orders\n";
        echo "- No service available: {$statusCounts['no_service']} orders\n";

        $totalValid = count($validEntries);
        $totalOrders = count($report);
        echo "\nService Availability: {$totalValid}/{$totalOrders} time slots (" . round(($totalValid/$totalOrders)*100, 1) . "%)\n";
    }
}

// Generate and print the report for Local Delivery
echo "=== LOCAL DELIVERY REPORT ===\n";
$localDeliveryReporter = new DeliveryTimeReport('Local Delivery', '11219');
$localDeliveryReporter->printReport();

echo "\n\n" . str_repeat("=", 80) . "\n\n";

// Generate and print the report for Standard Shipping for comparison
echo "=== STANDARD SHIPPING REPORT (for comparison) ===\n";
try {
    // Try a different zip code that might have Standard Shipping
    $standardShippingReporter = new DeliveryTimeReport('Standard Shipping', '10001');
    $standardShippingReporter->printReport();
} catch (Exception $e) {
    echo "Could not generate Standard Shipping report for 10001: " . $e->getMessage() . "\n";
    try {
        // Try another zip code
        $standardShippingReporter = new DeliveryTimeReport('Standard Shipping', '90210');
        $standardShippingReporter->printReport();
    } catch (Exception $e2) {
        echo "Could not generate Standard Shipping report for 90210: " . $e2->getMessage() . "\n";
        echo "Standard Shipping may not be available for the tested zip codes.\n";
    }
}