# Eichlers — E-commerce with Laravel Nova & Vue Components

Eichlers is a robust e-commerce application built on Laravel 11, featuring an admin panel powered by Laravel Nova. The frontend leverages Vue 2.6.14 components, bundled efficiently with Vite. This repository includes Laravel Nova components and a Docker-based development environment for streamlined setup and management.

## 📋 Table of Contents

- [Getting Started](#getting-started)
- [Technology Stack](#technology-stack)
- [Setting Up the Development Environment](#setting-up-the-development-environment)
- [Building Nova Components](#building-nova-components)
- [NPM Scripts](#npm-scripts)

## 🚀 Getting Started

To begin working with <PERSON><PERSON><PERSON>, follow these steps to set up your development environment and build the Nova components.

### Prerequisites

Ensure you have the following installed:
- [Docker](https://www.docker.com/get-started) and [Docker Compose](https://docs.docker.com/compose/install/)
- [Node.js](https://nodejs.org/) and [npm](https://www.npmjs.com/) (if building outside Docker)
- Git

### Installation

1. **Copy Environment File**  
   Copy the example environment file and configure it as needed:
   ```bash
   cp .env.example .env
   ```

2. **Start the Docker Environment**  
   Launch the full development environment using Docker Compose:
   ```bash
   docker-compose up -d
   ```

3. **Install PHP Dependencies**  
   Run Composer to install Laravel dependencies inside the `server` container:
   ```bash
   docker exec -it eichlers_server composer install
   ```

4. **Generate Application Key**  
   Generate a new Laravel application key:
   ```bash
   docker exec -it eichlers_server php artisan key:generate
   ```

5. **Run Database Migrations**  
   Set up the database schema:
   ```bash
   docker exec -it eichlers_server php artisan migrate
   ```

### Access the Application

- **Application**: http://localhost
- **phpMyAdmin**: http://localhost:15000

## 🛠 Technology Stack

- **Backend**: Laravel 11 (PHP 8+)
- **Admin Panel**: Laravel Nova
- **Frontend Components**: Vue 2.6.14 with Vite
- **Database**: MySQL
- **Cache**: Redis
- **Database Management**: phpMyAdmin

## 🐳 Setting Up the Development Environment

The project includes a `docker-compose.yml` file to orchestrate the following services:

| Service       | Description                            | Ports        |
|---------------|----------------------------------------|--------------|
| `phpmyadmin`  | Web UI for MySQL management            | 15000:80     |
| `server`      | PHP / Laravel application server       | 80:8000      |
| `mysql`       | MySQL database server                  | Exposed 3306 |
| `cron`        | Laravel scheduler for cron jobs        | —            |
| `worker`      | Background queue worker                | 8080:8080    |
| `redis`       | Redis cache server                     | 6379:6379    |

### Starting the Environment

To start all services in the background:
```bash
docker-compose up -d
```

To stop the environment:
```bash
docker-compose down
```

## 📦 Building Nova Components

Laravel Nova components are located in the `nova-components` folder. A provided build script automates the process of building these components.

### Steps to Build Components

1. **Ensure Node.js and npm**  
   Verify that Node.js and npm are installed in the `server` container. If not, add them to your `Dockerfile`.

2. **Make the Build Script Executable**  
   ```bash
   chmod +x build-nova-components.sh
   ```

3. **Run the Build Script**  
   Execute the script inside the Docker container:
   ```bash
   docker exec -it eichlers_server bash -c "./build-nova-components.sh"
   ```

   Alternatively, open an interactive shell and run manually:
   ```bash
   docker exec -it eichlers_server bash
   ./build-nova-components.sh
   ```

The script will:
- Detect all components in `nova-components`
- Install npm dependencies if missing
- Run `npm run dev` for each component
- Display a summary of successful and failed builds

## ⚙️ NPM Scripts

- **`npm run dev`**  
  Launches the Vite development server with hot module replacement (HMR) for rapid development.

- **`npm run build`**  
  Generates an optimized, production-ready build of the Vue components.

- **`npm run format`**  
  Formats all `.js` and `.vue` files in `resources/js/` using Prettier, based on `prettier.config.cjs`.
