# Delivery Reports

This directory contains delivery and shipping analysis tools for the application.

## Available Reports

### delivery_time_report.php
Comprehensive delivery time analysis tool that tests delivery estimates across all possible order placement times.

**Usage:**
```bash
# Run from project root
php reports/delivery_time_report.php
```

**Features:**
- Tests all 7 days of the week, 24 hours per day (168 total scenarios)
- Uses production ShippingController logic via getOptions() method
- Analyzes Local Delivery and Standard Shipping options
- Provides simplified output format showing order time → delivery day
- Tests against standard operational weeks without closed day interference
- Generates comprehensive statistics and summaries

**Output Format:**
```
=== Sunday ===
10am - Delivery on Sunday
11am - Delivery on Sunday
12pm - Delivery on Sunday
1pm - Delivery on Sunday
2pm - Delivery on Monday
...
```

**Configuration:**
- Test Location: Brooklyn, NY (11219)
- Test Week: March 9-15, 2025 (standard operational week)
- Mock Order: 1lb weight, $100 total

## Future Reports

This directory structure allows for additional delivery-related reports:
- `shipping_analysis.php` - Shipping option performance analysis
- `delivery_performance.php` - Historical delivery performance metrics
- `cutoff_analysis.php` - Cutoff time optimization analysis
- `zone_coverage.php` - Shipping zone coverage analysis

## Development Notes

- All reports should use the Laravel bootstrap: `require_once __DIR__ . '/../bootstrap/app.php'`
- Vendor autoload path: `require_once __DIR__ . '/../vendor/autoload.php'`
- Reports should be runnable from the project root directory
- Follow the established pattern of comprehensive testing and clear output formatting
