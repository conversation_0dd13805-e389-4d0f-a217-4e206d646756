/**
 * Klaviyo Customer Identification Utility
 *
 * This module provides utilities for identifying customers in Klaviyo
 * for both authenticated users and guest checkout scenarios using window.klaviyo.identify()
 */

/**
 * Identify a customer in Klaviyo using window.klaviyo.identify()
 * @param {Object} customerData - Customer data object
 * @param {string} customerData.email - Customer email (required)
 * @param {string} [customerData.first_name] - Customer first name
 * @param {string} [customerData.last_name] - Customer last name
 * @param {string} [customerData.name] - Full customer name (will be split if first/last not provided)
 * @param {number} [customerData.customer_id] - Customer ID for authenticated users
 * @param {Object} [customerData.properties] - Additional custom properties
 * @returns {Promise} - Promise that resolves when identification is complete
 */
export function identifyCustomer(customerData) {
    return new Promise((resolve, reject) => {
        // Validate required data
        if (!customerData || !customerData.email) {
            console.error('Klaviyo identification failed: Email is required');
            reject(new Error('Email is required for Klaviyo identification'));
            return;
        }

        // Check if Klaviyo is available
        if (typeof window === 'undefined' || !window.klaviyo) {
            console.warn('Klaviyo is not available - identification skipped');
            resolve();
            return;
        }

        try {
            // Prepare the identification payload
            const identifyPayload = {
                email: customerData.email,
            };

            // Add name fields if available
            if (customerData.first_name) {
                identifyPayload.first_name = customerData.first_name;
            }
            if (customerData.last_name) {
                identifyPayload.last_name = customerData.last_name;
            }

            // If we have a full name but no first/last, try to split it
            if (customerData.name && !customerData.first_name && !customerData.last_name) {
                const nameParts = customerData.name.trim().split(' ');
                if (nameParts.length >= 1) {
                    identifyPayload.first_name = nameParts[0];
                }
                if (nameParts.length >= 2) {
                    identifyPayload.last_name = nameParts.slice(1).join(' ');
                }
            }

            // Add customer ID for authenticated users
            if (customerData.customer_id) {
                identifyPayload.customer_id = customerData.customer_id;
            }

            // Add any additional custom properties
            if (customerData.properties && typeof customerData.properties === 'object') {
                Object.assign(identifyPayload, customerData.properties);
            }

            // Call Klaviyo identify
            window.klaviyo.identify(identifyPayload);

            console.log('Klaviyo customer identified:', {
                email: identifyPayload.email,
                customer_id: identifyPayload.customer_id || 'guest',
                name: `${identifyPayload.first_name || ''} ${identifyPayload.last_name || ''}`.trim()
            });

            resolve();

        } catch (error) {
            console.error('Klaviyo identification error:', error);
            reject(error);
        }
    });
}

/**
 * Identify an authenticated user after login
 * @param {Object} user - User object from authentication response
 * @returns {Promise} - Promise that resolves when identification is complete
 */
export function identifyAuthenticatedUser(user) {
    if (!user) {
        return Promise.reject(new Error('User data is required'));
    }

    const customerData = {
        email: user.email,
        customer_id: user.id,
        first_name: user.first_name,
        last_name: user.last_name,
        name: user.name,
        properties: {
            user_type: 'authenticated',
            login_timestamp: new Date().toISOString(),
        }
    };

    return identifyCustomer(customerData);
}

/**
 * Identify a guest user during checkout
 * @param {Object} guestData - Guest user data
 * @param {string} guestData.email - Guest email
 * @param {string} [guestData.name] - Guest name from shipping/billing info
 * @param {string} [guestData.first_name] - Guest first name
 * @param {string} [guestData.last_name] - Guest last name
 * @param {Object} [additionalProperties] - Additional properties to track
 * @returns {Promise} - Promise that resolves when identification is complete
 */
export function identifyGuestUser(guestData, additionalProperties = {}) {
    if (!guestData || !guestData.email) {
        return Promise.reject(new Error('Guest email is required'));
    }

    const customerData = {
        email: guestData.email,
        first_name: guestData.first_name,
        last_name: guestData.last_name,
        name: guestData.name,
        properties: {
            user_type: 'guest',
            checkout_timestamp: new Date().toISOString(),
            ...additionalProperties
        }
    };

    return identifyCustomer(customerData);
}

/**
 * Safe wrapper for Klaviyo identification that won't throw errors
 * @param {Function} identifyFunction - The identify function to call
 * @param {...any} args - Arguments to pass to the identify function
 */
export function safeIdentify(identifyFunction, ...args) {
    try {
        return identifyFunction(...args).catch(error => {
            // Log error but don't propagate to avoid breaking user flow
            console.warn('Klaviyo identification failed silently:', error.message);
        });
    } catch (error) {
        console.warn('Klaviyo identification failed silently:', error.message);
        return Promise.resolve();
    }
}


