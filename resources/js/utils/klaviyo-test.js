/**
 * Simple test file to validate Klaviyo identification functionality
 * This can be run in the browser console to test the implementation
 */

import { identifyCustomer, identifyAuthenticatedUser, identifyGuestUser, safeIdentify } from './klaviyo.js';

// Mock window.klaviyo for testing
window.klaviyo = {
    identify: function(data) {
        console.log('Mock Klaviyo.identify called with:', data);
        return Promise.resolve();
    }
};

// Test authenticated user identification
export function testAuthenticatedUser() {
    console.log('Testing authenticated user identification...');
    
    const mockUser = {
        id: 123,
        email: '<EMAIL>',
        name: '<PERSON>',
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>'
    };

    return identifyAuthenticatedUser(mockUser)
        .then(() => {
            console.log('✅ Authenticated user identification test passed');
        })
        .catch((error) => {
            console.error('❌ Authenticated user identification test failed:', error);
        });
}

// Test guest user identification
export function testGuestUser() {
    console.log('Testing guest user identification...');
    
    const mockGuestData = {
        email: '<EMAIL>',
        name: '<PERSON>'
    };

    const additionalProperties = {
        order_type: 'shipping',
        checkout_method: 'guest'
    };

    return identifyGuestUser(mockGuestData, additionalProperties)
        .then(() => {
            console.log('✅ Guest user identification test passed');
        })
        .catch((error) => {
            console.error('❌ Guest user identification test failed:', error);
        });
}

// Test error handling
export function testErrorHandling() {
    console.log('Testing error handling...');
    
    // Test with missing email
    return identifyCustomer({})
        .then(() => {
            console.error('❌ Error handling test failed - should have thrown error');
        })
        .catch((error) => {
            console.log('✅ Error handling test passed - correctly caught error:', error.message);
        });
}

// Test safe identify wrapper
export function testSafeIdentify() {
    console.log('Testing safe identify wrapper...');
    
    const mockUser = {
        id: 456,
        email: '<EMAIL>',
        name: 'Safe User'
    };

    return safeIdentify(identifyAuthenticatedUser, mockUser)
        .then(() => {
            console.log('✅ Safe identify test passed');
        })
        .catch((error) => {
            console.error('❌ Safe identify test failed:', error);
        });
}

// Run all tests
export function runAllTests() {
    console.log('🧪 Running Klaviyo identification tests...');
    
    return Promise.all([
        testAuthenticatedUser(),
        testGuestUser(),
        testErrorHandling(),
        testSafeIdentify()
    ]).then(() => {
        console.log('🎉 All Klaviyo identification tests completed!');
    });
}

// Auto-run tests if this file is imported
if (typeof window !== 'undefined') {
    console.log('Klaviyo test utilities loaded. Run runAllTests() to test the implementation.');
}
